# -*- coding: utf-8 -*-
"""
任务识别主控制器
开发者: @ConceptualGod
版本: v1.0
创建时间: 2025-07-28
项目: 起凡自动化脚本

功能:
- 整合数据管理、OCR处理、任务匹配三个模块
- 实现完整的识别流程控制
- 提供统一的识别接口
- 管理识别状态和结果
- 支持批量识别和单次识别
"""

import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime

from .task_data_manager import TaskDataManager
from .ocr_processor import OCRProcessor
from .task_matcher import TaskMatcher

class TaskRecognitionController:
    """任务识别主控制器"""
    
    def __init__(self):
        """初始化主控制器"""
        self.logger = logging.getLogger(__name__)
        
        # 初始化三个核心模块
        self.data_manager = None
        self.ocr_processor = None
        self.task_matcher = None

        # 日志监控作为备用方案
        self.log_monitor = None
        self.use_log_backup = True  # 是否启用日志备用方案
        
        # 识别状态
        self.is_initialized = False
        self.last_recognition_time = None
        self.recognition_count = 0
        
        # 识别结果缓存
        self.last_recognition_result = None
        self.recognition_history = []
        
        # 性能统计
        self.performance_stats = {
            "total_recognitions": 0,
            "successful_recognitions": 0,
            "failed_recognitions": 0,
            "average_time": 0.0,
            "total_time": 0.0
        }

        # 日志回调
        self.log_callback = None

        self._log_info("任务识别主控制器创建完成 - By @ConceptualGod")

    def _log_info(self, message: str):
        """
        统一的日志记录方法，同时输出到命令行和GUI

        Args:
            message: 日志消息

        开发者: @ConceptualGod
        """
        # 记录到日志文件（命令行显示）
        self._log_info(message)

        # 如果有回调，也发送到GUI
        if self.log_callback:
            self.log_callback(message)

    def initialize(self) -> bool:
        """初始化所有模块"""
        try:
            self._log_info("开始初始化任务识别系统 - By @ConceptualGod")
            
            # 1. 初始化数据管理器
            self._log_info("初始化数据管理器 - By @ConceptualGod")
            self.data_manager = TaskDataManager()
            # 数据管理器在初始化时自动加载数据，检查是否加载成功
            if not self.data_manager.zhangong_data:
                self.logger.error("数据管理器初始化失败 - By @ConceptualGod")
                return False
            
            # 2. 初始化OCR处理器
            self._log_info("初始化OCR处理器 - By @ConceptualGod")
            self.ocr_processor = OCRProcessor()
            # OCR处理器在初始化时自动设置，检查关键组件是否就绪
            if not hasattr(self.ocr_processor, 'reader') or self.ocr_processor.reader is None:
                self.logger.error("OCR处理器初始化失败 - By @ConceptualGod")
                return False
            
            # 3. 初始化任务匹配器
            self._log_info("初始化任务匹配器 - By @ConceptualGod")
            self.task_matcher = TaskMatcher(self.data_manager)

            # 4. 初始化日志监控备用方案
            if self.use_log_backup:
                try:
                    from .simple_log_monitor import CompleteGameLogMonitor
                    self.log_monitor = CompleteGameLogMonitor("7fgame")
                    self._log_info("日志监控备用方案初始化完成 - By @ConceptualGod")
                except Exception as e:
                    self.logger.warning(f"日志监控备用方案初始化失败: {e} - By @ConceptualGod")
                    self.use_log_backup = False

            self.is_initialized = True
            self._log_info("任务识别系统初始化完成 - By @ConceptualGod")
            return True
            
        except Exception as e:
            self.logger.error(f"系统初始化失败: {e} - By @ConceptualGod")
            return False
    
    def recognize_tasks(self) -> Dict[str, Any]:
        """执行完整的任务识别流程"""
        if not self.is_initialized:
            return self._create_error_result("系统未初始化")
        
        start_time = time.time()
        recognition_id = f"recognition_{int(time.time())}_{self.recognition_count}"
        
        try:
            self._log_info(f"开始任务识别 (ID: {recognition_id}) - By @ConceptualGod")
            
            # 第一步：OCR识别任务文本
            self._log_info("步骤1: OCR识别任务文本 - By @ConceptualGod")
            ocr_tasks = self.ocr_processor.process_task_region()

            if not ocr_tasks:
                # OCR失败时尝试使用日志监控备用方案
                if self.use_log_backup and self.log_monitor:
                    self._log_info("OCR识别失败，尝试使用日志监控备用方案 - By @ConceptualGod")
                    return self._try_log_backup(recognition_id, start_time)
                else:
                    return self._create_error_result("OCR识别失败", recognition_id, start_time)

            self._log_info(f"OCR识别到 {len(ocr_tasks)} 个任务文本 - By @ConceptualGod")
            
            # 第二步：任务匹配
            self._log_info("步骤2: 任务匹配分析 - By @ConceptualGod")
            matched_tasks = self.task_matcher.match_tasks(ocr_tasks)

            self._log_info(f"成功匹配 {len(matched_tasks)} 个目标任务 - By @ConceptualGod")

            # 第三步：智能排序
            self._log_info("步骤3: 智能排序任务 - By @ConceptualGod")
            sorted_tasks = self._smart_sort_tasks(matched_tasks)

            # 第四步：生成识别结果
            self._log_info("步骤4: 生成识别结果 - By @ConceptualGod")
            recognition_result = self._create_success_result(
                recognition_id=recognition_id,
                ocr_tasks=ocr_tasks,
                matched_tasks=sorted_tasks,
                start_time=start_time
            )
            
            # 更新统计信息
            self._update_performance_stats(start_time, True)
            
            # 缓存结果
            self.last_recognition_result = recognition_result
            self.last_recognition_time = datetime.now()
            self.recognition_count += 1
            
            # 添加到历史记录
            self._add_to_history(recognition_result)
            
            self._log_info(f"任务识别完成 (ID: {recognition_id}) - By @ConceptualGod")
            return recognition_result
            
        except Exception as e:
            self.logger.error(f"任务识别失败: {e} - By @ConceptualGod")
            self._update_performance_stats(start_time, False)
            return self._create_error_result(f"识别过程异常: {e}", recognition_id, start_time)
    
    def get_target_tasks_summary(self) -> Dict[str, Any]:
        """获取目标任务摘要"""
        try:
            if not self.is_initialized:
                return {"error": "系统未初始化"}
            
            # 获取5种目标任务类型的数据
            summary = {
                "victory_tasks": self.data_manager.get_victory_tasks(),
                "complete_game_tasks": self.data_manager.get_complete_game_tasks(),
                "mvp_tasks": self.data_manager.get_mvp_tasks(),
                "assist_tasks": self.data_manager.get_assist_tasks(),
                "sacrifice_tasks": self.data_manager.get_sacrifice_tasks(),
                "total_target_tasks": 0
            }
            
            # 计算总数
            for task_type, tasks in summary.items():
                if task_type != "total_target_tasks" and isinstance(tasks, list):
                    summary["total_target_tasks"] += len(tasks)
            
            self._log_info(f"获取目标任务摘要完成，共 {summary['total_target_tasks']} 个目标任务 - By @ConceptualGod")
            return summary
            
        except Exception as e:
            self.logger.error(f"获取目标任务摘要失败: {e} - By @ConceptualGod")
            return {"error": f"获取摘要失败: {e}"}
    
    def get_last_recognition_result(self) -> Optional[Dict[str, Any]]:
        """获取最后一次识别结果"""
        return self.last_recognition_result
    
    def get_recognition_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取识别历史记录"""
        try:
            # 返回最近的识别记录
            return self.recognition_history[-limit:] if self.recognition_history else []
        except Exception as e:
            self.logger.error(f"获取识别历史失败: {e} - By @ConceptualGod")
            return []
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        try:
            stats = self.performance_stats.copy()
            
            # 计算成功率
            if stats["total_recognitions"] > 0:
                stats["success_rate"] = stats["successful_recognitions"] / stats["total_recognitions"]
                stats["failure_rate"] = stats["failed_recognitions"] / stats["total_recognitions"]
            else:
                stats["success_rate"] = 0.0
                stats["failure_rate"] = 0.0
            
            # 添加系统状态
            stats["is_initialized"] = self.is_initialized
            stats["last_recognition_time"] = self.last_recognition_time.isoformat() if self.last_recognition_time else None
            stats["recognition_count"] = self.recognition_count
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取性能统计失败: {e} - By @ConceptualGod")
            return {}
    
    def reset_statistics(self) -> bool:
        """重置统计信息"""
        try:
            self.performance_stats = {
                "total_recognitions": 0,
                "successful_recognitions": 0,
                "failed_recognitions": 0,
                "average_time": 0.0,
                "total_time": 0.0
            }
            
            self.recognition_history.clear()
            self.recognition_count = 0
            self.last_recognition_time = None
            self.last_recognition_result = None
            
            self._log_info("统计信息重置完成 - By @ConceptualGod")
            return True
            
        except Exception as e:
            self.logger.error(f"重置统计信息失败: {e} - By @ConceptualGod")
            return False
    
    def _create_success_result(self, recognition_id: str, ocr_tasks: List[Dict], 
                              matched_tasks: List[Dict], start_time: float) -> Dict[str, Any]:
        """创建成功的识别结果"""
        try:
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 获取匹配统计
            match_stats = self.task_matcher.get_match_statistics(matched_tasks)
            
            result = {
                # 基本信息
                "success": True,
                "recognition_id": recognition_id,
                "timestamp": datetime.now().isoformat(),
                "processing_time": round(processing_time, 3),
                
                # OCR结果
                "ocr_result": {
                    "total_tasks": len(ocr_tasks),
                    "tasks": ocr_tasks
                },
                
                # 匹配结果
                "match_result": {
                    "matched_tasks": matched_tasks,
                    "statistics": match_stats
                },
                
                # 摘要信息
                "summary": {
                    "total_ocr_tasks": len(ocr_tasks),
                    "total_matched_tasks": len(matched_tasks),
                    "match_rate": len(matched_tasks) / len(ocr_tasks) if ocr_tasks else 0,
                    "adaptable_tasks": sum(1 for task in matched_tasks if isinstance(task, dict) and task.get('is_adaptable', False)),
                    "processing_time": round(processing_time, 3)
                },
                
                # 元数据
                "metadata": {
                    "controller_version": "v1.0",
                    "recognition_count": self.recognition_count + 1
                }
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"创建成功结果失败: {e} - By @ConceptualGod")
            return self._create_error_result(f"结果生成失败: {e}", recognition_id, start_time)
    
    def _create_error_result(self, error_message: str, recognition_id: str = None, 
                           start_time: float = None) -> Dict[str, Any]:
        """创建错误的识别结果"""
        try:
            end_time = time.time()
            processing_time = (end_time - start_time) if start_time else 0
            
            result = {
                "success": False,
                "error": error_message,
                "recognition_id": recognition_id or f"error_{int(time.time())}",
                "timestamp": datetime.now().isoformat(),
                "processing_time": round(processing_time, 3),
                "metadata": {
                    "controller_version": "v1.0",
                    "recognition_count": self.recognition_count
                }
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"创建错误结果失败: {e} - By @ConceptualGod")
            return {
                "success": False,
                "error": f"严重错误: {e}",
                "timestamp": datetime.now().isoformat()
            }

    def _update_performance_stats(self, start_time: float, success: bool) -> None:
        """更新性能统计信息"""
        try:
            end_time = time.time()
            processing_time = end_time - start_time

            self.performance_stats["total_recognitions"] += 1
            self.performance_stats["total_time"] += processing_time

            if success:
                self.performance_stats["successful_recognitions"] += 1
            else:
                self.performance_stats["failed_recognitions"] += 1

            # 更新平均时间
            self.performance_stats["average_time"] = (
                self.performance_stats["total_time"] / self.performance_stats["total_recognitions"]
            )

        except Exception as e:
            self.logger.error(f"更新性能统计失败: {e} - By @ConceptualGod")

    def _add_to_history(self, recognition_result: Dict[str, Any]) -> None:
        """添加识别结果到历史记录"""
        try:
            # 只保留关键信息，避免历史记录过大
            history_item = {
                "recognition_id": recognition_result.get("recognition_id"),
                "timestamp": recognition_result.get("timestamp"),
                "success": recognition_result.get("success"),
                "processing_time": recognition_result.get("processing_time"),
                "summary": recognition_result.get("summary", {}),
                "error": recognition_result.get("error")
            }

            self.recognition_history.append(history_item)

            # 限制历史记录数量，避免内存占用过大
            max_history = 50
            if len(self.recognition_history) > max_history:
                self.recognition_history = self.recognition_history[-max_history:]

        except Exception as e:
            self.logger.error(f"添加历史记录失败: {e} - By @ConceptualGod")

    def cleanup(self) -> bool:
        """清理资源"""
        try:
            self._log_info("开始清理系统资源 - By @ConceptualGod")

            # 清理OCR处理器资源
            if self.ocr_processor:
                # OCR处理器目前没有需要特别清理的资源
                pass

            # 清理数据管理器资源
            if self.data_manager:
                # 数据管理器目前没有需要特别清理的资源
                pass

            # 清理任务匹配器资源
            if self.task_matcher:
                # 任务匹配器目前没有需要特别清理的资源
                pass

            # 重置状态
            self.is_initialized = False
            self.data_manager = None
            self.ocr_processor = None
            self.task_matcher = None

            self._log_info("系统资源清理完成 - By @ConceptualGod")
            return True

        except Exception as e:
            self.logger.error(f"清理系统资源失败: {e} - By @ConceptualGod")
            return False

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态信息"""
        try:
            status = {
                "controller": {
                    "initialized": self.is_initialized,
                    "recognition_count": self.recognition_count,
                    "last_recognition_time": self.last_recognition_time.isoformat() if self.last_recognition_time else None
                },
                "modules": {
                    "data_manager": self.data_manager is not None,
                    "ocr_processor": self.ocr_processor is not None,
                    "task_matcher": self.task_matcher is not None
                },
                "performance": self.get_performance_stats(),
                "memory": {
                    "history_count": len(self.recognition_history),
                    "last_result_cached": self.last_recognition_result is not None
                }
            }

            return status

        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e} - By @ConceptualGod")
            return {"error": f"获取状态失败: {e}"}

    def validate_system(self) -> Dict[str, Any]:
        """验证系统完整性"""
        try:
            validation_result = {
                "valid": True,
                "issues": [],
                "warnings": [],
                "details": {}
            }

            # 检查初始化状态
            if not self.is_initialized:
                validation_result["issues"].append("系统未初始化")
                validation_result["valid"] = False

            # 检查模块状态
            if not self.data_manager:
                validation_result["issues"].append("数据管理器未初始化")
                validation_result["valid"] = False
            else:
                # 检查数据管理器状态
                if not hasattr(self.data_manager, 'target_tasks') or not self.data_manager.target_tasks:
                    validation_result["issues"].append("目标任务数据未加载")
                    validation_result["valid"] = False

            if not self.ocr_processor:
                validation_result["issues"].append("OCR处理器未初始化")
                validation_result["valid"] = False
            else:
                # 检查OCR处理器状态
                if not hasattr(self.ocr_processor, 'ocr_reader') or not self.ocr_processor.ocr_reader:
                    validation_result["issues"].append("OCR引擎未初始化")
                    validation_result["valid"] = False

            if not self.task_matcher:
                validation_result["issues"].append("任务匹配器未初始化")
                validation_result["valid"] = False

            # 性能警告
            stats = self.get_performance_stats()
            if stats.get("failure_rate", 0) > 0.3:
                validation_result["warnings"].append(f"识别失败率较高: {stats.get('failure_rate', 0):.2%}")

            if stats.get("average_time", 0) > 10:
                validation_result["warnings"].append(f"平均识别时间较长: {stats.get('average_time', 0):.2f}秒")

            # 详细信息
            validation_result["details"] = {
                "initialization_status": self.is_initialized,
                "module_count": sum([
                    self.data_manager is not None,
                    self.ocr_processor is not None,
                    self.task_matcher is not None
                ]),
                "performance_stats": stats
            }

            if validation_result["valid"]:
                self._log_info("系统验证通过 - By @ConceptualGod")
            else:
                self.logger.warning(f"系统验证失败，发现 {len(validation_result['issues'])} 个问题 - By @ConceptualGod")

            return validation_result

        except Exception as e:
            self.logger.error(f"系统验证失败: {e} - By @ConceptualGod")
            return {
                "valid": False,
                "issues": [f"验证过程异常: {e}"],
                "warnings": [],
                "details": {}
            }

    def _smart_sort_tasks(self, tasks: List[Dict]) -> List[Dict]:
        """智能排序任务 - 优先级算法"""
        try:
            if not tasks:
                return tasks

            # 按英雄类型分组
            hero_groups = {}
            for task in tasks:
                hero_type = task.get("hero_type", "未知")
                if hero_type not in hero_groups:
                    hero_groups[hero_type] = []
                hero_groups[hero_type].append(task)

            self._log_info(f"任务分组: {list(hero_groups.keys())} - By @ConceptualGod")

            # 如果只有一个英雄类型，直接返回该类型的任务
            if len(hero_groups) == 1:
                hero_type = list(hero_groups.keys())[0]
                selected_tasks = hero_groups[hero_type]
                # 添加英雄推荐
                selected_tasks = self._add_hero_recommendations(selected_tasks)
                self._log_info(f"只有一个英雄类型 '{hero_type}'，选择该类型的所有任务 - By @ConceptualGod")
                return self._sort_by_priority(selected_tasks)

            # 多个英雄类型时的优先级排序
            priority_order = ["任意英雄", "中立英雄", "蜀国英雄", "魏国英雄"]
            sorted_tasks = []

            for hero_type in priority_order:
                if hero_type in hero_groups:
                    group_tasks = self._sort_by_priority(hero_groups[hero_type])
                    # 添加英雄推荐
                    group_tasks = self._add_hero_recommendations(group_tasks)
                    sorted_tasks.extend(group_tasks)
                    self._log_info(f"添加 '{hero_type}' 类型任务: {len(group_tasks)}个 - By @ConceptualGod")

            # 添加其他未分类的英雄类型
            for hero_type, group_tasks in hero_groups.items():
                if hero_type not in priority_order:
                    group_tasks = self._sort_by_priority(group_tasks)
                    # 添加英雄推荐
                    group_tasks = self._add_hero_recommendations(group_tasks)
                    sorted_tasks.extend(group_tasks)
                    self._log_info(f"添加其他类型 '{hero_type}' 任务: {len(group_tasks)}个 - By @ConceptualGod")

            self._log_info(f"智能排序完成，共 {len(sorted_tasks)} 个任务 - By @ConceptualGod")

            # 显示完整的任务排序结果并智能推荐英雄
            if sorted_tasks:
                self._log_info(f"=== 任务执行顺序（按优先级排序） === - By @ConceptualGod")
                self._log_info(f"任务执行顺序: - By @ConceptualGod")

                for i, task in enumerate(sorted_tasks, 1):
                    task_desc = task.get("task_desc", "")
                    task_type = task.get("task_type", "")
                    hero_type = task.get("hero_type", "")
                    recommended_hero = task.get("recommended_hero", "")

                    self._log_info(f"  {i}. [{task_type}] {task_desc} ({hero_type}) - 推荐: {recommended_hero} - By @ConceptualGod")

                # 智能分析最优英雄组合
                optimal_hero = self._analyze_optimal_hero_combination(sorted_tasks)
                self._log_info(f"=== 智能英雄推荐 === - By @ConceptualGod")
                self._log_info(f"最优英雄: {optimal_hero} - By @ConceptualGod")

                # 分析推荐原因
                self._explain_hero_recommendation(sorted_tasks, optimal_hero)

            return sorted_tasks

        except Exception as e:
            self.logger.error(f"智能排序失败: {e} - By @ConceptualGod")
            return tasks

    def _sort_by_priority(self, tasks: List[Dict]) -> List[Dict]:
        """按任务类型优先级排序"""
        try:
            # 任务类型优先级：简单任务优先
            task_priority = {
                "胜利类": 1,      # 最简单，只需要获得胜利
                "完整局类": 2,    # 需要完成完整游戏
                "MVP类": 3,       # 需要达到MVP值
                "助攻类": 4,      # 需要完成助攻
                "牺牲值类": 5     # 需要达到牺牲值
            }

            def get_priority(task):
                task_type = task.get("task_type", "未知")
                return task_priority.get(task_type, 999)

            sorted_tasks = sorted(tasks, key=get_priority)

            for i, task in enumerate(sorted_tasks):
                task_type = task.get("task_type", "未知")
                self._log_info(f"  排序{i+1}: [{task_type}] {task.get('task_desc', '')} - By @ConceptualGod")

            return sorted_tasks

        except Exception as e:
            self.logger.error(f"任务优先级排序失败: {e} - By @ConceptualGod")
            return tasks

    def _add_hero_recommendations(self, tasks: List[Dict]) -> List[Dict]:
        """为任务添加英雄推荐"""
        try:
            # 英雄推荐配置
            hero_recommendations = {
                "魏国英雄": ["曹操"],
                "蜀国英雄": ["华佗", "刘备"],
                "中立英雄": ["诸葛瑾", "陆逊", "孙权"],
                "任意英雄": ["华佗", "刘备", "诸葛瑾", "陆逊", "孙权", "曹操"]
            }

            for task in tasks:
                hero_type = task.get("hero_type", "未知")
                task_type = task.get("task_type", "")
                recommendations = hero_recommendations.get(hero_type, [])

                if recommendations:
                    # 智能选择一个英雄（考虑任务类型）
                    selected_hero = self._smart_select_hero(recommendations, hero_type, task_type)
                    task["recommended_hero"] = selected_hero
                    task["hero_options"] = recommendations  # 保留所有选项供参考

                    # 记录智能选择结果
                    if len(recommendations) == 1:
                        self._log_info(f"  智能选择英雄: {selected_hero} - By @ConceptualGod")
                    else:
                        self._log_info(f"  智能选择英雄: {selected_hero} (从{len(recommendations)}个候选中智能选择) - By @ConceptualGod")
                else:
                    task["recommended_hero"] = "未知英雄"
                    task["hero_options"] = []
                    self.logger.warning(f"  未找到 '{hero_type}' 的推荐英雄 - By @ConceptualGod")

            return tasks

        except Exception as e:
            self.logger.error(f"添加英雄推荐失败: {e} - By @ConceptualGod")
            return tasks

    def _smart_select_hero(self, hero_options: List[str], hero_type: str, task_type: str = "") -> str:
        """智能选择英雄 - 基于英雄特长和任务类型匹配"""
        try:
            if not hero_options:
                return "未知英雄"

            if len(hero_options) == 1:
                return hero_options[0]

            # 英雄特长分析
            hero_specialties = {
                "华佗": {"治疗": 10, "辅助": 9, "生存": 8, "输出": 6, "控制": 5},
                "刘备": {"领导": 10, "平衡": 9, "生存": 8, "输出": 7, "控制": 6},
                "诸葛瑾": {"策略": 10, "控制": 9, "辅助": 8, "输出": 7, "生存": 6},
                "陆逊": {"输出": 10, "机动": 9, "控制": 8, "生存": 7, "辅助": 5},
                "孙权": {"领导": 10, "平衡": 9, "输出": 8, "控制": 7, "生存": 8},
                "曹操": {"输出": 10, "控制": 9, "领导": 8, "生存": 7, "辅助": 5}
            }

            # 任务类型对应的能力需求
            task_requirements = {
                "胜利类": {"输出": 8, "生存": 7, "控制": 6},
                "完整局类": {"生存": 9, "平衡": 8, "辅助": 6},
                "MVP类": {"输出": 10, "控制": 8, "机动": 7},
                "助攻类": {"辅助": 9, "控制": 8, "策略": 7},
                "牺牲值类": {"生存": 10, "治疗": 8, "平衡": 7}
            }

            # 计算每个英雄的适配度
            best_hero = None
            best_score = -1

            for hero in hero_options:
                if hero not in hero_specialties:
                    continue

                hero_abilities = hero_specialties[hero]
                task_needs = task_requirements.get(task_type, {"平衡": 8})

                # 计算匹配分数
                score = 0
                for ability, need_level in task_needs.items():
                    hero_level = hero_abilities.get(ability, 5)  # 默认5分
                    score += hero_level * need_level / 10  # 归一化

                # 英雄类型加成
                if hero_type == "蜀国英雄" and hero in ["华佗", "刘备"]:
                    score += 2
                elif hero_type == "中立英雄" and hero in ["诸葛瑾", "陆逊", "孙权"]:
                    score += 2
                elif hero_type == "魏国英雄" and hero == "曹操":
                    score += 2
                elif hero_type == "任意英雄":
                    score += 1  # 任意英雄都有小幅加成

                if score > best_score:
                    best_score = score
                    best_hero = hero

            selected_hero = best_hero if best_hero else hero_options[0]

            # 记录智能选择的原因
            if task_type:
                self._log_info(f"    智能分析: {task_type}任务最适合{selected_hero}(适配度:{best_score:.1f}) - By @ConceptualGod")

            return selected_hero

        except Exception as e:
            self.logger.error(f"智能选择英雄失败: {e} - By @ConceptualGod")
            return hero_options[0] if hero_options else "未知英雄"

    def _analyze_optimal_hero_combination(self, tasks: List[Dict]) -> str:
        """分析最优英雄组合，考虑所有任务的综合需求"""
        try:
            if not tasks:
                return "华佗"

            # 收集所有可能的英雄
            all_heroes = set()
            hero_task_compatibility = {}  # 英雄与任务的兼容性

            for task in tasks:
                hero_type = task.get("hero_type", "")
                task_type = task.get("task_type", "")
                recommended_hero = task.get("recommended_hero", "")

                if recommended_hero and recommended_hero != "未知英雄":
                    all_heroes.add(recommended_hero)

                    # 记录英雄与任务的兼容性
                    if recommended_hero not in hero_task_compatibility:
                        hero_task_compatibility[recommended_hero] = []
                    hero_task_compatibility[recommended_hero].append({
                        "task_desc": task.get("task_desc", ""),
                        "task_type": task_type,
                        "hero_type": hero_type
                    })

            if not all_heroes:
                return "华佗"

            # 分析每个英雄能完成的任务数量和质量
            hero_scores = {}
            for hero in all_heroes:
                compatible_tasks = hero_task_compatibility.get(hero, [])

                # 基础分数：能完成的任务数量
                task_count_score = len(compatible_tasks) * 10

                # 任务类型多样性加分
                task_types = set(task.get("task_type", "") for task in compatible_tasks)
                diversity_score = len(task_types) * 5

                # 英雄类型覆盖加分
                hero_types = set(task.get("hero_type", "") for task in compatible_tasks)
                coverage_score = 0
                if "任意英雄" in hero_types:
                    coverage_score += 15  # 任意英雄覆盖范围最广
                if len(hero_types) > 1:
                    coverage_score += 10  # 能覆盖多种英雄类型

                # 任务优先级加分（优先级高的任务加分更多）
                priority_score = 0
                task_priority = {"胜利类": 10, "完整局类": 8, "MVP类": 6, "助攻类": 4, "牺牲值类": 2}
                for task in compatible_tasks:
                    task_type = task.get("task_type", "")
                    priority_score += task_priority.get(task_type, 1)

                total_score = task_count_score + diversity_score + coverage_score + priority_score
                hero_scores[hero] = total_score

                self.logger.debug(f"英雄分析: {hero} - 任务数:{len(compatible_tasks)}, 多样性:{len(task_types)}, 覆盖:{len(hero_types)}, 总分:{total_score} - By @ConceptualGod")

            # 选择得分最高的英雄
            optimal_hero = max(hero_scores.items(), key=lambda x: x[1])[0]
            return optimal_hero

        except Exception as e:
            self.logger.error(f"分析最优英雄组合失败: {e} - By @ConceptualGod")
            return tasks[0].get("recommended_hero", "华佗") if tasks else "华佗"

    def _explain_hero_recommendation(self, tasks: List[Dict], optimal_hero: str):
        """解释英雄推荐的原因"""
        try:
            # 统计该英雄能完成的任务
            compatible_tasks = []
            incompatible_tasks = []

            for task in tasks:
                recommended_hero = task.get("recommended_hero", "")
                if recommended_hero == optimal_hero:
                    compatible_tasks.append(task)
                else:
                    incompatible_tasks.append(task)

            # 解释推荐原因
            if len(compatible_tasks) == len(tasks):
                self._log_info(f"推荐原因: {optimal_hero}能完成所有{len(tasks)}个任务，效率最高 - By @ConceptualGod")
            elif len(compatible_tasks) > len(incompatible_tasks):
                self._log_info(f"推荐原因: {optimal_hero}能完成{len(compatible_tasks)}/{len(tasks)}个任务，覆盖率最高 - By @ConceptualGod")
            else:
                self._log_info(f"推荐原因: {optimal_hero}适合完成优先级最高的任务 - By @ConceptualGod")

            # 详细说明任务分配
            if compatible_tasks:
                self._log_info(f"{optimal_hero}可完成的任务: - By @ConceptualGod")
                for i, task in enumerate(compatible_tasks, 1):
                    task_desc = task.get("task_desc", "")
                    task_type = task.get("task_type", "")
                    self._log_info(f"  ✓ [{task_type}] {task_desc} - By @ConceptualGod")

            if incompatible_tasks:
                self._log_info(f"需要其他英雄完成的任务: - By @ConceptualGod")
                for task in incompatible_tasks:
                    task_desc = task.get("task_desc", "")
                    task_type = task.get("task_type", "")
                    recommended_hero = task.get("recommended_hero", "")
                    self._log_info(f"  → [{task_type}] {task_desc} (建议: {recommended_hero}) - By @ConceptualGod")

                self._log_info(f"建议: 先用{optimal_hero}完成主要任务，再考虑切换英雄 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"解释英雄推荐失败: {e} - By @ConceptualGod")

    def stop_recognition(self):
        """
        停止任务识别

        开发者: @ConceptualGod
        """
        try:
            self.is_running = False
            self._log_info("任务识别已停止 - By @ConceptualGod")
        except Exception as e:
            self.logger.error(f"停止任务识别失败: {str(e)} - By @ConceptualGod")

    def _try_log_backup(self, recognition_id: str, start_time: float) -> Dict[str, Any]:
        """尝试使用日志监控备用方案"""
        try:
            # 扫描并更新日志数据
            self.log_monitor.scan_today_logs()
            success = self.log_monitor.update_all_data()

            if not success:
                return self._create_error_result("日志监控备用方案失败", recognition_id, start_time)

            # 从日志数据构造任务进度
            task_progress = self.log_monitor.game_data['task_progress']
            game_status = self.log_monitor.game_data['game_status']

            # 构造备用识别结果
            backup_tasks = []

            # 助攻任务
            assists = task_progress.get('assists', 0)
            if assists > 0:
                backup_tasks.append({
                    'task_type': 'assists',
                    'task_desc': f'获得{assists}个助攻',
                    'current_value': assists,
                    'source': 'log_backup'
                })

            # MVP任务
            mvp_score = task_progress.get('mvp_score', 0)
            if mvp_score > 0:
                backup_tasks.append({
                    'task_type': 'mvp',
                    'task_desc': f'获得{mvp_score}MVP分数',
                    'current_value': mvp_score,
                    'source': 'log_backup'
                })

            # 牺牲值任务
            sacrifice = task_progress.get('sacrifice_value', 0)
            if sacrifice > 0:
                backup_tasks.append({
                    'task_type': 'sacrifice',
                    'task_desc': f'获得{sacrifice}K牺牲值',
                    'current_value': sacrifice,
                    'source': 'log_backup'
                })

            # 胜利任务
            if game_status.get('is_victory', False):
                backup_tasks.append({
                    'task_type': 'victory',
                    'task_desc': '获得胜利',
                    'current_value': 1,
                    'source': 'log_backup'
                })

            # 完整局任务
            if game_status.get('game_ended', False):
                backup_tasks.append({
                    'task_type': 'complete_game',
                    'task_desc': '完成一局游戏',
                    'current_value': 1,
                    'source': 'log_backup'
                })

            # 创建成功结果
            end_time = time.time()
            processing_time = end_time - start_time

            result = {
                "success": True,
                "recognition_id": recognition_id,
                "timestamp": datetime.now().isoformat(),
                "processing_time": processing_time,
                "source": "log_backup",
                "tasks": backup_tasks,
                "task_count": len(backup_tasks),
                "message": f"日志监控备用方案成功识别{len(backup_tasks)}个任务"
            }

            self._log_info(f"日志监控备用方案成功，识别到{len(backup_tasks)}个任务 - By @ConceptualGod")
            return result

        except Exception as e:
            self.logger.error(f"日志监控备用方案执行失败: {e} - By @ConceptualGod")
            return self._create_error_result("日志监控备用方案执行失败", recognition_id, start_time)

# 开发者签名
# By @ConceptualGod
