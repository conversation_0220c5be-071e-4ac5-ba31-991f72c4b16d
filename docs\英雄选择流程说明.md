# 英雄选择流程说明

**开发者:** @ConceptualGod  
**版本:** v2.0 Final Hero Selection  
**修正时间:** 2025-08-05  
**项目:** 起凡游戏自动化脚本

## 🎯 流程概述

根据战功识别推荐的英雄，在开始游戏后自动选择对应的英雄：

### **完整流程**
```
1. 战功识别 → 推荐英雄 (如: 诸葛瑾)
2. 开始游戏 → 进入英雄选择界面
3. 查找英雄坐标 → 在herochoose.json中查找诸葛瑾的坐标
4. 点击选择英雄 → 点击诸葛瑾的坐标
5. 确认英雄选择 → 点击确认按钮
6. 魂玉搭配 → 执行魂玉搭配流程
```

## 📋 配置文件

### **herochoose.json - 英雄选择坐标**
```json
[
  {
    "step": 1,
    "x": 675,
    "y": 223,
    "description": "刘备英雄选择",
    "timestamp": "03:40:11",
    "status": "未执行"
  },
  {
    "step": 2,
    "x": 743,
    "y": 230,
    "description": "华佗英雄选择",
    "timestamp": "03:40:22",
    "status": "未执行"
  },
  {
    "step": 4,
    "x": 922,
    "y": 387,
    "description": "诸葛瑾英雄选择",
    "timestamp": "03:40:49",
    "status": "未执行"
  },
  {
    "step": 6,
    "x": 1335,
    "y": 224,
    "description": "曹操英雄选择",
    "timestamp": "04:04:32",
    "status": "未执行"
  }
]
```

### **可用英雄列表**
- **刘备** - 坐标: (675, 223)
- **华佗** - 坐标: (743, 230)
- **陆逊** - 坐标: (927, 277)
- **诸葛瑾** - 坐标: (922, 387)
- **孙权** - 坐标: (982, 275)
- **曹操** - 坐标: (1335, 224)

## 🔧 技术实现

### **1. 坐标文件加载**
```python
def _load_hero_selection_coordinates(self) -> bool:
    """加载英雄选择相关坐标配置"""
    
    # 加载英雄选择坐标 (修正路径)
    hero_file = os.path.join(os.path.dirname(current_dir), 'herochoose.json')
    
    if os.path.exists(hero_file):
        with open(hero_file, 'r', encoding='utf-8') as f:
            hero_data = json.load(f)
            for item in hero_data:
                # 从description中提取英雄名称
                description = item.get("description", "")
                hero_name = description.replace("英雄选择", "").strip()
                
                self.hero_coordinates[hero_name] = {
                    "x": item["x"],
                    "y": item["y"],
                    "description": description
                }
```

### **2. 英雄选择流程**
```python
def _execute_hero_selection_flow(self) -> bool:
    """执行英雄选择流程"""
    
    # 1. 等待游戏界面加载 (10秒)
    # 2. 准备选择英雄 (5秒)
    # 3. 获取推荐英雄
    recommended_hero = self._get_recommended_hero()
    
    # 4. 选择英雄
    if self._select_hero(recommended_hero):
        # 5. 确认英雄选择
        if self._confirm_hero_selection():
            # 6. 执行魂玉搭配
            if self._execute_hunyu_dapei():
                return True
```

### **3. 智能英雄选择**
```python
def _select_hero(self, hero_name: str) -> bool:
    """选择指定英雄"""
    
    # 直接匹配英雄名称
    if hero_name in self.hero_coordinates:
        coord = self.hero_coordinates[hero_name]
        self.logger.info(f"找到英雄 {hero_name} 的坐标: ({coord['x']}, {coord['y']})")
        
        pyautogui.click(coord["x"], coord["y"])
        time.sleep(1)  # 等待选择生效
        
        return True
    else:
        # 尝试使用默认英雄华佗
        if "华佗" in self.hero_coordinates:
            self.logger.info("使用默认英雄华佗")
            coord = self.hero_coordinates["华佗"]
            pyautogui.click(coord["x"], coord["y"])
            return True
```

### **4. 推荐英雄获取**
```python
def _get_recommended_hero(self) -> str:
    """获取战功识别推荐的英雄"""
    
    if self.recommended_hero:
        self.logger.info(f"获取到推荐英雄: {self.recommended_hero}")
        return self.recommended_hero
    else:
        # 如果没有设置推荐英雄，使用默认英雄
        default_hero = "华佗"
        self.logger.info(f"未设置推荐英雄，使用默认英雄: {default_hero}")
        return default_hero
```

## 📊 实际案例演示

### **案例1: 诸葛瑾推荐**
```
=== 开始英雄选择流程 ===
战功识别推荐英雄: 诸葛瑾
可用英雄列表: ['刘备', '华佗', '陆逊', '诸葛瑾', '孙权', '曹操']

尝试选择英雄: 诸葛瑾
找到英雄 诸葛瑾 的坐标: (922, 387)
成功点击选择英雄 诸葛瑾
✓ 英雄 诸葛瑾 选择成功

点击确认英雄按钮 (确认坐标)
英雄确认成功

魂玉搭配完成
```

### **案例2: 华佗推荐**
```
=== 开始英雄选择流程 ===
战功识别推荐英雄: 华佗
可用英雄列表: ['刘备', '华佗', '陆逊', '诸葛瑾', '孙权', '曹操']

尝试选择英雄: 华佗
找到英雄 华佗 的坐标: (743, 230)
成功点击选择英雄 华佗
✓ 英雄 华佗 选择成功
```

### **案例3: 未知英雄处理**
```
=== 开始英雄选择流程 ===
战功识别推荐英雄: 张飞
可用英雄列表: ['刘备', '华佗', '陆逊', '诸葛瑾', '孙权', '曹操']

尝试选择英雄: 张飞
未找到英雄 张飞 的坐标配置
请检查英雄名称是否正确，可用英雄: ['刘备', '华佗', '陆逊', '诸葛瑾', '孙权', '曹操']
使用默认英雄华佗
成功点击选择英雄 华佗
```

## 🔧 配置修正

### **路径修正**
修正前的问题：
```python
# 错误路径 - 在core目录下查找
hero_file = current_dir / "herochoose.json"
```

修正后：
```python
# 正确路径 - 在QFL根目录下查找
hero_file = os.path.join(os.path.dirname(current_dir), 'herochoose.json')
```

### **文件结构**
```
QFL/
├── core/
│   └── game_starter_controller.py
├── herochoose.json          ← 英雄选择坐标
├── querenhero.json          ← 确认英雄坐标
└── hunyudapei.json          ← 魂玉搭配坐标
```

## 🎯 使用效果

### **智能匹配**
- ✅ **精确匹配** - 根据战功识别结果精确选择英雄
- ✅ **容错处理** - 未找到英雄时自动使用华佗
- ✅ **日志详细** - 完整的选择过程日志

### **流程完整**
- ✅ **英雄选择** - 自动点击推荐英雄
- ✅ **确认选择** - 自动确认英雄选择
- ✅ **魂玉搭配** - 自动执行魂玉搭配

### **用户体验**
- ✅ **无需手动** - 完全自动化的英雄选择
- ✅ **智能推荐** - 基于战功任务的智能推荐
- ✅ **状态清晰** - 清楚的执行状态反馈

## 📋 注意事项

### **坐标准确性**
- 确保herochoose.json中的坐标准确
- 定期检查游戏界面变化
- 必要时重新录制坐标

### **英雄名称匹配**
- 战功识别推荐的英雄名称必须与herochoose.json中的description匹配
- 当前支持的英雄：刘备、华佗、陆逊、诸葛瑾、孙权、曹操
- 新增英雄需要在herochoose.json中添加对应坐标

### **异常处理**
- 未找到推荐英雄时自动使用华佗
- 坐标文件不存在时会有警告日志
- 点击失败时会有错误日志

## 🎉 流程优势

### **智能化**
- ✅ **战功驱动** - 基于战功任务智能推荐英雄
- ✅ **自动选择** - 无需手动干预的英雄选择
- ✅ **完整流程** - 从识别到选择的完整自动化

### **可靠性**
- ✅ **容错机制** - 多重容错保证流程稳定
- ✅ **日志完整** - 详细的执行日志便于调试
- ✅ **状态清晰** - 每个步骤的执行状态都有反馈

### **扩展性**
- ✅ **配置驱动** - 通过JSON配置文件管理坐标
- ✅ **易于维护** - 新增英雄只需添加坐标配置
- ✅ **模块化** - 英雄选择、确认、魂玉搭配分离

---

**实现完成:** 2025-08-05  
**开发者:** @ConceptualGod  
**状态:** 战功驱动的智能英雄选择  
**特点:** 智能、自动、可靠、完整
