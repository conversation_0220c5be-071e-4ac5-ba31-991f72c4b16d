#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试导入问题
"""

import sys
import traceback
from pathlib import Path

# 添加当前目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

print("开始调试导入问题...")

try:
    print("1. 测试login_controller导入...")
    from gui.login_controller import LoginController
    print("✓ login_controller导入成功")
except Exception as e:
    print(f"✗ login_controller导入失败: {e}")
    traceback.print_exc()

try:
    print("2. 测试main_window导入...")
    from gui.main_window import MainWindow
    print("✓ main_window导入成功")
except Exception as e:
    print(f"✗ main_window导入失败: {e}")
    traceback.print_exc()

print("调试完成")
