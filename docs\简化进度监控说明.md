# 简化进度监控说明

**开发者:** @ConceptualGod  
**版本:** v2.0 Final Simplified  
**修正时间:** 2025-08-05  
**项目:** 起凡游戏自动化脚本

## 🎯 简化原则

完全去掉任务类型分类，直接使用hero坐标对应的监控和领取坐标：

### **核心逻辑**
```
hero坐标索引 → 监控坐标 → 领取坐标
```

### **工作流程**
```
1. 战功识别 → 匹配到hero坐标点X
2. 进度监控 → 截图hero坐标点X上方的数值
3. 任务领取 → 点击hero坐标点X对应的领取按钮
```

## 🔧 技术实现

### **1. 任务识别时保存hero坐标索引**
```python
# OCR处理器中
task['hero_coord_index'] = closest_hero['step']  # 保存hero坐标索引

# 识别结果示例
task = {
    'text': '任意英雄完成30个助攻',
    'hero_coord_index': 1,  # 对应hero坐标点1
    'coordinate_distance': 5.8
}
```

### **2. 进度监控直接使用坐标索引**
```python
def _monitor_task_progress(self, task: Dict) -> Dict:
    """监控单个任务的进度 - 直接使用hero坐标对应的监控坐标"""
    
    hero_coord_index = task.get('hero_coord_index', None)
    
    # 直接使用hero坐标对应的监控坐标截图识别数值
    current_value = self._screenshot_and_recognize_value(hero_coord_index)
    
    return {"current": current_value, "target": target_value}
```

### **3. 任务领取直接使用坐标索引**
```python
def _claim_completed_tasks(self, completed_tasks: List[Dict]) -> bool:
    """领取已完成的任务 - 根据hero坐标索引点击对应的领取坐标"""
    
    for task in completed_tasks:
        hero_coord_index = task.get('hero_coord_index', None)
        
        # 根据hero坐标索引点击对应的领取坐标
        self._click_claim_button(hero_coord_index)
```

## 📊 坐标映射关系

### **hero坐标 → 监控坐标 → 领取坐标**
```
hero坐标点1 → 监控坐标1 → 领取坐标1
hero坐标点2 → 监控坐标2 → 领取坐标2
hero坐标点3 → 监控坐标3 → 领取坐标3
...
hero坐标点11 → 监控坐标11 → 领取坐标11
```

### **实际案例**
```
任务: "任意英雄完成30个助攻"
匹配: hero坐标点1 (距离: 5.8像素)
监控: 截图hero坐标点1上方的数值区域
识别: 当前助攻数 = 25
判断: 25 < 30 (未完成)

任务: "蜀国英雄完成25个击杀"  
匹配: hero坐标点7 (距离: 11.4像素)
监控: 截图hero坐标点7上方的数值区域
识别: 当前击杀数 = 25
判断: 25 >= 25 (已完成)
领取: 点击hero坐标点7对应的领取按钮
```

## 🔧 进度监控系统接口

### **需要的接口**
```python
class ProgressMonitorController:
    def screenshot_and_recognize_value(self, hero_coord_index: int) -> int:
        """
        根据hero坐标索引截图识别对应位置的数值
        
        Args:
            hero_coord_index: hero坐标索引 (1-11)
            
        Returns:
            int: 识别到的数值
        """
        # 根据hero_coord_index找到对应的监控坐标
        # 截图该区域
        # OCR识别数值
        # 返回数值
        
    def click_claim_button(self, hero_coord_index: int) -> bool:
        """
        根据hero坐标索引点击对应的领取按钮
        
        Args:
            hero_coord_index: hero坐标索引 (1-11)
            
        Returns:
            bool: 是否点击成功
        """
        # 根据hero_coord_index找到对应的领取坐标
        # 点击领取按钮
        # 返回是否成功
```

### **坐标配置文件示例**
```json
{
  "monitor_coordinates": {
    "1": {"x": 500, "y": 200, "width": 50, "height": 20},
    "2": {"x": 500, "y": 230, "width": 50, "height": 20},
    "3": {"x": 500, "y": 260, "width": 50, "height": 20},
    ...
    "11": {"x": 500, "y": 500, "width": 50, "height": 20}
  },
  "claim_coordinates": {
    "1": {"x": 600, "y": 200},
    "2": {"x": 600, "y": 230},
    "3": {"x": 600, "y": 260},
    ...
    "11": {"x": 600, "y": 500}
  }
}
```

## 📋 实际使用流程

### **完整流程示例**
```
=== 战功识别阶段 ===
识别到任务: "任意英雄完成30个助攻"
匹配到hero坐标点1: 距离5.8像素
保存hero_coord_index: 1

识别到任务: "蜀国英雄完成25个击杀"
匹配到hero坐标点7: 距离11.4像素
保存hero_coord_index: 7

=== 进度监控阶段 ===
监控任务1: hero坐标1对应的数值
截图识别hero坐标1上方区域: 识别到数值25
任务1进度: 25/30 (未完成)

监控任务2: hero坐标7对应的数值
截图识别hero坐标7上方区域: 识别到数值25
任务2进度: 25/25 (已完成)

=== 任务领取阶段 ===
领取任务2: 点击hero坐标7对应的领取按钮
领取成功: ✓
```

## 🎯 简化优势

### **实现简单**
- ✅ **无需分类** - 不需要复杂的任务类型判断
- ✅ **直接映射** - hero坐标直接对应监控和领取坐标
- ✅ **统一处理** - 所有任务使用相同的处理逻辑

### **维护容易**
- ✅ **配置驱动** - 通过坐标配置文件管理
- ✅ **扩展简单** - 新增任务只需添加坐标配置
- ✅ **调试方便** - 坐标索引清晰，便于调试

### **准确可靠**
- ✅ **坐标精确** - 基于实际的hero坐标位置
- ✅ **识别准确** - 直接截图识别，避免分类错误
- ✅ **操作可靠** - 直接点击对应坐标，操作可靠

## 📋 开发要求

### **进度监控系统需要实现**
1. **坐标配置管理** - 管理hero坐标对应的监控和领取坐标
2. **截图识别功能** - 根据坐标索引截图识别数值
3. **点击操作功能** - 根据坐标索引点击领取按钮

### **坐标配置要求**
1. **监控坐标** - 每个hero坐标点上方的数值区域坐标
2. **领取坐标** - 每个hero坐标点对应的领取按钮坐标
3. **OCR配置** - 数值识别的OCR参数配置

### **接口规范**
1. **输入参数** - hero坐标索引 (1-11)
2. **返回结果** - 识别的数值或操作结果
3. **异常处理** - 完善的异常处理和日志记录

## 🔧 调用示例

### **进度监控调用**
```python
# 游戏启动控制器中
hero_coord_index = task.get('hero_coord_index', 1)
current_value = progress_monitor.screenshot_and_recognize_value(hero_coord_index)

# 进度监控系统中
def screenshot_and_recognize_value(self, hero_coord_index: int) -> int:
    monitor_coord = self.monitor_coordinates[str(hero_coord_index)]
    screenshot = self.take_screenshot(monitor_coord)
    value = self.ocr_recognize_number(screenshot)
    return value
```

### **任务领取调用**
```python
# 游戏启动控制器中
hero_coord_index = task.get('hero_coord_index', 1)
success = progress_monitor.click_claim_button(hero_coord_index)

# 进度监控系统中
def click_claim_button(self, hero_coord_index: int) -> bool:
    claim_coord = self.claim_coordinates[str(hero_coord_index)]
    pyautogui.click(claim_coord['x'], claim_coord['y'])
    return True
```

---

**简化完成:** 2025-08-05  
**开发者:** @ConceptualGod  
**状态:** 去除分类，直接坐标映射  
**特点:** 简单、直接、准确、易维护
