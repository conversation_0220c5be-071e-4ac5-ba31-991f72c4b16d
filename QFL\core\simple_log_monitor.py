#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于需求的精准日志监控 - OCR备用方案
只监控对您自动化需求有直接帮助的8个核心日志文件

核心需求覆盖:
1. 战功任务监控 (scoreLOG.log, end.log)
2. 游戏状态监控 (scoreLOG.log, allcmd.log)
3. 装备购买监控 (buyItemCmd.log)
4. 游戏进程监控 (end.log, guajitime.log)
5. 性能监控 (Perf-fps.log)
6. 错误诊断 (error.log)

开发者: @ConceptualGod
"""

import os
import re
from datetime import datetime

class CompleteGameLogMonitor:
    def __init__(self, game_path="7fgame"):
        self.game_path = game_path
        self.log_path = os.path.join(game_path, "GameLog")
        self.today = datetime.now().strftime("%Y.%m.%d")
        self.today_folders = []
        
        # 基于您需求的核心数据
        self.game_data = {
            # 战功任务数据 (来自scoreLOG.log)
            'task_progress': {
                'assists': 0,           # [22] 助攻数 (助攻任务: 20个、30个)
                'mvp_score': 0,         # [29] MVP分数 (MVP任务: 150、225)
                'sacrifice_value': 0,   # [74] 牺牲值 (牺牲值任务: 200K、300K、500K)
                'gold': 0,              # [11] 金币 (5000金币出装触发)
                'hero_level': 0,        # [16] 等级 (15级、25级加生命值)
                'victories': 0,         # 胜利数 (胜利任务)
                'complete_games': 0     # 完整局数 (完整局任务)
            },
            # 游戏状态数据 (来自end.log)
            'game_status': {
                'game_ended': False,    # 游戏结束检测
                'is_victory': False,    # 胜负判断
                'game_time': 0          # 游戏时长 (20分钟模式切换)
            },
            # 装备购买数据 (来自buyItemCmd.log)
            'equipment': {
                'total_purchases': 0,   # 装备购买次数
                'last_item': '',        # 最后购买的装备
                'speed_boots_bought': False  # 是否买了速度之靴
            },
            # 游戏操作数据 (来自allcmd.log)
            'game_operations': {
                'last_command': '',     # 最后执行的命令
                'skill_usage': 0,       # 技能使用次数
                'movement_count': 0     # 移动命令次数
            },
            # 挂机检测数据 (来自guajitime.log)
            'afk_detection': {
                'afk_time': 0,          # 挂机时长
                'last_operation': 0     # 最后操作时间
            },
            # 性能数据 (来自Perf-fps.log)
            'performance': {
                'fps': 0,               # 帧率 (游戏流畅度)
                'ping': 0               # 延迟 (网络质量)
            },
            # 错误数据 (来自error.log)
            'errors': {
                'error_count': 0,       # 错误次数
                'last_error': ''        # 最后错误信息
            }
        }

    def scan_today_logs(self):
        """扫描今天的日志文件夹"""
        if not os.path.exists(self.log_path):
            return []
        
        folders = [f for f in os.listdir(self.log_path) 
                  if os.path.isdir(os.path.join(self.log_path, f)) and self.today in f]
        
        self.today_folders = sorted(folders)
        return self.today_folders

    def get_latest_session(self):
        """获取最新游戏会话路径"""
        if not self.today_folders:
            self.scan_today_logs()
        
        if not self.today_folders:
            return None
        
        latest_folder = self.today_folders[-1]
        return os.path.join(self.log_path, latest_folder)

    def extract_number(self, line):
        """从日志行中提取数字"""
        match = re.search(r'=\s*(-?\d+)', line)
        return int(match.group(1)) if match else 0

    def _parse_score_log(self, session_path):
        """解析scoreLOG.log获取战功任务数据"""
        score_file = os.path.join(session_path, "scoreLOG.log")
        if not os.path.exists(score_file):
            return
        
        try:
            with open(score_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # 从最后100行中提取核心战功数据
            for line in lines[-100:]:
                if "Escape_temp_tab[22]=" in line:  # 助攻数
                    self.game_data['task_progress']['assists'] = self.extract_number(line)
                elif "Escape_temp_tab[29]=" in line:  # MVP分数
                    self.game_data['task_progress']['mvp_score'] = self.extract_number(line)
                elif "Escape_temp_tab[74]=" in line:  # 牺牲值
                    damage_value = self.extract_number(line)
                    # 转换为K单位 (需要确定转换公式)
                    self.game_data['task_progress']['sacrifice_value'] = damage_value // 10000
                elif "Escape_temp_tab[11]=" in line:  # 金币数量
                    self.game_data['task_progress']['gold'] = self.extract_number(line)
                elif "Escape_temp_tab[16]=" in line:  # 英雄等级
                    self.game_data['task_progress']['hero_level'] = self.extract_number(line)
            
        except Exception as e:
            print(f"解析scoreLOG.log失败: {e}")

    def _parse_end_log(self, session_path):
        """解析end.log获取游戏结束状态"""
        end_file = os.path.join(session_path, "end.log")
        if not os.path.exists(end_file):
            return
        
        try:
            with open(end_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # 检查最后几行的游戏结果
            for line in lines[-10:]:
                if "查询结果" in line:
                    result = self.extract_number(line)
                    self.game_data['game_status']['game_ended'] = True
                    self.game_data['game_status']['is_victory'] = (result == 5)
                    
                    # 更新统计
                    self.game_data['task_progress']['complete_games'] = 1
                    if result == 5:
                        self.game_data['task_progress']['victories'] = 1
                    break
            
        except Exception as e:
            print(f"解析end.log失败: {e}")

    def _parse_buy_item_log(self, session_path):
        """解析buyItemCmd.log获取装备购买数据"""
        buy_file = os.path.join(session_path, "buyItemCmd.log")
        if not os.path.exists(buy_file):
            return

        try:
            with open(buy_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

            purchase_count = len(lines)
            last_item = ""
            speed_boots_bought = False

            # 检查购买记录
            for line in lines:
                line_content = line.strip()
                if line_content:
                    last_item = line_content
                    # 检查是否购买了速度之靴 (根据您的需求: B键买速度之靴)
                    if "速度" in line_content or "靴" in line_content or "boots" in line_content.lower():
                        speed_boots_bought = True

            self.game_data['equipment']['total_purchases'] = purchase_count
            self.game_data['equipment']['last_item'] = last_item
            self.game_data['equipment']['speed_boots_bought'] = speed_boots_bought

        except Exception as e:
            print(f"解析buyItemCmd.log失败: {e}")

    def _parse_performance_log(self, session_path):
        """解析Perf-fps.log获取性能数据"""
        perf_file = os.path.join(session_path, "Perf-fps.log")
        if not os.path.exists(perf_file):
            return
        
        try:
            with open(perf_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # 从最后几行提取性能数据
            for line in lines[-10:]:
                if "[FPS," in line:
                    fps_match = re.search(r'\[FPS,([0-9.]+)', line)
                    if fps_match:
                        self.game_data['performance']['fps'] = int(float(fps_match.group(1)))
                elif "[Ping," in line:
                    ping_match = re.search(r'\[Ping,([0-9.]+)', line)
                    if ping_match:
                        self.game_data['performance']['ping'] = int(float(ping_match.group(1)))
            
        except Exception as e:
            print(f"解析Perf-fps.log失败: {e}")

    def _parse_all_cmd_log(self, session_path):
        """解析allcmd.log获取游戏操作数据"""
        cmd_file = os.path.join(session_path, "allcmd.log")
        if not os.path.exists(cmd_file):
            return

        try:
            with open(cmd_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

            # 统计操作数据
            skill_count = 0
            movement_count = 0
            last_command = ""

            for line in lines[-100:]:  # 检查最后100行
                if "346" in line:  # 技能使用命令
                    skill_count += 1
                elif "301" in line:  # 移动命令
                    movement_count += 1

                if line.strip():
                    last_command = line.strip()

            self.game_data['game_operations']['skill_usage'] = skill_count
            self.game_data['game_operations']['movement_count'] = movement_count
            self.game_data['game_operations']['last_command'] = last_command

        except Exception as e:
            print(f"解析allcmd.log失败: {e}")

    def _parse_guaji_time_log(self, session_path):
        """解析guajitime.log获取挂机检测数据"""
        guaji_file = os.path.join(session_path, "guajitime.log")
        if not os.path.exists(guaji_file):
            return

        try:
            with open(guaji_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

            # 提取挂机时间信息
            for line in lines[-10:]:
                if "time" in line:
                    time_match = re.search(r'time[=:]\s*(\d+)', line)
                    if time_match:
                        self.game_data['afk_detection']['afk_time'] = int(time_match.group(1))

                if "op_time" in line:
                    op_time_match = re.search(r'op_time[=:]\s*(\d+)', line)
                    if op_time_match:
                        self.game_data['afk_detection']['last_operation'] = int(op_time_match.group(1))

        except Exception as e:
            print(f"解析guajitime.log失败: {e}")

    def _parse_error_log(self, session_path):
        """解析error.log获取错误信息"""
        error_file = os.path.join(session_path, "error.log")
        if not os.path.exists(error_file):
            return

        try:
            with open(error_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

            error_count = len(lines)
            last_error = ""

            if lines:
                last_error = lines[-1].strip()

            self.game_data['errors']['error_count'] = error_count
            self.game_data['errors']['last_error'] = last_error

        except Exception as e:
            print(f"解析error.log失败: {e}")

    def update_all_data(self):
        """更新8个核心日志的数据 - 基于您的需求"""
        session_path = self.get_latest_session()
        if not session_path:
            return False

        # 解析8个对您需求有帮助的核心日志文件
        self._parse_score_log(session_path)      # scoreLOG.log - 战功任务和游戏状态
        self._parse_end_log(session_path)        # end.log - 游戏结束和胜负
        self._parse_buy_item_log(session_path)   # buyItemCmd.log - 装备购买
        self._parse_all_cmd_log(session_path)    # allcmd.log - 游戏操作
        self._parse_guaji_time_log(session_path) # guajitime.log - 挂机检测
        self._parse_performance_log(session_path) # Perf-fps.log - 性能监控
        self._parse_error_log(session_path)      # error.log - 错误诊断

        return True

    def get_task_progress(self):
        """获取任务进度 - 替代OCR识别"""
        self.scan_today_logs()
        if not self.today_folders:
            return None

        self.update_all_data()
        return self.game_data['task_progress']

    def get_game_status(self):
        """获取游戏状态"""
        self.scan_today_logs()
        if not self.today_folders:
            return None

        self.update_all_data()
        return self.game_data['game_status']

    def get_equipment_status(self):
        """获取装备状态 - 监控出装情况"""
        self.scan_today_logs()
        if not self.today_folders:
            return None

        self.update_all_data()
        return self.game_data['equipment']

    def get_automation_status(self):
        """获取自动化相关状态 - 基于您的需求"""
        self.scan_today_logs()
        if not self.today_folders:
            return None

        self.update_all_data()

        # 整合您需要的关键状态
        task_progress = self.game_data['task_progress']
        game_status = self.game_data['game_status']
        equipment = self.game_data['equipment']
        performance = self.game_data['performance']

        return {
            # 战功任务状态
            'task_status': {
                'assists_progress': f"{task_progress['assists']}/30",
                'mvp_progress': f"{task_progress['mvp_score']}/225",
                'sacrifice_progress': f"{task_progress['sacrifice_value']}/500K",
                'victory_achieved': task_progress['victories'] > 0,
                'complete_game_achieved': task_progress['complete_games'] > 0
            },
            # 游戏状态触发条件
            'trigger_conditions': {
                'gold_enough_for_equipment': task_progress['gold'] >= 5000,  # 5000金币出装
                'level_15_reached': task_progress['hero_level'] >= 15,       # 15级加生命值
                'level_25_reached': task_progress['hero_level'] >= 25,       # 25级加生命值
                'game_ended': game_status['game_ended'],                     # 游戏结束
                'is_victory': game_status['is_victory']                      # 是否胜利
            },
            # 装备状态
            'equipment_status': {
                'total_purchases': equipment['total_purchases'],
                'speed_boots_bought': equipment['speed_boots_bought'],       # B键买速度之靴
                'last_item': equipment['last_item']
            },
            # 性能状态
            'performance_status': {
                'fps_good': performance['fps'] >= 30,                       # 游戏流畅
                'ping_good': performance['ping'] <= 100,                    # 网络良好
                'fps': performance['fps'],
                'ping': performance['ping']
            }
        }

    def check_task_completion(self, task_type, target_value):
        """检查任务完成状态"""
        progress = self.get_task_progress()
        if not progress:
            return False

        if task_type == 'assists':
            return progress['assists'] >= target_value
        elif task_type == 'mvp':
            return progress['mvp_score'] >= target_value
        elif task_type == 'sacrifice':
            return progress['sacrifice_value'] >= target_value
        elif task_type == 'victory':
            return progress['victories'] >= target_value
        elif task_type == 'complete_game':
            return progress['complete_games'] >= target_value

        return False

    def check_automation_triggers(self):
        """检查自动化触发条件 - 基于您的需求"""
        status = self.get_automation_status()
        if not status:
            return {}

        triggers = status['trigger_conditions']

        return {
            'should_buy_equipment': triggers['gold_enough_for_equipment'],    # 5000金币出装
            'should_add_health_15': triggers['level_15_reached'],             # 15级加生命值
            'should_add_health_25': triggers['level_25_reached'],             # 25级加生命值
            'game_finished': triggers['game_ended'],                          # 游戏结束
            'victory_achieved': triggers['is_victory'],                       # 胜利
            'performance_good': status['performance_status']['fps_good'] and
                               status['performance_status']['ping_good']      # 性能良好
        }
