#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录控制器模块

开发者: @ConceptualGod
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import logging
import json
import os
from typing import List, Dict, Any, Optional, Callable, Tuple
import pyautogui
import sys
from pathlib import Path

# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from core.config_manager import ConfigManager

class GameAutomation:
    """起凡游戏窗口自动化类"""

    def __init__(self):
        self.is_logged_in = False
        self.game_window_title = ""
        self.game_window_handle = None

    def detect_game_window(self) -> bool:
        """检测起凡游戏窗口（简化版：进程+窗口+强制置前）"""
        try:
            import win32gui
            import psutil
            import win32process

            # 方法1：通过进程扫描查找7FGame.exe
            try:
                for proc in psutil.process_iter(['pid', 'name']):
                    if proc.info['name'] == '7FGame.exe':
                        pid = proc.info['pid']

                        # 通过进程找到对应的窗口
                        def enum_windows_callback(hwnd, windows):
                            try:
                                _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                                if window_pid == pid and win32gui.IsWindowVisible(hwnd):
                                    window_title = win32gui.GetWindowText(hwnd)
                                    if "起凡游戏平台" in window_title:
                                        windows.append((hwnd, window_title))
                            except:
                                pass
                            return True

                        windows = []
                        win32gui.EnumWindows(enum_windows_callback, windows)

                        if windows:
                            self.game_window_handle, self.game_window_title = windows[0]
                            self._determine_login_status()
                            return True
            except:
                pass

            # 方法2：直接搜索窗口标题
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if "起凡游戏平台" in window_title:
                        windows.append((hwnd, window_title))
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            if windows:
                self.game_window_handle, self.game_window_title = windows[0]
                self._determine_login_status()
                return True
            else:
                self.game_window_handle = None
                self.game_window_title = ""
                self.is_logged_in = False
                return False

        except ImportError:
            # 如果没有win32gui，使用备用方法
            return self._detect_window_fallback()
        except Exception as e:
            print(f"检测游戏窗口异常: {e}")
            return False

    def _detect_window_fallback(self) -> bool:
        """备用窗口检测方法"""
        try:
            import psutil

            # 查找起凡游戏进程
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    if proc.info['name'] and '起凡' in proc.info['name']:
                        self.game_window_title = "起凡游戏平台"
                        self.is_logged_in = False  # 无法精确判断，默认未登录
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return False
        except ImportError:
            # 如果psutil也没有，返回False
            return False

    def bring_window_to_front(self) -> bool:
        """将游戏窗口置于前台"""
        try:
            import win32gui
            import win32con

            if self.game_window_handle:
                # 恢复窗口（如果最小化）
                win32gui.ShowWindow(self.game_window_handle, win32con.SW_RESTORE)
                # 置于前台
                win32gui.SetForegroundWindow(self.game_window_handle)
                return True
            return False

        except ImportError:
            # 如果没有win32gui，使用pyautogui的备用方法
            return self._bring_window_to_front_fallback()
        except Exception as e:
            print(f"窗口置前异常: {e}")
            return False

    def _determine_login_status(self):
        """判断登录状态（改进版：基于多个特征判断）"""
        title = self.game_window_title.strip()

        # 未登录状态的特征（优先级高）
        logout_indicators = [
            "发布时间",           # 主要特征：起凡游戏平台  Version: *********  发布时间: 2025.06.11
            "Version:",          # 版本信息通常出现在未登录界面
            "登录",              # 登录按钮或登录界面
            "Login"              # 英文登录界面
        ]

        # 已登录状态的特征
        login_indicators = [
            "起凡游戏平台-",      # 已登录格式：起凡游戏平台-*********
            "群雄逐鹿",          # 游戏房间
            "三国争霸",          # 游戏房间
            "房间",              # 游戏房间界面
            "大厅"               # 游戏大厅
        ]

        # 首先检查未登录特征（更可靠）
        for indicator in logout_indicators:
            if indicator in title:
                self.is_logged_in = False
                return

        # 然后检查已登录特征
        for indicator in login_indicators:
            if indicator in title:
                self.is_logged_in = True
                return

        # 如果都没有匹配，根据标题长度和格式判断
        # 未登录标题通常很长，已登录标题相对简短
        if len(title) > 30:  # 长标题通常是未登录状态
            self.is_logged_in = False
        elif len(title) > 0:  # 有标题但较短，可能是已登录
            self.is_logged_in = True
        else:  # 空标题，默认未登录
            self.is_logged_in = False

    def _bring_window_to_front_fallback(self) -> bool:
        """备用窗口置前方法"""
        try:
            # 使用Alt+Tab切换窗口的方法
            pyautogui.hotkey('alt', 'tab')
            time.sleep(0.5)
            return True
        except Exception:
            return False

    def input_text_via_clipboard(self, text: str) -> bool:
        """通过剪贴板输入文本"""
        try:
            import pyperclip
            pyperclip.copy(text)
            pyautogui.hotkey('ctrl', 'v')
            return True
        except ImportError:
            # 如果没有pyperclip，返回False让调用者使用备用方法
            return False
        except Exception:
            return False

class LoginController:
    """登录控制器"""
    
    def __init__(self, parent_frame):
        """
        初始化登录控制器

        Args:
            parent_frame: 父级框架
        """
        self.parent_frame = parent_frame
        self.logger = logging.getLogger(__name__)

        # 初始化核心组件
        self.config_manager = ConfigManager()

        # 创建游戏自动化对象
        self.automation = GameAutomation()

        # 登录状态
        self.is_running = False
        self.current_account = None
        self.login_thread = None
        self.last_logged_username = ""  # 记录上一个登录的账号名

        # 登录坐标配置
        self.login_coordinates = []
        self.login_config_file = "login.json"

        # 回调函数
        self.get_accounts_callback = None
        self.log_callback = None  # 日志回调函数

        # 先创建界面（包括status_text）
        self._create_widgets()

        # 然后加载登录坐标配置（需要status_text已存在）
        self._load_login_coordinates()

        # 绑定快捷键
        self._setup_hotkeys()

    def _pause_login(self):
        """
        暂停轮登并保存当前断点

        开发者: @ConceptualGod
        """
        try:
            if not self.is_running:
                messagebox.showinfo("提示 - By @ConceptualGod", "当前没有正在运行的轮登任务")
                return

            # 停止运行
            self.is_running = False

            # 保存当前状态为断点
            if hasattr(self, 'current_account') and self.current_account:
                account_index = getattr(self, 'current_account_index', 0)
                current_step = getattr(self, 'current_step', '用户暂停')

                self._log_status(f"轮登已暂停，断点已保存: 账号{account_index}, 步骤{current_step} - By @ConceptualGod")
                messagebox.showinfo("暂停成功 - By @ConceptualGod",
                                   f"轮登已暂停并保存断点\n账号索引: {account_index}\n当前步骤: {current_step}")
            else:
                self._log_status("轮登已暂停 - By @ConceptualGod")
                messagebox.showinfo("暂停成功 - By @ConceptualGod", "轮登已暂停")

        except Exception as e:
            self.logger.error(f"暂停轮登异常: {str(e)} - By @ConceptualGod")
            messagebox.showerror("错误 - By @ConceptualGod", f"暂停失败: {str(e)}")

    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.parent_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 账号信息显示框架
        account_frame = ttk.LabelFrame(main_frame, text="账号信息")
        account_frame.pack(fill=tk.X, pady=(0, 10))

        # 账号数量显示
        self.account_count_label = ttk.Label(account_frame, text="已导入账号: 0个")
        self.account_count_label.pack(side=tk.LEFT, padx=(10, 5), pady=5)

        # 刷新账号按钮
        ttk.Button(account_frame, text="刷新账号", command=self._refresh_accounts).pack(side=tk.LEFT, padx=10, pady=5)
        
        # 控制按钮框架
        control_frame = ttk.LabelFrame(main_frame, text="多号轮登控制")
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # 控制按钮
        self.start_button = ttk.Button(control_frame, text="开始多号轮登", command=self._start_login)
        self.start_button.pack(side=tk.LEFT, padx=10, pady=5)

        self.stop_button = ttk.Button(control_frame, text="停止轮登", command=self._stop_login, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=10, pady=5)

        # 紧急停止按钮
        self.emergency_stop_button = ttk.Button(control_frame, text="紧急停止", command=self._emergency_stop)
        self.emergency_stop_button.pack(side=tk.LEFT, padx=10, pady=5)

        # 清空日志按钮
        self.clear_log_button = ttk.Button(control_frame, text="清空日志", command=self._clear_logs)
        self.clear_log_button.pack(side=tk.LEFT, padx=10, pady=5)

        # 断点续传按钮
        self.resume_button = ttk.Button(control_frame, text="断点续传", command=self._resume_from_checkpoint)
        self.resume_button.pack(side=tk.LEFT, padx=10, pady=5)



        # 暂停按钮
        self.pause_button = ttk.Button(control_frame, text="暂停", command=self._pause_login)
        self.pause_button.pack(side=tk.LEFT, padx=10, pady=5)

        # 状态显示框架
        status_frame = ttk.LabelFrame(main_frame, text="登录状态")
        status_frame.pack(fill=tk.BOTH, expand=True)
        
        # 状态文本框
        self.status_text = tk.Text(status_frame, height=10, state=tk.DISABLED)
        status_scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=status_scrollbar.set)
        
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        status_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 状态和署名框架
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=5)

        # 当前状态标签
        self.current_status_label = ttk.Label(bottom_frame, text="状态: 就绪")
        self.current_status_label.pack(side=tk.LEFT)

        # 署名水印
        signature_label = ttk.Label(bottom_frame, text="By @ConceptualGod",
                                   foreground="gray", font=("Arial", 8))
        signature_label.pack(side=tk.RIGHT)

    def _setup_hotkeys(self):
        """
        设置快捷键

        开发者: @ConceptualGod
        """
        try:
            # 绑定END键为紧急停止
            self.parent_frame.bind_all('<End>', lambda event: self._emergency_stop())
            # 只记录到日志文件，不显示到GUI（避免初始化时的问题）
            self.logger.info("快捷键绑定成功: END键 -> 紧急停止 - By @ConceptualGod")
        except Exception as e:
            self.logger.error(f"快捷键绑定失败: {str(e)} - By @ConceptualGod")
    
    def set_account_callbacks(self, get_accounts_func: Callable):
        """
        设置获取账号的回调函数

        Args:
            get_accounts_func: 获取所有账号的函数
        """
        self.get_accounts_callback = get_accounts_func
        # 延迟刷新账号，确保界面完全初始化后再执行
        if hasattr(self, 'status_text') and self.status_text:
            self._refresh_accounts()
    
    def _refresh_accounts(self):
        """刷新账号信息"""
        try:
            if self.get_accounts_callback:
                accounts = self.get_accounts_callback()
                account_count = len(accounts)

                # 更新账号数量显示（仅当界面组件存在时）
                if hasattr(self, 'account_count_label') and self.account_count_label:
                    self.account_count_label.config(text=f"已导入账号: {account_count}个")

                # 记录日志（仅当日志系统可用时）
                if hasattr(self, 'status_text') and self.status_text:
                    self._log_status(f"刷新账号信息，共 {account_count} 个账号")
                else:
                    self.logger.info(f"刷新账号信息，共 {account_count} 个账号")

        except Exception as e:
            self.logger.error(f"刷新账号信息异常: {str(e)}")
            # 只有在GUI组件存在时才显示到GUI
            if hasattr(self, 'status_text') and self.status_text:
                self._log_status(f"刷新账号信息失败: {str(e)}")
            else:
                self.logger.error(f"刷新账号信息失败: {str(e)}")
    
    def _resume_from_checkpoint(self):
        """
        从断点续传

        开发者: @ConceptualGod
        """
        try:
            if self.is_running:
                messagebox.showwarning("警告 - By @ConceptualGod", "登录已在进行中")
                return

            # 断点功能已移除
            messagebox.showinfo("提示 - By @ConceptualGod", "断点续传功能已移除")
            return

            # 显示断点信息
            account_index = checkpoint.get('account_index', 0)
            step = checkpoint.get('step', '')
            error_msg = checkpoint.get('error_msg', '')
            timestamp = checkpoint.get('timestamp', '')

            msg = f"发现断点信息:\n"
            msg += f"时间: {timestamp}\n"
            msg += f"账号索引: {account_index}\n"
            msg += f"执行步骤: {step}\n"
            if error_msg:
                msg += f"错误信息: {error_msg}\n"
            msg += f"\n是否从此断点继续执行？"

            if messagebox.askyesno("断点续传 - By @ConceptualGod", msg):
                # 获取账号列表
                if self.get_accounts_callback:
                    all_accounts = self.get_accounts_callback()
                    if not all_accounts:
                        messagebox.showwarning("警告 - By @ConceptualGod", "没有账号可以登录")
                        return

                    # 从断点开始执行
                    self._start_multiple_login_from_checkpoint(all_accounts, checkpoint)
                else:
                    messagebox.showwarning("警告 - By @ConceptualGod", "账号回调函数未设置")

        except Exception as e:
            self.logger.error(f"断点续传异常: {str(e)} - By @ConceptualGod")
            messagebox.showerror("错误 - By @ConceptualGod", f"断点续传失败: {str(e)}")

    def _start_login(self):
        """开始多号轮登"""
        try:
            if self.is_running:
                messagebox.showwarning("警告 - By @ConceptualGod", "登录已在进行中")
                return

            # 检查是否有断点


            # 多号轮换
            if self.get_accounts_callback:
                all_accounts = self.get_accounts_callback()
                if not all_accounts:
                    messagebox.showwarning("警告 - By @ConceptualGod", "没有账号可以登录")
                    return
                self._start_multiple_login(all_accounts)
            else:
                messagebox.showwarning("警告 - By @ConceptualGod", "账号回调函数未设置")

        except Exception as e:
            self.logger.error(f"开始登录异常: {str(e)} - By @ConceptualGod")
            messagebox.showerror("错误 - By @ConceptualGod", f"开始登录失败: {str(e)}")

    def _start_multiple_login_from_checkpoint(self, accounts: List[Dict[str, Any]], checkpoint: Dict[str, Any]):
        """
        从断点开始多号轮换登录

        Args:
            accounts: 账号列表
            checkpoint: 断点信息

        开发者: @ConceptualGod
        """
        self.is_running = True
        self.total_accounts = len(accounts)
        self._update_button_state()
        self._log_status(f"从断点续传多号轮换登录，共 {len(accounts)} 个账号")
        self._log_status(f"断点信息: 账号{checkpoint.get('account_index')}, 步骤{checkpoint.get('step')} - By @ConceptualGod")

        # 在新线程中执行登录
        self.login_thread = threading.Thread(target=self._multiple_login_worker_from_checkpoint, args=(accounts, checkpoint))
        self.login_thread.daemon = True
        self.login_thread.start()

    def _start_multiple_login(self, accounts: List[Dict[str, Any]]):
        """
        开始多号轮换登录

        Args:
            accounts: 账号列表
        """
        self.is_running = True
        self.total_accounts = len(accounts)
        self._update_button_state()
        self._log_status(f"开始多号轮换登录，共 {len(accounts)} 个账号")

        # 在新线程中执行登录
        self.login_thread = threading.Thread(target=self._multiple_login_worker, args=(accounts,))
        self.login_thread.daemon = True
        self.login_thread.start()

    def _multiple_login_worker(self, accounts: List[Dict[str, Any]]):
        """
        多号轮换登录工作线程

        Args:
            accounts: 账号列表

        开发者: @ConceptualGod
        """
        try:
            # 在开始前先检测一次游戏窗口
            initial_window_check = self.automation.detect_game_window()
            if not initial_window_check:
                self._log_status("初始窗口检测失败，无法开始多号轮登 - By @ConceptualGod")
                return

            for i, account in enumerate(accounts):
                if not self.is_running:
                    break

                self.current_account = account
                self._update_current_status(f"准备第{i+1}个账号...")

                # 重新检测游戏窗口（允许失败重试）
                window_detected = False
                for retry in range(3):  # 最多重试3次
                    self._update_current_status(f"检测游戏窗口... (尝试{retry+1}/3)")
                    if self.automation.detect_game_window():
                        window_detected = True
                        break
                    else:
                        self._log_status(f"第 {i+1} 个账号: 窗口检测失败，重试 {retry+1}/3 - By @ConceptualGod")
                        time.sleep(2)  # 等待2秒后重试

                if not window_detected:
                    self._update_current_status("未检测到游戏窗口")
                    self._log_status(f"第 {i+1} 个账号: 多次尝试后仍未检测到游戏窗口，跳过 - By @ConceptualGod")
                    continue

                # 将游戏窗口置于前台（允许失败重试）
                window_front = False
                for retry in range(2):  # 最多重试2次
                    self._update_current_status(f"准备游戏窗口... (尝试{retry+1}/2)")
                    if self.automation.bring_window_to_front():
                        window_front = True
                        break
                    else:
                        self._log_status(f"第 {i+1} 个账号: 窗口置前失败，重试 {retry+1}/2 - By @ConceptualGod")
                        time.sleep(1)

                if not window_front:
                    self._update_current_status("窗口置前失败")
                    self._log_status(f"第 {i+1} 个账号: 多次尝试后仍无法将游戏窗口置于前台，跳过 - By @ConceptualGod")
                    continue

                # 等待窗口稳定
                time.sleep(2)  # 增加等待时间确保窗口稳定

                # 执行登录
                self.current_account = account
                self.current_account_index = i + 1
                self.current_step = f"开始登录账号{i+1}"

                self._update_current_status(f"正在登录: {account['username']} ({i+1}/{len(accounts)})")
                self._log_status(f"开始登录账号 {i+1}/{len(accounts)}: {account['username']} - By @ConceptualGod")

                # 执行登录前再次检查停止状态
                if not self.is_running:
                    break

                # 执行登录
                success = self._perform_multiple_login(account, i+1, len(accounts))

                # 登录完成后检查停止状态
                if not self.is_running:
                    break

                if success:
                    self._log_status(f"账号 {account['username']} 完整流程执行成功 - By @ConceptualGod")

                    # 如果不是最后一个账号，需要准备下一个账号
                    if i < len(accounts) - 1 and self.is_running:
                        next_account = accounts[i+1]
                        self._prepare_next_account(next_account['username'])
                else:
                    self._log_status(f"账号 {account['username']} 流程执行失败 - By @ConceptualGod")

                    # 登录失败后，等待一段时间再继续下一个账号
                    if i < len(accounts) - 1 and self.is_running:
                        self._log_status(f"账号 {account['username']} 登录失败，等待5秒后继续下一个账号 - By @ConceptualGod")
                        # 分段等待，便于快速响应停止
                        for wait_sec in range(5):
                            if not self.is_running:
                                break
                            time.sleep(1)

        except Exception as e:
            self.logger.error(f"多号轮换登录异常: {str(e)}")
            self._log_status(f"多号轮换登录异常: {str(e)}")
        finally:
            self.is_running = False
            self.current_account = None
            self._update_button_state()
            self._update_current_status("所有账号登录完成")

    def _multiple_login_worker_from_checkpoint(self, accounts: List[Dict[str, Any]], checkpoint: Dict[str, Any]):
        """
        从断点开始的多号轮换登录工作线程

        Args:
            accounts: 账号列表
            checkpoint: 断点信息

        开发者: @ConceptualGod
        """
        try:
            start_account_index = checkpoint.get('account_index', 0)
            start_step = checkpoint.get('step', '')

            self._log_status(f"从断点开始执行: 账号{start_account_index}, 步骤{start_step} - By @ConceptualGod")

            # 从指定账号开始执行
            for i in range(start_account_index - 1, len(accounts)):  # 账号索引从1开始，数组从0开始
                if not self.is_running:
                    break

                account = accounts[i]
                self.current_account = account
                account_index = i + 1

                self._update_current_status(f"续传第{account_index}个账号...")
                self._log_status(f"续传执行账号 {account_index}/{len(accounts)}: {account['username']} - By @ConceptualGod")

                # 保存当前进度

                # 所有账号都正常执行
                success = self._perform_multiple_login(account, account_index, len(accounts))

                # 登录完成后检查停止状态
                if not self.is_running:
                    break

                if success:
                    self._log_status(f"账号 {account['username']} 完整流程执行成功 - By @ConceptualGod")
                else:
                    self._log_status(f"账号 {account['username']} 流程执行失败 - By @ConceptualGod")
                    # 保存失败断点

                    # 登录失败后，等待一段时间再继续下一个账号
                    if i < len(accounts) - 1 and self.is_running:
                        self._log_status(f"账号 {account['username']} 登录失败，等待5秒后继续下一个账号 - By @ConceptualGod")
                        for wait_sec in range(5):
                            if not self.is_running:
                                break
                            time.sleep(1)

            # 全部完成
            if self.is_running:
                self._log_status("所有账号执行完成 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"断点续传登录异常: {str(e)} - By @ConceptualGod")
            self._log_status(f"断点续传登录异常: {str(e)} - By @ConceptualGod")
            # 保存异常断点
            if hasattr(self, 'current_account') and self.current_account:
                account_index = getattr(self, 'current_account_index', 0)
        finally:
            self.is_running = False
            self.current_account = None
            self._update_button_state()
            self._update_current_status("断点续传完成")

    def _perform_login(self, account: Dict[str, Any]) -> bool:
        """
        执行登录操作

        Args:
            account: 账号信息

        Returns:
            是否登录成功

        开发者: @ConceptualGod
        """
        try:
            username = account.get('username', '')
            password = account.get('password', '')

            if not username or not password:
                self._log_status("错误: 账号或密码为空 - By @ConceptualGod")
                return False

            if not self.login_coordinates:
                self._log_status("错误: 未加载登录坐标配置 - By @ConceptualGod")
                return False

            self._log_status(f"开始登录账号: {username} - 使用JSON坐标配置 - By @ConceptualGod")

            # 按步骤执行登录坐标
            for coord in self.login_coordinates:
                step = coord.get("step", 0)
                x = coord.get("x", 0)
                y = coord.get("y", 0)
                description = coord.get("description", "")

                self._update_current_status(f"执行第 {step} 步: {description}...")
                self._log_status(f"第 {step} 步: {description} - 坐标({x}, {y}) - By @ConceptualGod")

                # 点击坐标
                pyautogui.click(x, y)
                time.sleep(0.5)  # 等待界面响应

                # 根据步骤执行相应操作
                if step == 1:  # 账号输入框
                    self._update_current_status("输入账号...")
                    if not self._input_text(username):
                        self._log_status("错误: 输入账号失败 - By @ConceptualGod")
                        return False
                elif step == 2:  # 密码输入框
                    self._update_current_status("输入密码...")
                    if not self._input_text(password):
                        self._log_status("错误: 输入密码失败 - By @ConceptualGod")
                        return False
                elif step == 3:  # 登录按钮
                    self._update_current_status("点击登录按钮...")
                    # 登录按钮已经点击，无需额外操作

                # 更新坐标状态
                coord["status"] = "已执行"

                # 步骤间隔
                time.sleep(0.5)

            # 等待登录结果
            login_success = self._wait_for_login_result()

            if login_success:
                self._log_status(f"账号 {username} 登录成功 - By @ConceptualGod")

                # 记录当前登录的账号名
                self.last_logged_username = username
                self._log_status(f"记录当前登录账号名: {username} - By @ConceptualGod")

                return True
            else:
                self._log_status(f"账号 {username} 登录失败或超时 - By @ConceptualGod")
                return False

        except Exception as e:
            self.logger.error(f"登录执行异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"登录异常: {str(e)} - By @ConceptualGod")
            return False

    def _perform_multiple_login(self, account: Dict[str, Any], account_index: int, total_accounts: int) -> bool:
        """
        执行多号登录操作（包含游戏操作和退出）

        Args:
            account: 账号信息
            account_index: 当前账号索引
            total_accounts: 总账号数

        Returns:
            是否执行成功

        开发者: @ConceptualGod
        """
        try:
            username = account.get('username', '')
            password = account.get('password', '')

            if not username or not password:
                self._log_status(f"第{account_index}个账号信息不完整 - By @ConceptualGod")
                return False

            if not self.login_coordinates:
                self._log_status(f"第{account_index}个账号: 未加载登录坐标配置 - By @ConceptualGod")
                return False

            self._log_status(f"第{account_index}/{total_accounts}个账号开始登录: {username} - By @ConceptualGod")

            # 保存断点：开始登录
            self.current_step = "开始登录"

            # 按步骤执行登录坐标
            for coord in self.login_coordinates:
                step = coord.get("step", 0)
                x = coord.get("x", 0)
                y = coord.get("y", 0)
                description = coord.get("description", "")

                self._update_current_status(f"第{account_index}个账号 - 第{step}步: {description}...")
                self._log_status(f"第{account_index}个账号 - 第{step}步: {description} - 坐标({x}, {y}) - By @ConceptualGod")

                # 点击坐标
                pyautogui.click(x, y)
                time.sleep(0.5)

                # 根据步骤执行相应操作
                if step == 1:  # 账号输入框
                    self._update_current_status(f"第{account_index}个账号 - 输入账号...")
                    if not self._input_text(username):
                        self._log_status(f"第{account_index}个账号: 输入账号失败 - By @ConceptualGod")
                        return False
                elif step == 2:  # 密码输入框
                    self._update_current_status(f"第{account_index}个账号 - 输入密码...")
                    if not self._input_text(password):
                        self._log_status(f"第{account_index}个账号: 输入密码失败 - By @ConceptualGod")
                        return False
                elif step == 3:  # 登录按钮
                    self._update_current_status(f"第{account_index}个账号 - 点击登录按钮...")

                coord["status"] = "已执行"
                time.sleep(0.5)

            # 保存断点：等待登录结果
            self.current_step = "等待登录结果"

            # 等待登录结果（多号登录专用，包含15秒网络延迟等待）
            login_success = self._wait_for_login_result_multiple(account_index)

            if login_success:
                self._log_status(f"第{account_index}个账号 {username} 登录成功 - By @ConceptualGod")

                # 记录当前登录的账号名
                self.last_logged_username = username
                self._log_status(f"记录当前登录账号名: {username} - By @ConceptualGod")

                # 保存断点：开始游戏操作
                self.current_step = "开始游戏操作"

                # 执行游戏操作
                self._execute_game_operations_for_multiple(username, account_index)

                return True
            else:
                self._log_status(f"第{account_index}个账号 {username} 登录失败或超时 - By @ConceptualGod")
                # 保存失败断点
                return False

        except Exception as e:
            self.logger.error(f"第{account_index}个账号登录执行异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"第{account_index}个账号登录异常: {str(e)} - By @ConceptualGod")
            # 保存异常断点
            return False



    # 注意：原有的句柄检测方法已移除，现在完全使用JSON坐标配置
    # _find_login_dialog, _find_username_input, _find_login_button 方法已不再需要

    def _click_element(self, position: Tuple[int, int]) -> bool:
        """
        点击指定位置

        Args:
            position: 点击位置 (x, y)

        Returns:
            是否点击成功

        开发者: @ConceptualGod
        """
        try:
            x, y = position

            # 双击以确保选中输入框
            pyautogui.doubleClick(x, y)
            time.sleep(0.2)

            self.logger.debug(f"点击位置: ({x}, {y}) - By @ConceptualGod")
            return True

        except Exception as e:
            self.logger.error(f"点击操作异常 - By @ConceptualGod: {str(e)}")
            return False

    def _input_text(self, text: str) -> bool:
        """
        输入文本

        Args:
            text: 要输入的文本

        Returns:
            是否输入成功

        开发者: @ConceptualGod
        """
        try:
            # 根据上一个账号名长度确定退格键次数
            if self.last_logged_username:
                # 计算上一个账号名长度
                last_username_length = len(self.last_logged_username)
                # 多删除10个字符确保完全清空
                total_backspace = last_username_length + 10
                self.logger.debug(f"根据上一个账号 {self.last_logged_username} 长度({last_username_length})，退格{total_backspace}次 - By @ConceptualGod")
            else:
                # 如果没有上一个账号记录，默认删除20次
                total_backspace = 20
                self.logger.debug(f"没有上一个账号记录，默认退格{total_backspace}次 - By @ConceptualGod")

            # 快速清空输入框：先全选再删除
            pyautogui.hotkey('ctrl', 'a')  # 全选
            time.sleep(0.1)
            pyautogui.press('delete')  # 删除
            time.sleep(0.1)

            # 通过剪贴板快速输入文本（支持中文）
            if not self.automation.input_text_via_clipboard(text):
                # 如果剪贴板输入失败，尝试直接输入
                pyautogui.write(text, interval=0.01)  # 加快输入速度

            time.sleep(0.1)  # 减少等待时间
            self.logger.debug(f"输入文本完成 - By @ConceptualGod")
            return True

        except Exception as e:
            self.logger.error(f"输入文本异常 - By @ConceptualGod: {str(e)}")
            return False

    def _wait_for_login_result(self) -> bool:
        """
        等待登录结果，通过检测窗口标题变化判断登录状态

        Returns:
            是否登录成功

        开发者: @ConceptualGod
        """
        try:
            self._update_current_status("等待登录结果...")
            self._log_status("开始等待登录结果，检测窗口标题变化 - By @ConceptualGod")

            # 等待15秒，期间检测窗口标题变化
            max_wait_time = 15  # 等待15秒
            check_interval = 1  # 每1秒检测一次

            for i in range(max_wait_time):
                if not self.is_running:
                    return False

                # 重新检测游戏窗口状态
                if self.automation.detect_game_window():
                    # 检查是否已经登录（窗口标题变化）
                    if self.automation.is_logged_in:
                        self._update_current_status("登录成功")
                        self._log_status(f"检测到登录成功，窗口标题: {self.automation.game_window_title} - By @ConceptualGod")
                        return True
                    else:
                        # 仍在登录界面，继续等待
                        self._update_current_status(f"等待登录结果... ({i+1}/{max_wait_time}秒)")
                        self.logger.debug(f"仍在登录界面，继续等待... ({i+1}/{max_wait_time}秒) - By @ConceptualGod")
                else:
                    # 窗口消失或检测失败
                    self.logger.warning(f"窗口检测失败 ({i+1}/{max_wait_time}秒) - By @ConceptualGod")

                time.sleep(check_interval)

            # 超时未检测到登录成功
            self._update_current_status("登录超时")
            self.logger.warning("登录等待超时，未检测到窗口标题变化 - By @ConceptualGod")
            return False

        except Exception as e:
            self.logger.error(f"等待登录结果异常 - By @ConceptualGod: {str(e)}")
            self._update_current_status("登录检测异常")
            return False

    def _wait_for_login_result_multiple(self, account_index: int) -> bool:
        """
        多号登录专用的等待登录结果方法（包含网络延迟等待）

        Args:
            account_index: 账号索引

        Returns:
            是否登录成功

        开发者: @ConceptualGod
        """
        try:
            self._update_current_status(f"第{account_index}个账号 - 等待登录结果...")
            self._log_status(f"第{account_index}个账号开始等待登录结果，包含15秒网络延迟等待 - By @ConceptualGod")

            # 等待15秒防止网络延迟
            network_wait_time = 15
            check_interval = 1

            for i in range(network_wait_time):
                if not self.is_running:
                    return False

                # 重新检测游戏窗口状态
                if self.automation.detect_game_window():
                    # 记录详细的窗口信息用于调试
                    self.logger.debug(f"第{account_index}个账号窗口检测成功，标题: '{self.automation.game_window_title}', 登录状态: {self.automation.is_logged_in} - By @ConceptualGod")

                    # 检查是否已经登录（窗口标题变化）
                    if self.automation.is_logged_in:
                        self._update_current_status(f"第{account_index}个账号 - 登录成功")
                        self._log_status(f"第{account_index}个账号检测到登录成功，窗口标题: '{self.automation.game_window_title}' - By @ConceptualGod")

                        # 登录成功后等待20秒再执行游戏操作
                        game_wait_time = 20
                        self._log_status(f"第{account_index}个账号登录成功，等待{game_wait_time}秒后开始游戏操作 - By @ConceptualGod")
                        self._update_current_status(f"第{account_index}个账号 - 等待游戏界面稳定...")

                        for j in range(game_wait_time):
                            if not self.is_running:
                                return False
                            self._update_current_status(f"第{account_index}个账号 - 等待游戏界面稳定... ({j+1}/{game_wait_time}秒)")
                            time.sleep(1)

                        return True
                    else:
                        # 仍在登录界面，继续等待
                        self._update_current_status(f"第{account_index}个账号 - 等待登录结果... ({i+1}/{network_wait_time}秒)")
                        self.logger.debug(f"第{account_index}个账号仍在登录界面，窗口标题: '{self.automation.game_window_title}', 继续等待... ({i+1}/{network_wait_time}秒) - By @ConceptualGod")
                else:
                    # 窗口消失或检测失败
                    self.logger.warning(f"第{account_index}个账号窗口检测失败 ({i+1}/{network_wait_time}秒) - By @ConceptualGod")

                time.sleep(check_interval)

            # 超时未检测到登录成功
            self._update_current_status(f"第{account_index}个账号 - 登录超时")
            self.logger.warning(f"第{account_index}个账号登录等待超时，未检测到窗口标题变化 - By @ConceptualGod")
            return False

        except Exception as e:
            self.logger.error(f"第{account_index}个账号等待登录结果异常 - By @ConceptualGod: {str(e)}")
            self._update_current_status(f"第{account_index}个账号 - 登录检测异常")
            return False

    def _execute_game_operations_for_multiple(self, username: str, account_index: int):
        """
        为多号登录执行游戏内操作

        Args:
            username: 当前账号用户名
            account_index: 账号索引

        开发者: @ConceptualGod
        """
        try:
            self._log_status(f"第{account_index}个账号 {username} 开始执行游戏内操作 - By @ConceptualGod")
            self._update_current_status(f"第{account_index}个账号 - 执行游戏内操作...")

            # 保存断点：游戏内操作开始

            # 执行游戏任务前再等待10秒确保界面完全稳定
            pre_game_wait_time = 10
            self._log_status(f"第{account_index}个账号执行游戏任务前，再等待{pre_game_wait_time}秒确保界面完全稳定 - By @ConceptualGod")

            for i in range(pre_game_wait_time):
                if not self.is_running:
                    return
                self._update_current_status(f"第{account_index}个账号 - 游戏任务前等待... ({i+1}/{pre_game_wait_time}秒)")
                time.sleep(1)

            # 保存断点：开始coordinates_1.json操作

            # 执行 coordinates_1.json 操作
            if self._execute_coordinate_file_for_multiple("coordinates_1.json", "游戏内任务操作", account_index):
                self._log_status(f"第{account_index}个账号 {username} 游戏内任务操作完成 - By @ConceptualGod")

                # 等待操作完成
                time.sleep(1)

                # 保存断点：开始close.json操作

                # 执行 close.json 操作
                if self._execute_coordinate_file_for_multiple("close.json", "关闭界面操作", account_index):
                    self._log_status(f"第{account_index}个账号 {username} 关闭界面操作完成 - By @ConceptualGod")

                    # 等待关闭操作完成
                    time.sleep(1)

                    # 保存断点：开始coordinates_2.json操作

                    # 执行 coordinates_2.json 操作
                    if self._execute_coordinate_file_for_multiple("coordinates_2.json", "游戏内任务操作2", account_index):
                        self._log_status(f"第{account_index}个账号 {username} 游戏内任务操作2完成 - By @ConceptualGod")

                        # 等待操作完成
                        time.sleep(1)

                        # 步骤9：集成功能1 - 任务识别
                        self._log_status(f"第{account_index}个账号 {username} 开始战功任务识别 - By @ConceptualGod")
                        recommended_hero = self._execute_integrated_task_recognition(username, account_index)

                        # 步骤10：集成功能2 - 开始游戏（包含完整游戏内操作）
                        self._log_status(f"第{account_index}个账号 {username} 开始游戏启动流程 - By @ConceptualGod")
                        # 游戏启动包含：coordinates_3.json → 英雄选择 → 魂玉搭配 → 游戏内操作(30分钟) → 游戏结束检测
                        game_result = self._execute_integrated_game_starter(username, account_index, recommended_hero)

                        # 步骤11：游戏结束后流程 - 战功任务进度监控
                        self._log_status(f"第{account_index}个账号 {username} 游戏结束，开始战功任务进度监控 - By @ConceptualGod")
                        has_completed_tasks = self._execute_integrated_progress_monitor(username, account_index)

                        # 步骤12：判断是否需要换号
                        if has_completed_tasks:
                            self._log_status(f"第{account_index}个账号 {username} 检测到已完成的战功任务，自动领取奖励 - By @ConceptualGod")
                            self._log_status(f"第{account_index}个账号 {username} 继续当前账号，重新开始游戏流程 - By @ConceptualGod")
                            # 重新开始游戏流程（回到步骤8：战功操作）
                            # 递归调用重新开始游戏流程
                            self._restart_game_flow_for_account(username, account_index)
                            return  # 结束当前流程
                        else:
                            self._log_status(f"第{account_index}个账号 {username} 无对应战功任务，准备换号 - By @ConceptualGod")

                            # 步骤13：换号前领取任务大厅奖励
                            self._log_status(f"第{account_index}个账号 {username} 换号前领取任务大厅奖励 - By @ConceptualGod")
                            if self._execute_coordinate_file_for_multiple("coordinates_1.json", "任务大厅奖励领取", account_index):
                                self._log_status(f"第{account_index}个账号 {username} 任务大厅奖励领取完成 - By @ConceptualGod")

                        # 执行 exit.json 操作
                        if self._execute_coordinate_file_for_multiple("exit.json", "退出账号操作", account_index):
                            self._log_status(f"第{account_index}个账号 {username} 退出账号操作完成 - By @ConceptualGod")

                            # 等待退出完成，检测回到登录平台
                            self._wait_for_logout_to_login_platform(username, account_index)
                        else:
                            self._log_status(f"第{account_index}个账号 {username} 退出账号操作失败 - By @ConceptualGod")
                    else:
                        self._log_status(f"第{account_index}个账号 {username} 游戏内任务操作2失败 - By @ConceptualGod")
                else:
                    self._log_status(f"第{account_index}个账号 {username} 关闭界面操作失败 - By @ConceptualGod")
            else:
                self._log_status(f"第{account_index}个账号 {username} 游戏内任务操作失败 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"第{account_index}个账号游戏操作异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"第{account_index}个账号 {username} 游戏操作异常: {str(e)} - By @ConceptualGod")
            # 保存异常断点





    def _continue_task_completion_flow(self, username: str, account_index: int, has_completed_tasks: bool):
        """
        继续任务完成后的流程处理

        开发者: @ConceptualGod
        """
        try:
            # 判断是否需要换号
            if has_completed_tasks:
                self._log_status(f"第{account_index}个账号 {username} 检测到已完成的战功任务，自动领取奖励 - By @ConceptualGod")
                self._log_status(f"第{account_index}个账号 {username} 继续当前账号，重新开始游戏流程 - By @ConceptualGod")
                # 重新开始游戏流程（回到步骤8：战功操作）
                self._restart_game_flow_for_account(username, account_index)
                return  # 结束当前流程
            else:
                self._log_status(f"第{account_index}个账号 {username} 无对应战功任务，准备换号 - By @ConceptualGod")

                # 换号前领取任务大厅奖励
                self._log_status(f"第{account_index}个账号 {username} 换号前领取任务大厅奖励 - By @ConceptualGod")
                if self._execute_coordinate_file_for_multiple("coordinates_1.json", "任务大厅奖励领取", account_index):
                    self._log_status(f"第{account_index}个账号 {username} 任务大厅奖励领取完成 - By @ConceptualGod")

                # 执行退出操作
                if self._execute_coordinate_file_for_multiple("exit.json", "退出账号操作", account_index):
                    self._log_status(f"第{account_index}个账号 {username} 退出账号操作完成 - By @ConceptualGod")
                    # 等待退出完成，检测回到登录平台
                    self._wait_for_logout_to_login_platform(username, account_index)
                else:
                    self._log_status(f"第{account_index}个账号 {username} 退出账号操作失败 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"第{account_index}个账号任务完成流程异常: {str(e)} - By @ConceptualGod")





    def _restart_game_flow_for_account(self, username: str, account_index: int):
        """
        重新开始游戏流程（当检测到已完成的战功任务时）

        Args:
            username: 当前账号用户名
            account_index: 账号索引

        开发者: @ConceptualGod
        """
        try:
            self._log_status(f"第{account_index}个账号 {username} 重新开始游戏流程 - By @ConceptualGod")

            # 重新执行战功操作（步骤8：coordinates_2.json）
            if self._execute_coordinate_file_for_multiple("coordinates_2.json", "重新战功操作", account_index):
                self._log_status(f"第{account_index}个账号 {username} 重新战功操作完成 - By @ConceptualGod")

                # 重新执行任务识别
                recommended_hero = self._execute_integrated_task_recognition(username, account_index)

                # 重新执行游戏启动
                game_result = self._execute_integrated_game_starter(username, account_index, recommended_hero)

                # 重新执行战功监控
                has_completed_tasks = self._execute_integrated_progress_monitor(username, account_index)

                # 如果还有完成的任务，可以再次重新开始（最多重试3次）
                if has_completed_tasks and not hasattr(self, '_restart_count'):
                    self._restart_count = 1
                    self._restart_game_flow_for_account(username, account_index)
                elif has_completed_tasks and self._restart_count < 3:
                    self._restart_count += 1
                    self._restart_game_flow_for_account(username, account_index)
                else:
                    # 重试次数用完或无更多任务，结束重新开始流程
                    if hasattr(self, '_restart_count'):
                        delattr(self, '_restart_count')
                    self._log_status(f"第{account_index}个账号 {username} 重新开始游戏流程完成 - By @ConceptualGod")
            else:
                self._log_status(f"第{account_index}个账号 {username} 重新战功操作失败 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"第{account_index}个账号重新开始游戏流程异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"第{account_index}个账号 {username} 重新开始游戏流程异常: {str(e)} - By @ConceptualGod")

    def _execute_game_operations(self, username: str):
        """
        执行游戏内操作

        Args:
            username: 当前账号用户名

        开发者: @ConceptualGod
        """
        try:
            self._log_status(f"账号 {username} 开始执行游戏内操作 - By @ConceptualGod")
            self._update_current_status("执行游戏内操作...")

            # 等待游戏界面稳定
            time.sleep(2)

            # 执行 coordinates_1.json 操作
            if self._execute_coordinate_file("coordinates_1.json", "游戏内任务操作"):
                self._log_status(f"账号 {username} 游戏内任务操作完成 - By @ConceptualGod")

                # 等待操作完成
                time.sleep(1)

                # 执行 exit.json 操作
                if self._execute_coordinate_file("exit.json", "退出账号操作"):
                    self._log_status(f"账号 {username} 退出账号操作完成 - By @ConceptualGod")
                else:
                    self._log_status(f"账号 {username} 退出账号操作失败 - By @ConceptualGod")
            else:
                self._log_status(f"账号 {username} 游戏内任务操作失败 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"执行游戏操作异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"账号 {username} 游戏操作异常: {str(e)} - By @ConceptualGod")

    def _execute_coordinate_file(self, filename: str, operation_name: str) -> bool:
        """
        执行指定的坐标文件操作

        Args:
            filename: 坐标文件名
            operation_name: 操作名称

        Returns:
            是否执行成功

        开发者: @ConceptualGod
        """
        try:
            # 加载坐标文件
            coordinates = self._load_coordinate_file(filename)
            if not coordinates:
                self._log_status(f"加载坐标文件失败: {filename} - By @ConceptualGod")
                return False

            self._log_status(f"开始执行 {operation_name} (文件: {filename}) - By @ConceptualGod")
            self._update_current_status(f"执行{operation_name}...")

            # 逐步执行坐标操作
            for i, coord in enumerate(coordinates):
                if not self.is_running:
                    self._log_status(f"{operation_name} 被用户中断 - By @ConceptualGod")
                    return False

                step = coord.get("step", i+1)
                x = coord.get("x", 0)
                y = coord.get("y", 0)
                description = coord.get("description", "")

                self._update_current_status(f"{operation_name} - 第{step}步: {description}")
                self._log_status(f"第{step}步: {description} - 坐标({x}, {y}) - By @ConceptualGod")

                # 执行点击操作
                pyautogui.click(x, y)

                # 记录操作日志
                self._log_status(f"{operation_name} - 第{step}步执行完成: {description} - 坐标({x}, {y}) - By @ConceptualGod")

                # 步骤间隔
                time.sleep(1)

            self._log_status(f"{operation_name} 全部步骤执行完成 - By @ConceptualGod")
            return True

        except Exception as e:
            self.logger.error(f"执行坐标文件异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"{operation_name} 执行异常: {str(e)} - By @ConceptualGod")
            return False

    def _load_coordinate_file(self, filename: str) -> List[Dict[str, Any]]:
        """
        加载坐标文件

        Args:
            filename: 坐标文件名

        Returns:
            坐标列表

        开发者: @ConceptualGod
        """
        try:
            file_path = os.path.join(os.path.dirname(__file__), '..', filename)

            if not os.path.exists(file_path):
                self.logger.error(f"坐标文件不存在: {file_path} - By @ConceptualGod")
                return []

            with open(file_path, 'r', encoding='utf-8') as f:
                coordinates = json.load(f)

            self._log_status(f"成功加载坐标文件: {filename}, 共{len(coordinates)}个步骤 - By @ConceptualGod")
            return coordinates

        except Exception as e:
            self.logger.error(f"加载坐标文件异常 - By @ConceptualGod: {str(e)}")
            return []

    def _execute_coordinate_file_for_multiple(self, filename: str, operation_name: str, account_index: int) -> bool:
        """
        为多号登录执行指定的坐标文件操作

        Args:
            filename: 坐标文件名
            operation_name: 操作名称
            account_index: 账号索引

        Returns:
            是否执行成功

        开发者: @ConceptualGod
        """
        try:
            # 加载坐标文件
            coordinates = self._load_coordinate_file(filename)
            if not coordinates:
                self._log_status(f"第{account_index}个账号: 加载坐标文件失败: {filename} - By @ConceptualGod")
                return False

            self._log_status(f"第{account_index}个账号开始执行 {operation_name} (文件: {filename}) - By @ConceptualGod")
            self._log_status(f"执行状态检查: is_running={self.is_running} - By @ConceptualGod")
            self._update_current_status(f"第{account_index}个账号 - {operation_name}...")

            # 逐步执行坐标操作
            for i, coord in enumerate(coordinates):
                if not self.is_running:
                    self._log_status(f"第{account_index}个账号 {operation_name} 被用户中断 (is_running={self.is_running}) - By @ConceptualGod")
                    self._log_status(f"中断原因检查: 可能按了END键或点击了停止按钮 - By @ConceptualGod")
                    return False

                step = coord.get("step", i+1)
                x = coord.get("x", 0)
                y = coord.get("y", 0)
                description = coord.get("description", "")

                self._update_current_status(f"第{account_index}个账号 {operation_name} - 第{step}步: {description}")
                self._log_status(f"第{account_index}个账号 第{step}步: {description} - 坐标({x}, {y}) - By @ConceptualGod")

                # 执行点击操作
                pyautogui.click(x, y)

                # 记录操作日志
                self._log_status(f"第{account_index}个账号 {operation_name} - 第{step}步执行完成: {description} - 坐标({x}, {y}) - By @ConceptualGod")

                # 步骤间隔 - 战功操作需要更长等待时间
                if filename == "coordinates_2.json":
                    # 战功操作每步等待10秒
                    wait_time = 10
                    self._log_status(f"第{account_index}个账号 战功操作等待{wait_time}秒... - By @ConceptualGod")
                    for j in range(wait_time):
                        if not self.is_running:
                            return False
                        self._update_current_status(f"第{account_index}个账号 战功操作等待... ({j+1}/{wait_time}秒)")
                        time.sleep(1)
                else:
                    # 其他操作保持1秒间隔
                    time.sleep(1)

            self._log_status(f"第{account_index}个账号 {operation_name} 全部步骤执行完成 - By @ConceptualGod")
            return True

        except Exception as e:
            self.logger.error(f"第{account_index}个账号执行坐标文件异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"第{account_index}个账号 {operation_name} 执行异常: {str(e)} - By @ConceptualGod")
            return False

    def _execute_specific_steps_from_json(self, filename: str, steps_to_execute: List[int], operation_name: str) -> bool:
        """
        执行JSON文件中的特定步骤

        Args:
            filename: JSON文件名
            steps_to_execute: 要执行的步骤列表，如[1, 4, 3]
            operation_name: 操作名称

        Returns:
            bool: 执行是否成功

        开发者: @ConceptualGod
        """
        try:
            self._log_status(f"开始执行{operation_name} (步骤: {steps_to_execute}) - By @ConceptualGod")

            if not os.path.exists(filename):
                self._log_status(f"✗ 配置文件不存在: {filename} - By @ConceptualGod")
                return False

            # 读取JSON配置
            with open(filename, 'r', encoding='utf-8') as f:
                coordinates = json.load(f)

            if not coordinates:
                self._log_status(f"✗ 配置文件为空: {filename} - By @ConceptualGod")
                return False

            # 创建步骤映射
            step_map = {}
            for coord in coordinates:
                step_map[coord.get('step')] = coord

            # 执行指定步骤
            for step_num in steps_to_execute:
                if step_num not in step_map:
                    self._log_status(f"✗ 步骤{step_num}不存在于{filename}中 - By @ConceptualGod")
                    return False

                coord = step_map[step_num]
                x = coord.get('x')
                y = coord.get('y')
                description = coord.get('description', f'步骤{step_num}')

                if x is None or y is None:
                    self._log_status(f"✗ 步骤{step_num}坐标无效 - By @ConceptualGod")
                    return False

                # 执行点击
                self._log_status(f"执行步骤{step_num}: {description} ({x}, {y}) - By @ConceptualGod")
                pyautogui.click(x, y)
                time.sleep(0.8)  # 每步之间等待0.8秒

            self._log_status(f"✓ {operation_name}执行完成 - By @ConceptualGod")
            return True

        except Exception as e:
            self._log_status(f"✗ 执行{operation_name}失败: {str(e)} - By @ConceptualGod")
            return False

    def _wait_for_logout_to_login_platform(self, username: str, account_index: int):
        """
        等待退出到登录平台（包含退出后的等待时间）

        Args:
            username: 当前账号用户名
            account_index: 账号索引

        开发者: @ConceptualGod
        """
        try:
            self._log_status(f"第{account_index}个账号 {username} 开始等待退出到登录平台... - By @ConceptualGod")
            self._update_current_status(f"第{account_index}个账号 - 等待退出到登录平台...")

            # 先等待5-6秒让退出操作完成
            logout_wait_time = 6
            self._log_status(f"第{account_index}个账号退出操作完成，等待{logout_wait_time}秒后检测登录平台 - By @ConceptualGod")

            for i in range(logout_wait_time):
                if not self.is_running:
                    return
                self._update_current_status(f"第{account_index}个账号 - 等待退出完成... ({i+1}/{logout_wait_time}秒)")
                time.sleep(1)

            # 然后检测是否回到登录平台
            max_detect_time = 20  # 最多检测20秒
            check_interval = 1

            self._log_status(f"第{account_index}个账号开始检测是否回到登录平台 - By @ConceptualGod")

            for i in range(max_detect_time):
                if not self.is_running:
                    return

                # 检测游戏窗口状态
                if self.automation.detect_game_window():
                    # 检查是否回到登录平台（窗口标题包含发布时间）
                    if not self.automation.is_logged_in:
                        self._log_status(f"第{account_index}个账号 {username} 已检测到退出到登录平台，窗口标题: {self.automation.game_window_title} - By @ConceptualGod")

                        # 检测到退出后，再等待15秒让界面完全稳定
                        platform_stable_wait = 15
                        self._log_status(f"第{account_index}个账号检测到退出成功，再等待{platform_stable_wait}秒让登录平台界面完全稳定 - By @ConceptualGod")

                        for j in range(platform_stable_wait):
                            if not self.is_running:
                                return
                            self._update_current_status(f"第{account_index}个账号 - 登录平台界面稳定等待... ({j+1}/{platform_stable_wait}秒)")
                            time.sleep(1)

                        self._log_status(f"第{account_index}个账号 {username} 登录平台界面已完全稳定，可以进行下一步操作 - By @ConceptualGod")
                        self._update_current_status(f"第{account_index}个账号 - 已退出到登录平台")
                        return
                    else:
                        self._update_current_status(f"第{account_index}个账号 - 检测登录平台... ({i+1}/{max_detect_time}秒)")
                        self.logger.debug(f"第{account_index}个账号仍在游戏界面，继续检测... ({i+1}/{max_detect_time}秒) - By @ConceptualGod")
                else:
                    self.logger.warning(f"第{account_index}个账号窗口检测失败 ({i+1}/{max_detect_time}秒) - By @ConceptualGod")

                time.sleep(check_interval)

            self._log_status(f"第{account_index}个账号 {username} 退出检测超时 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"第{account_index}个账号等待退出异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"第{account_index}个账号 {username} 等待退出异常: {str(e)} - By @ConceptualGod")

    def _prepare_next_account(self, next_username: str):
        """
        准备下一个账号登录（清除上一个账号输入框内容）

        Args:
            next_username: 下一个账号用户名

        开发者: @ConceptualGod
        """
        try:
            # 使用记录的上一个账号名
            last_username = self.last_logged_username
            self._log_status(f"准备下一个账号: 清除上一个账号 {last_username}，准备输入 {next_username} - By @ConceptualGod")
            self._update_current_status("准备下一个账号...")

            # 等待界面稳定
            time.sleep(1)

            # 获取账号输入框坐标（login.json的第一步）
            if self.login_coordinates and len(self.login_coordinates) > 0:
                username_coord = self.login_coordinates[0]  # 第一步是账号输入框
                x = username_coord.get("x", 0)
                y = username_coord.get("y", 0)

                self._log_status(f"定位账号输入框 - 坐标({x}, {y}) - By @ConceptualGod")

                # 点击账号输入框
                pyautogui.click(x, y)
                time.sleep(0.5)

                # 清除上一个账号内容
                self._clear_username_input_with_backspace(last_username)

                self._log_status(f"账号输入框已清除，准备输入下一个账号 {next_username} - By @ConceptualGod")
            else:
                self._log_status("未找到账号输入框坐标配置 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"准备下一个账号异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"准备下一个账号异常: {str(e)} - By @ConceptualGod")

    def _clear_username_input_with_backspace(self, username: str):
        """
        使用退格键清除账号输入框内容

        Args:
            username: 要清除的用户名

        开发者: @ConceptualGod
        """
        try:
            self._log_status(f"开始使用退格键清除账号输入框内容: {username} - By @ConceptualGod")

            # 计算用户名长度
            username_length = len(username)
            self._log_status(f"账号 {username} 长度为 {username_length} 个字符 - By @ConceptualGod")

            # 使用退格键逐个删除字符
            total_backspace = username_length + 15  # 多删除15个字符确保完全清空
            for i in range(total_backspace):
                pyautogui.press('backspace')
                time.sleep(0.15)  # 每次按键间隔
                self.logger.debug(f"退格键第 {i+1} 次，剩余约 {max(0, username_length - i)} 个字符 - By @ConceptualGod")

            self._log_status(f"账号输入框内容已使用退格键清除完成 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"使用退格键清除账号输入框异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"使用退格键清除账号输入框异常: {str(e)} - By @ConceptualGod")

    def _stop_login(self):
        """停止轮登"""
        self.is_running = False
        self._log_status("用户停止轮登")
        self._update_current_status("正在停止...")

    def _update_button_state(self):
        """更新按钮状态"""
        if self.is_running:
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
        else:
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
    
    def _update_current_status(self, status: str):
        """更新当前状态"""
        self.current_status_label.config(text=f"状态: {status}")
    
    def _log_status(self, message: str):
        """记录状态日志"""
        # 优先使用回调函数（确保GUI和命令行日志一致）
        if self.log_callback:
            self.log_callback(message)
        else:
            # 如果没有回调，使用本地显示（仅当status_text存在时）
            if hasattr(self, 'status_text') and self.status_text:
                timestamp = time.strftime("%H:%M:%S")
                log_message = f"[{timestamp}] {message}\n"

                # 更新状态文本框
                self.status_text.config(state=tk.NORMAL)
                self.status_text.insert(tk.END, log_message)
                self.status_text.see(tk.END)
                self.status_text.config(state=tk.DISABLED)

            # 记录到日志文件
            self.logger.info(message)

    def _load_login_coordinates(self):
        """
        加载登录坐标配置
        开发者: @ConceptualGod
        """
        try:
            if os.path.exists(self.login_config_file):
                with open(self.login_config_file, 'r', encoding='utf-8') as f:
                    self.login_coordinates = json.load(f)

                self._log_status(f"加载登录坐标配置成功，共 {len(self.login_coordinates)} 个坐标 - By @ConceptualGod")

                # 按步骤排序
                self.login_coordinates.sort(key=lambda x: x.get("step", 0))

            else:
                self.logger.warning(f"登录坐标配置文件不存在: {self.login_config_file} - By @ConceptualGod")
                self.login_coordinates = []

        except Exception as e:
            self.logger.error(f"加载登录坐标配置失败: {str(e)} - By @ConceptualGod")
            self.login_coordinates = []

    def _execute_integrated_progress_monitor(self, username: str, account_index: int) -> bool:
        """
        执行集成的进度监控功能

        Args:
            username: 当前账号用户名
            account_index: 账号索引

        Returns:
            bool: 是否有完成的战功任务

        开发者: @ConceptualGod
        """
        try:
            self._log_status(f"第{account_index}个账号 {username} 开始执行战功任务进度监控 - By @ConceptualGod")

            # 调用主窗口的进度监控回调
            if hasattr(self, 'progress_monitor_callback'):
                result = self.progress_monitor_callback()
                if result:
                    self._log_status(f"第{account_index}个账号 {username} 检测到已完成的战功任务 - By @ConceptualGod")
                    return True
                else:
                    self._log_status(f"第{account_index}个账号 {username} 未检测到已完成的战功任务 - By @ConceptualGod")
                    return False
            else:
                self._log_status(f"第{account_index}个账号 {username} 进度监控回调未设置 - By @ConceptualGod")
                return False

        except Exception as e:
            self.logger.error(f"第{account_index}个账号进度监控异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"第{account_index}个账号 {username} 进度监控异常: {str(e)} - By @ConceptualGod")
            return False

    def _execute_integrated_task_recognition(self, username: str, account_index: int) -> str:
        """
        执行集成的任务识别功能

        Args:
            username: 当前账号用户名
            account_index: 账号索引

        Returns:
            str: 推荐的英雄名称

        开发者: @ConceptualGod
        """
        try:
            self._log_status(f"第{account_index}个账号 {username} 开始执行战功任务识别 - By @ConceptualGod")

            # 调用主窗口的任务识别回调
            if hasattr(self, 'task_recognition_callback'):
                recommended_hero = self.task_recognition_callback()
                if recommended_hero:
                    self._log_status(f"第{account_index}个账号 {username} 任务识别成功，推荐英雄: {recommended_hero} - By @ConceptualGod")
                    return recommended_hero
                else:
                    self._log_status(f"第{account_index}个账号 {username} 任务识别失败，使用默认英雄华佗 - By @ConceptualGod")
                    return "华佗"
            else:
                self._log_status(f"第{account_index}个账号 {username} 任务识别回调未设置，使用默认英雄华佗 - By @ConceptualGod")
                return "华佗"

        except Exception as e:
            self.logger.error(f"第{account_index}个账号任务识别异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"第{account_index}个账号 {username} 任务识别异常，使用默认英雄华佗: {str(e)} - By @ConceptualGod")
            return "华佗"

    def _execute_integrated_game_starter(self, username: str, account_index: int, recommended_hero: str):
        """
        执行集成的游戏启动功能（包含完整游戏流程）

        Args:
            username: 当前账号用户名
            account_index: 账号索引
            recommended_hero: 推荐的英雄名称

        开发者: @ConceptualGod
        """
        try:
            self._log_status(f"第{account_index}个账号 {username} 开始执行游戏启动流程，推荐英雄: {recommended_hero} - By @ConceptualGod")

            # 检查调试模式状态
            debug_mode_status = hasattr(self, 'debug_mode') and self.debug_mode
            self._log_status(f"第{account_index}个账号 {username} 调试模式状态: {debug_mode_status} - By @ConceptualGod")

            # 调试模式下跳过实际游戏启动，避免卡死
            if debug_mode_status:
                self._log_status(f"第{account_index}个账号 {username} 调试模式：跳过实际游戏启动 - By @ConceptualGod")
                time.sleep(2)  # 模拟游戏启动时间
                return True

            # 检查游戏启动回调状态
            has_callback = hasattr(self, 'game_starter_callback')
            self._log_status(f"第{account_index}个账号 {username} 游戏启动回调状态: {has_callback} - By @ConceptualGod")

            # 调用主窗口的游戏启动回调
            if has_callback:
                self._log_status(f"第{account_index}个账号 {username} 开始调用游戏启动回调 - By @ConceptualGod")
                # 传递当前账号信息和推荐英雄
                result = self.game_starter_callback(username, recommended_hero)
                self._log_status(f"第{account_index}个账号 {username} 游戏启动回调返回结果: {result} - By @ConceptualGod")

                if result:
                    self._log_status(f"第{account_index}个账号 {username} 完整游戏流程执行成功 - By @ConceptualGod")
                    return result
                else:
                    self._log_status(f"第{account_index}个账号 {username} 完整游戏流程执行失败 - By @ConceptualGod")
                    return False
            else:
                self._log_status(f"第{account_index}个账号 {username} 游戏启动回调未设置 - By @ConceptualGod")
                return False

        except Exception as e:
            self.logger.error(f"第{account_index}个账号游戏启动异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"第{account_index}个账号 {username} 游戏启动异常: {str(e)} - By @ConceptualGod")
            return False

    def _execute_integrated_game_operation(self, username: str, account_index: int):
        """
        执行集成的游戏内操作功能（30分钟智能操作）

        Args:
            username: 当前账号用户名
            account_index: 账号索引

        Returns:
            bool: 游戏内操作是否成功

        开发者: @ConceptualGod
        """
        try:
            self._log_status(f"第{account_index}个账号 {username} 开始执行30分钟游戏内智能操作 - By @ConceptualGod")

            # 调试模式下跳过实际游戏内操作
            debug_mode_status = hasattr(self, 'debug_mode') and self.debug_mode
            if debug_mode_status:
                self._log_status(f"第{account_index}个账号 {username} 调试模式：跳过实际游戏内操作 - By @ConceptualGod")
                time.sleep(5)  # 模拟游戏内操作时间
                return True

            # 检查游戏内操作回调状态
            has_callback = hasattr(self, 'game_operation_callback')
            self._log_status(f"第{account_index}个账号 {username} 游戏内操作回调状态: {has_callback} - By @ConceptualGod")

            # 调用主窗口的游戏内操作回调
            if has_callback:
                self._log_status(f"第{account_index}个账号 {username} 开始调用游戏内操作回调 - By @ConceptualGod")
                result = self.game_operation_callback(username)
                self._log_status(f"第{account_index}个账号 {username} 游戏内操作回调返回结果: {result} - By @ConceptualGod")

                if result:
                    self._log_status(f"第{account_index}个账号 {username} 30分钟游戏内操作执行成功 - By @ConceptualGod")
                    return result
                else:
                    self._log_status(f"第{account_index}个账号 {username} 30分钟游戏内操作执行失败 - By @ConceptualGod")
                    return False
            else:
                self._log_status(f"第{account_index}个账号 {username} 游戏内操作回调未设置 - By @ConceptualGod")
                return False

        except Exception as e:
            self.logger.error(f"第{account_index}个账号游戏内操作异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"第{account_index}个账号 {username} 游戏内操作异常: {str(e)} - By @ConceptualGod")
            return False

    def _emergency_stop(self):
        """
        紧急停止所有操作

        开发者: @ConceptualGod
        """
        try:
            self.logger.warning("触发紧急停止 - By @ConceptualGod")
            self._log_status("=== 紧急停止触发 === - By @ConceptualGod")

            # 停止所有运行状态
            self.is_running = False

            # 强制中断登录线程
            if self.login_thread and self.login_thread.is_alive():
                self._log_status("强制中断登录线程 - By @ConceptualGod")
                # 注意：Python线程无法强制终止，只能通过is_running标志优雅停止
                # 线程会在下一次检查is_running时自动退出

            # 重置当前账号状态
            self.current_account = None

            # 停止游戏内操作（如果有回调）
            if hasattr(self, 'game_operation_stop_callback'):
                try:
                    self.game_operation_stop_callback()
                    self._log_status("游戏内操作已紧急停止 - By @ConceptualGod")
                except Exception as e:
                    self.logger.error(f"停止游戏内操作失败: {str(e)} - By @ConceptualGod")

            # 停止游戏启动控制器（如果有回调）
            if hasattr(self, 'game_starter_stop_callback'):
                try:
                    self.game_starter_stop_callback()
                    self._log_status("游戏启动控制器已紧急停止 - By @ConceptualGod")
                except Exception as e:
                    self.logger.error(f"停止游戏启动控制器失败: {str(e)} - By @ConceptualGod")

            # 停止任务识别和进度监控（如果有回调）
            if hasattr(self, 'task_recognition_stop_callback'):
                try:
                    self.task_recognition_stop_callback()
                    self._log_status("任务识别已紧急停止 - By @ConceptualGod")
                except Exception as e:
                    self.logger.error(f"停止任务识别失败: {str(e)} - By @ConceptualGod")

            if hasattr(self, 'progress_monitor_stop_callback'):
                try:
                    self.progress_monitor_stop_callback()
                    self._log_status("进度监控已紧急停止 - By @ConceptualGod")
                except Exception as e:
                    self.logger.error(f"停止进度监控失败: {str(e)} - By @ConceptualGod")

            # 更新界面状态
            self._update_button_state()
            self._update_current_status("紧急停止")

            self._log_status("=== 所有操作已紧急停止 === - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"紧急停止异常: {str(e)} - By @ConceptualGod")
            self._log_status(f"紧急停止异常: {str(e)} - By @ConceptualGod")

    def _clear_logs(self):
        """
        清空日志

        开发者: @ConceptualGod
        """
        try:
            # 清空状态文本框
            self.status_text.config(state=tk.NORMAL)
            self.status_text.delete('1.0', tk.END)
            self.status_text.config(state=tk.DISABLED)

            # 更新状态
            self._update_current_status("日志已清空")

            # 添加清空日志的记录
            self._log_status("=== 日志已清空 === - By @ConceptualGod")

            self._log_status("用户清空日志 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"清空日志异常: {str(e)} - By @ConceptualGod")
            self._log_status(f"清空日志异常: {str(e)} - By @ConceptualGod")
