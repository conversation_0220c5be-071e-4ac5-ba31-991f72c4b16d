#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试确定按钮双坐标检测功能
开发者: @ConceptualGod
"""

import sys
import os
from pathlib import Path

# 添加当前目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_confirm_button_detection():
    """测试确定按钮检测功能"""
    print("=" * 60)
    print("测试确定按钮双坐标检测功能")
    print("=" * 60)
    
    try:
        from core.game_starter_controller import GameStarterController
        
        # 创建控制器实例
        controller = GameStarterController()
        
        print("\n1. 检查坐标加载情况:")
        print(f"coordinates_3.json坐标: {controller.coordinates}")
        
        if hasattr(controller, 'zhanjibufu_coordinates'):
            print(f"zhanjibufu.json坐标: {controller.zhanjibufu_coordinates}")
        else:
            print("zhanjibufu.json坐标: 未加载")
        
        print("\n2. 测试确定按钮检测方法:")
        
        # 检查coordinates_3.json中的确定按钮
        if "确定按钮" in controller.coordinates:
            coord = controller.coordinates["确定按钮"]
            print(f"✓ coordinates_3确定按钮坐标: ({coord['x']}, {coord['y']})")
        else:
            print("✗ coordinates_3确定按钮坐标未找到")
        
        # 检查zhanjibufu.json中的确定按钮
        if hasattr(controller, 'zhanjibufu_coordinates') and "确定按钮" in controller.zhanjibufu_coordinates:
            coord = controller.zhanjibufu_coordinates["确定按钮"]
            print(f"✓ zhanjibufu确定按钮坐标: ({coord['x']}, {coord['y']})")
        else:
            print("✗ zhanjibufu确定按钮坐标未找到")
        
        print("\n3. 方法可用性检查:")
        
        # 检查detect_confirm_button方法
        if hasattr(controller, 'detect_confirm_button'):
            print("✓ detect_confirm_button方法存在")
        else:
            print("✗ detect_confirm_button方法不存在")
        
        # 检查click_detected_confirm_button方法
        if hasattr(controller, 'click_detected_confirm_button'):
            print("✓ click_detected_confirm_button方法存在")
        else:
            print("✗ click_detected_confirm_button方法不存在")
        
        print("\n4. 坐标文件内容验证:")
        
        # 检查coordinates_3.json
        coord_file = Path("coordinates_3.json")
        if coord_file.exists():
            import json
            with open(coord_file, 'r', encoding='utf-8') as f:
                coords = json.load(f)
            
            step3_found = False
            for coord in coords:
                if coord.get('step') == 3:
                    step3_found = True
                    print(f"✓ coordinates_3.json step3: ({coord.get('x')}, {coord.get('y')}) - {coord.get('description')}")
                    break
            
            if not step3_found:
                print("✗ coordinates_3.json中未找到step3")
        else:
            print("✗ coordinates_3.json文件不存在")
        
        # 检查zhanjibufu.json
        zhanjibufu_file = Path("zhanjibufu.json")
        if zhanjibufu_file.exists():
            with open(zhanjibufu_file, 'r', encoding='utf-8') as f:
                coords = json.load(f)
            
            step1_found = False
            for coord in coords:
                if coord.get('step') == 1:
                    step1_found = True
                    print(f"✓ zhanjibufu.json step1: ({coord.get('x')}, {coord.get('y')}) - {coord.get('description')}")
                    break
            
            if not step1_found:
                print("✗ zhanjibufu.json中未找到step1")
        else:
            print("✗ zhanjibufu.json文件不存在")
        
        print("\n" + "=" * 60)
        print("✅ 确定按钮双坐标检测功能测试完成")
        print("功能说明:")
        print("1. detect_confirm_button() - 检测两个位置的确定按钮")
        print("2. click_detected_confirm_button() - 智能点击检测到的确定按钮")
        print("3. 支持coordinates_3.json和zhanjibufu.json两个坐标")
        print("4. 自动选择有确定文字的按钮进行点击")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    test_confirm_button_detection()

if __name__ == "__main__":
    main()
