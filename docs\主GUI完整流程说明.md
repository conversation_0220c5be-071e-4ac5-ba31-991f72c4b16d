# 起凡自动化脚本主GUI完整流程说明

**开发者:** @ConceptualGod  
**版本:** v2.0 Final  
**创建时间:** 2025-08-05  
**项目:** 起凡游戏自动化脚本

## 📋 主GUI界面概述

起凡自动化脚本主GUI是一个集成化的控制界面，包含所有功能模块的统一管理。用户通过主GUI可以完成从账号管理到游戏自动化的全部操作。

## 🏗️ 主GUI界面结构

### 界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 起凡自动化脚本 - By @ConceptualGod                          │
├─────────────────────────────────────────────────────────────┤
│ 菜单栏: 文件 | 工具 | 帮助                                  │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│ │   账号管理      │ │  多号轮登控制   │ │   状态显示      │ │
│ │                 │ │                 │ │                 │ │
│ │ - 导入账号      │ │ - 账号信息      │ │ - 账号状态      │ │
│ │ - 账号列表      │ │ - 开始轮登      │ │ - 任务进度      │ │
│ │ - 编辑删除      │ │ - 集成功能      │ │ - 实时更新      │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 状态栏: 就绪 | By @ConceptualGod | v1.2                     │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 主要功能模块

### 1. 账号管理模块
**位置:** 主界面左侧  
**功能:** 管理游戏账号数据

#### 界面组件
- **导入账号按钮**: 支持CSV/Excel文件导入
- **账号列表**: 显示所有已导入的账号
- **编辑按钮**: 修改选中账号信息
- **删除按钮**: 删除选中账号
- **清空按钮**: 清空所有账号数据

#### 操作流程
1. 点击"导入账号"按钮
2. 选择CSV或Excel文件（支持中文列名）
3. 系统自动解析并去重
4. 账号显示在列表中
5. 可进行编辑、删除等操作

### 2. 多号轮登控制模块
**位置:** 主界面中央
**功能:** 控制多账号轮换登录和集成功能执行

#### 界面组件
- **账号信息显示**: 显示已导入账号数量
- **刷新账号按钮**: 刷新账号信息显示
- **开始多号轮登按钮**: 开始多账号轮换登录
- **停止按钮**: 停止当前操作
- **状态文本框**: 显示详细的操作日志
- **集成功能选项**: 任务识别、进度监控、游戏启动

#### 操作流程
**多号轮登流程:**
1. 确保已导入多个账号
2. 勾选需要的集成功能
3. 点击"开始多号轮登"
4. 系统按顺序处理每个账号
5. 实时显示当前处理状态
6. 自动循环直到手动停止

### 3. 状态显示模块
**位置:** 主界面右侧  
**功能:** 显示账号状态和任务完成情况

#### 界面组件
- **账号状态表**: 显示每个账号的胜利/失败/逃跑统计
- **任务进度表**: 显示战功任务完成数值
- **自动更新开关**: 控制状态自动刷新
- **立即刷新按钮**: 手动刷新状态
- **清除数据按钮**: 清空所有统计数据

## 🔄 完整操作流程

### 流程1：首次使用设置
1. **启动程序**: 运行gui_main.py
2. **导入账号**:
   - 点击"导入账号"
   - 选择包含账号信息的CSV/Excel文件
   - 确认导入成功
3. **检查设置**: 确认游戏已启动，坐标配置正确

### 流程2：多账号轮登流程
1. **确认账号**: 确保已导入多个账号
2. **选择功能**: 勾选"任务识别"、"进度监控"、"游戏启动"
3. **开始轮登**: 点击"开始多号轮登"
4. **全程监控**: 系统自动处理每个账号
5. **查看统计**: 在状态文本框查看实时日志和账号状态

## 📊 集成功能详解

### 任务识别功能
**作用:** 识别当前战功任务并推荐英雄  
**流程:**
1. OCR扫描战功界面
2. 智能匹配任务类型
3. 推荐适合的英雄
4. 设置给游戏启动模块

### 进度监控功能
**作用:** 监控战功任务完成进度  
**流程:**
1. 扫描11个监控点
2. 检测任务完成状态
3. 自动领取已完成奖励
4. 更新任务完成统计

### 游戏启动功能
**作用:** 自动启动游戏并执行游戏内操作  
**流程:**
1. 智能窗口检测
2. 选择游戏房间
3. 选择推荐英雄
4. 执行魂玉搭配
5. 开始游戏内智能操作

## 🎮 游戏内操作系统

### 三模式智能操作
1. **发育模式**（前20分钟）
   - 安全发育，跟随队友
   - 敌人≥3个时后退
   - 野区规则：蜀国下路，魏国上路

2. **跟随模式**（20分钟后）
   - 跟随MVP最高队友
   - 可进入防御塔范围
   - 主动寻找战斗机会

3. **战斗模式**（遇敌触发）
   - 攻击优先级：英雄>小兵>野怪
   - 600码范围技能释放
   - 1500码安全撤退

4. **撤退模式**（危险触发）
   - 紧急撤退和生存保障
   - 自动回城和复活处理

### 英雄专属技能
- **华佗**: W治疗血量<80%队友，D攻击敌人
- **刘备**: C和E攻击600码内敌人
- **诸葛瑾**: E和W攻击600码内敌人
- **陆逊**: E攻击600码内敌人
- **孙权**: E攻击600码内敌人
- **曹操**: C攻击600码内敌人

### 生存保障系统
- **血量<80%**: 自动使用玄铁盾（快捷键2）
- **血量<40%或蓝量<10%**: 紧急撤退使用奔雷靴（快捷键1）
- **自动回城**: 撤离到安全距离后按Y回城
- **复活处理**: 血量满后自动重新出门

## 🔚 游戏结束后流程

### 完整结束流程
1. **游戏结束检测**: OCR识别胜利/失败界面
2. **状态记录**: 更新账号胜利/失败/逃跑统计
3. **战功监控**: 检查战功任务完成情况
4. **任务判断**:
   - **有完成任务**: 自动领取奖励，继续当前账号
   - **无对应任务**: 执行换号流程
5. **任务大厅奖励**: 换号前领取任务大厅奖励（coordinates_1.json）
6. **账号切换**: 执行exit.json切换下一个账号

### 状态显示更新
- **账号状态**: 实时显示每个账号的胜/败/逃统计
- **任务进度**: 显示战功任务完成数值（如"功高绩伟"30/30）
- **自动更新**: 每5秒自动刷新状态信息

## 📝 日志显示系统

### 日志内容
- **操作进度**: 详细的步骤执行情况
- **状态更新**: 账号状态和任务进度变化
- **错误信息**: 异常情况和错误处理
- **时间戳**: 每条日志都包含精确时间

### 日志格式
```
[HH:MM:SS] 操作内容 - By @ConceptualGod
[HH:MM:SS] 账号状态更新: 用户名: 胜1/败0/逃0 (最近:胜利) - By @ConceptualGod
[HH:MM:SS] 战功任务进度: 功高绩伟: 25/30 (83.3%) - By @ConceptualGod
```

## 🎯 使用建议

### 最佳实践
1. **首次使用**: 先导入少量账号测试多号轮登功能
2. **批量操作**: 确认流程正常后再导入大量账号
3. **监控状态**: 定期查看状态文本框的实时日志
4. **日志查看**: 遇到问题时仔细查看状态文本框的详细日志

### 注意事项
1. **游戏窗口**: 确保游戏已启动并可见
2. **坐标配置**: 确保所有JSON配置文件存在且正确
3. **网络稳定**: 保持网络连接稳定
4. **系统资源**: 确保系统有足够的内存和CPU资源

## 🔧 技术实现细节

### 核心控制器集成
主GUI通过以下核心控制器实现功能：
- **TaskRecognitionController**: 任务识别控制器
- **ProgressMonitorController**: 进度监控控制器
- **GameStarterController**: 游戏启动控制器
- **AccountStatusManager**: 账号状态管理器

### 数据流转机制
```
账号数据 → 登录控制 → 任务识别 → 英雄推荐 → 游戏启动 →
游戏内操作 → 结果检测 → 状态更新 → 进度监控 → 账号切换
```

### 回调函数体系
- **任务识别回调**: 将识别结果传递给游戏启动
- **进度监控回调**: 检测任务完成状态
- **游戏启动回调**: 执行完整游戏流程
- **日志显示回调**: 统一日志输出到GUI

## 📈 性能优化

### 内存管理
- 及时释放OCR识别资源
- 限制日志历史记录数量
- 优化图像处理内存使用

### 响应速度
- 异步执行长时间操作
- 实时更新界面状态
- 智能等待和重试机制

### 稳定性保障
- 完善的异常处理
- 自动重试机制
- 状态恢复功能

## 🛠️ 故障排除

### 常见问题
1. **账号导入失败**: 检查文件格式和编码
2. **任务识别错误**: 确认游戏界面清晰可见
3. **游戏启动失败**: 检查游戏窗口状态
4. **状态更新异常**: 重启程序重新初始化

### 调试方法
1. **查看日志**: 状态文本框显示详细信息
2. **单步测试**: 使用单号登录逐步测试
3. **配置检查**: 确认所有JSON文件正确
4. **系统检查**: 确认系统资源充足

## 📋 配置文件依赖

### 必需配置文件（20个）
- **基础坐标**: login.json, coordinates_1/2/3.json, close.json, exit.json
- **英雄选择**: herochoose.json, querenhero.json, hunyudapei.json
- **游戏内操作**: jinnang.json, chuzhuang.json, jiadianshengmingzhi.json
- **系统配置**: game_params.json, hero_skills.json, hotkeys.json
- **任务系统**: task.json, zhangonghero.json, zhangong.json, zhangongpick.json, zhangongtaskpick.json

### 数据文件
- **accounts.json**: 账号数据存储
- **account_status.json**: 账号状态记录
- **task_completion_stats.json**: 任务完成统计

## 🎉 系统优势

### 用户体验
1. **一键操作**: 导入账号后一键启动全流程
2. **实时反馈**: 详细的进度显示和状态更新
3. **智能化**: 自动识别、自动决策、自动操作
4. **可视化**: 直观的状态统计和进度显示

### 技术优势
1. **模块化**: 清晰的架构，便于维护和扩展
2. **配置化**: 所有参数可通过配置文件调整
3. **智能化**: 基于AI的决策引擎和操作系统
4. **稳定性**: 完善的错误处理和恢复机制

### 功能完整性
1. **全流程覆盖**: 从登录到游戏结束的完整自动化
2. **多账号支持**: 支持批量账号轮换操作
3. **状态监控**: 实时监控账号状态和任务进度
4. **智能决策**: 根据游戏状态自动调整操作策略

---

**开发完成:** 2025-08-05
**开发者:** @ConceptualGod
**状态:** 完整功能实现，用户友好界面
**特点:** 集成化管理，智能化操作，实时状态监控
**总代码量:** 约8000行Python代码
**配置文件:** 20个JSON配置文件
**核心模块:** 15个核心控制器和处理器
