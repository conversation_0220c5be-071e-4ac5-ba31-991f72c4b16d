2025-08-06 06:35:37 - easyocr.easyocr - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-08-06 06:35:47 - root - ERROR - GUI日志显示异常: 'MainWindow' object has no attribute 'login_controller' - By @ConceptualGod
2025-08-06 06:35:47 - root - ERROR - 控制器初始化失败: maximum recursion depth exceeded - By @ConceptualGod
2025-08-06 06:36:27 - root - ERROR - GUI日志显示异常: 'MainWindow' object has no attribute 'login_controller' - By @ConceptualGod
2025-08-06 06:36:27 - gui.account_manager_gui - INFO - 加载了 5 个账号
2025-08-06 06:36:27 - core.config_manager - INFO - 加载配置文件: coordinates_1.json
2025-08-06 06:36:27 - core.config_manager - INFO - 加载配置文件: coordinates_2.json
2025-08-06 06:36:27 - core.config_manager - INFO - 加载配置文件: coordinates_3.json
2025-08-06 06:36:27 - core.config_manager - INFO - 加载退出配置: exit.json
2025-08-06 06:36:27 - gui.login_controller - ERROR - 加载登录坐标配置失败: 'LoginController' object has no attribute 'status_text' - By @ConceptualGod
2025-08-06 06:36:32 - gui.login_controller - ERROR - 快捷键绑定失败: maximum recursion depth exceeded - By @ConceptualGod
2025-08-06 06:36:35 - gui.login_controller - ERROR - 刷新账号信息异常: maximum recursion depth exceeded
2025-08-06 06:42:09 - easyocr.easyocr - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-08-06 06:42:12 - root - INFO - 进度监控控制器初始化完成 - By @ConceptualGod
2025-08-06 06:42:12 - root - ERROR - 控制器初始化失败: maximum recursion depth exceeded - By @ConceptualGod
2025-08-06 06:42:25 - root - INFO - 成功设置窗口图标: G:\YMSJ\qfyxzdh\QFL\logo\logo.png - By @ConceptualGod
2025-08-06 06:42:25 - gui.account_manager_gui - INFO - 加载了 5 个账号
2025-08-06 06:42:25 - core.config_manager - INFO - 加载配置文件: coordinates_1.json
2025-08-06 06:42:25 - core.config_manager - INFO - 加载配置文件: coordinates_2.json
2025-08-06 06:42:25 - core.config_manager - INFO - 加载配置文件: coordinates_3.json
2025-08-06 06:42:25 - core.config_manager - INFO - 加载退出配置: exit.json
2025-08-06 06:42:26 - gui.login_controller - INFO - 加载登录坐标配置成功，共 3 个坐标 - By @ConceptualGod
2025-08-06 06:42:26 - gui.login_controller - INFO - 快捷键绑定成功: END键 -> 紧急停止 - By @ConceptualGod
2025-08-06 06:42:26 - gui.login_controller - INFO - 刷新账号信息，共 5 个账号
2025-08-06 06:42:26 - root - INFO - 集成功能回调直接设置完成 - By @ConceptualGod
2025-08-06 06:42:26 - core.account_status_manager - INFO - 账号状态管理器初始化完成 - By @ConceptualGod
2025-08-06 06:42:26 - root - INFO - 主窗口初始化完成 - By @ConceptualGod
2025-08-06 06:42:26 - root - INFO - 启动GUI界面 - By @ConceptualGod
2025-08-06 06:42:26 - root - INFO - 实时监控 - 等待游戏数据... - By @ConceptualGod
2025-08-06 06:43:44 - easyocr.easyocr - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-08-06 06:43:46 - root - INFO - 进度监控控制器初始化完成 - By @ConceptualGod
2025-08-06 06:43:46 - core.task_recognition_controller - INFO - 任务识别主控制器创建完成 - By @ConceptualGod
2025-08-06 06:43:46 - root - INFO - 任务识别控制器初始化完成 - By @ConceptualGod
2025-08-06 06:43:46 - easyocr.easyocr - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-08-06 06:43:49 - core.game_operation_controller - ERROR - 加载锦囊配置失败: maximum recursion depth exceeded - By @ConceptualGod
2025-08-06 06:43:49 - core.game_operation_controller - ERROR - 加载出装配置失败: maximum recursion depth exceeded - By @ConceptualGod
2025-08-06 06:43:49 - core.game_operation_controller - ERROR - 加载加点配置失败: maximum recursion depth exceeded - By @ConceptualGod
2025-08-06 06:43:49 - core.game_operation_controller - ERROR - 加载英雄技能配置失败: maximum recursion depth exceeded - By @ConceptualGod
2025-08-06 06:43:49 - core.game_operation_controller - ERROR - 加载游戏参数配置失败: maximum recursion depth exceeded - By @ConceptualGod
2025-08-06 06:43:49 - core.game_operation_controller - ERROR - 加载快捷键配置失败: maximum recursion depth exceeded - By @ConceptualGod
2025-08-06 06:43:49 - core.mode_manager - INFO - 模式管理器初始化完成 - By @ConceptualGod
2025-08-06 06:43:49 - core.decision_engine - INFO - 决策引擎初始化完成 - By @ConceptualGod
2025-08-06 06:43:49 - easyocr.easyocr - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-08-06 06:43:52 - core.game_end_detector - INFO - 游戏结束检测器初始化完成 - By @ConceptualGod
2025-08-06 06:43:52 - core.game_operation_controller - ERROR - 核心模块初始化失败: maximum recursion depth exceeded - By @ConceptualGod
2025-08-06 06:43:52 - root - ERROR - 控制器初始化失败: maximum recursion depth exceeded - By @ConceptualGod
2025-08-06 06:44:08 - root - INFO - 成功设置窗口图标: G:\YMSJ\qfyxzdh\QFL\logo\logo.png - By @ConceptualGod
2025-08-06 06:44:08 - gui.account_manager_gui - INFO - 加载了 5 个账号
2025-08-06 06:44:08 - core.config_manager - INFO - 加载配置文件: coordinates_1.json
2025-08-06 06:44:08 - core.config_manager - INFO - 加载配置文件: coordinates_2.json
2025-08-06 06:44:08 - core.config_manager - INFO - 加载配置文件: coordinates_3.json
2025-08-06 06:44:08 - core.config_manager - INFO - 加载退出配置: exit.json
2025-08-06 06:44:08 - gui.login_controller - INFO - 加载登录坐标配置成功，共 3 个坐标 - By @ConceptualGod
2025-08-06 06:44:08 - gui.login_controller - INFO - 快捷键绑定成功: END键 -> 紧急停止 - By @ConceptualGod
2025-08-06 06:44:08 - gui.login_controller - INFO - 刷新账号信息，共 5 个账号
2025-08-06 06:44:08 - root - INFO - 集成功能回调直接设置完成 - By @ConceptualGod
2025-08-06 06:44:08 - core.account_status_manager - INFO - 账号状态管理器初始化完成 - By @ConceptualGod
2025-08-06 06:44:08 - root - INFO - 主窗口初始化完成 - By @ConceptualGod
2025-08-06 06:44:08 - root - INFO - 启动GUI界面 - By @ConceptualGod
2025-08-06 06:44:08 - root - INFO - 实时监控 - 等待游戏数据... - By @ConceptualGod
2025-08-06 06:44:22 - root - INFO - 程序退出
2025-08-06 06:46:32 - easyocr.easyocr - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-08-06 06:46:37 - root - INFO - 进度监控控制器初始化完成 - By @ConceptualGod
2025-08-06 06:46:37 - core.task_recognition_controller - INFO - 任务识别主控制器创建完成 - By @ConceptualGod
2025-08-06 06:46:37 - root - INFO - 任务识别控制器初始化完成 - By @ConceptualGod
2025-08-06 06:46:37 - easyocr.easyocr - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-08-06 06:46:40 - core.game_operation_controller - INFO - 成功加载7个锦囊操作坐标 - By @ConceptualGod
2025-08-06 06:46:40 - core.game_operation_controller - INFO - 成功加载20个出装操作坐标 - By @ConceptualGod
2025-08-06 06:46:40 - core.game_operation_controller - INFO - 成功加载5个加点操作坐标 - By @ConceptualGod
2025-08-06 06:46:40 - core.game_operation_controller - INFO - 成功加载6个英雄技能配置 - By @ConceptualGod
2025-08-06 06:46:40 - core.game_operation_controller - INFO - 成功加载游戏参数配置文件 - By @ConceptualGod
2025-08-06 06:46:40 - core.game_operation_controller - INFO - 成功加载快捷键配置文件 - By @ConceptualGod
2025-08-06 06:46:40 - core.mode_manager - INFO - 模式管理器初始化完成 - By @ConceptualGod
2025-08-06 06:46:40 - core.decision_engine - INFO - 决策引擎初始化完成 - By @ConceptualGod
2025-08-06 06:46:40 - easyocr.easyocr - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-08-06 06:46:43 - core.game_end_detector - INFO - 游戏结束检测器初始化完成 - By @ConceptualGod
2025-08-06 06:46:43 - core.game_operation_controller - INFO - 核心模块初始化完成 - By @ConceptualGod
2025-08-06 06:46:43 - core.game_operation_controller - INFO - 游戏内操作控制器初始化完成 - By @ConceptualGod
2025-08-06 06:46:43 - core.account_status_manager - INFO - 账号状态管理器初始化完成 - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 尝试加载坐标文件: G:\YMSJ\qfyxzdh\QFL\coordinates_3.json - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 开始加载coordinates_3.json坐标配置 - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 处理坐标: step=1, description='群雄逐鹿', x=27, y=249 - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 成功加载坐标: 群雄逐鹿 -> (27, 249) - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 处理坐标: step=2, description='武勋专房1线', x=124, y=232 - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 成功加载坐标: 武勋专房1 -> (124, 232) - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 处理坐标: step=3, description='确定更换房间确定按钮', x=956, y=582 - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 成功加载坐标: 确定按钮 -> (956, 582) - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 处理坐标: step=4, description='武勋新手1线', x=144, y=156 - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 成功加载坐标: 武勋新手1 -> (144, 156) - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 处理坐标: step=5, description='开始游戏', x=972, y=174 - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 成功加载坐标: 开始游戏 -> (972, 174) - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 坐标配置加载成功，共加载 5 个坐标 - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 加载的坐标: ['群雄逐鹿', '武勋专房1', '确定按钮', '武勋新手1', '开始游戏'] - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 尝试加载战绩不符坐标文件: G:\YMSJ\qfyxzdh\QFL\zhanjibufu.json - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 加载战绩不符确定按钮坐标: (1038, 588) - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 尝试加载英雄选择坐标文件: G:\YMSJ\qfyxzdh\QFL\herochoose.json - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 加载英雄坐标: 刘备 -> (675, 223) - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 加载英雄坐标: 华佗 -> (743, 230) - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 加载英雄坐标: 陆逊 -> (927, 277) - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 加载英雄坐标: 诸葛瑾 -> (922, 387) - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 加载英雄坐标: 孙权 -> (982, 275) - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 加载英雄坐标: 曹操 -> (1335, 224) - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 成功加载6个英雄选择坐标 - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 可用英雄: ['刘备', '华佗', '陆逊', '诸葛瑾', '孙权', '曹操'] - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 尝试加载确认英雄坐标文件: G:\YMSJ\qfyxzdh\QFL\querenhero.json - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 成功加载1个确认英雄坐标 - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 尝试加载魂玉搭配坐标文件: G:\YMSJ\qfyxzdh\QFL\hunyudapei.json - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 成功加载11个魂玉搭配坐标 - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 创建默认任务领取坐标 - By @ConceptualGod
2025-08-06 06:46:43 - core.game_starter_controller - INFO - 创建了11个默认领取坐标 - By @ConceptualGod
2025-08-06 06:46:43 - root - INFO - 游戏启动控制器初始化完成 - By @ConceptualGod
2025-08-06 06:46:43 - root - INFO - 成功设置窗口图标: G:\YMSJ\qfyxzdh\QFL\logo\logo.png - By @ConceptualGod
2025-08-06 06:46:43 - gui.account_manager_gui - INFO - 加载了 5 个账号
2025-08-06 06:46:43 - core.config_manager - INFO - 加载配置文件: coordinates_1.json
2025-08-06 06:46:43 - core.config_manager - INFO - 加载配置文件: coordinates_2.json
2025-08-06 06:46:43 - core.config_manager - INFO - 加载配置文件: coordinates_3.json
2025-08-06 06:46:43 - core.config_manager - INFO - 加载退出配置: exit.json
2025-08-06 06:46:43 - gui.login_controller - INFO - 加载登录坐标配置成功，共 3 个坐标 - By @ConceptualGod
2025-08-06 06:46:43 - gui.login_controller - INFO - 快捷键绑定成功: END键 -> 紧急停止 - By @ConceptualGod
2025-08-06 06:46:43 - gui.login_controller - INFO - 刷新账号信息，共 5 个账号
2025-08-06 06:46:43 - root - INFO - 集成功能回调直接设置完成 - By @ConceptualGod
2025-08-06 06:46:43 - core.account_status_manager - INFO - 账号状态管理器初始化完成 - By @ConceptualGod
2025-08-06 06:46:43 - root - INFO - 主窗口初始化完成 - By @ConceptualGod
2025-08-06 06:46:43 - root - INFO - 启动GUI界面 - By @ConceptualGod
2025-08-06 06:46:43 - root - INFO - 实时监控 - 等待游戏数据... - By @ConceptualGod
