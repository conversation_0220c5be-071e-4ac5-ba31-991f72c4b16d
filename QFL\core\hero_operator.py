#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
英雄操作器
负责英雄的移动、技能释放、装备使用等具体操作

开发者: @ConceptualGod
创建时间: 2025-08-05
"""

import time
import logging
import pyautogui
import keyboard
import random
from typing import Dict, List, Optional, Tuple

class HeroOperator:
    """
    英雄操作器
    
    功能包括：
    - 英雄移动控制
    - 技能释放管理
    - 装备使用控制
    - 回城和复活处理
    
    开发者: @ConceptualGod
    """
    
    def __init__(self, hero_name: str, hero_skills: Dict, hotkeys: Dict, game_params: Dict):
        """
        初始化英雄操作器
        
        Args:
            hero_name: 英雄名称
            hero_skills: 英雄技能配置
            hotkeys: 快捷键配置
            game_params: 游戏参数配置
            
        开发者: @ConceptualGod
        """
        self.hero_name = hero_name
        self.hero_skills = hero_skills
        self.hotkeys = hotkeys
        self.game_params = game_params
        self.logger = logging.getLogger(__name__)
        
        # 技能CD记录
        self.last_skill_time = {}
        self.last_item_time = {}
        
        # 英雄配置
        self.hero_config = hero_skills.get(hero_name, {})
        
        self.logger.info(f"英雄操作器初始化完成，英雄: {hero_name} - By @ConceptualGod")
    
    def move_to_position(self, x: int, y: int):
        """
        移动到指定位置
        
        Args:
            x: 目标X坐标
            y: 目标Y坐标
            
        开发者: @ConceptualGod
        """
        try:
            # 添加随机偏移，模拟真实玩家操作
            offset_x = random.randint(-5, 5)
            offset_y = random.randint(-5, 5)
            
            target_x = x + offset_x
            target_y = y + offset_y
            
            # 右键点击移动
            pyautogui.rightClick(target_x, target_y)
            
            self.logger.debug(f"移动到位置: ({target_x}, {target_y}) - By @ConceptualGod")
            
        except Exception as e:
            self.logger.error(f"移动到位置失败: {str(e)} - By @ConceptualGod")
    
    def follow_teammate(self, teammate_pos: Optional[Tuple[int, int]]):
        """
        跟随队友
        
        Args:
            teammate_pos: 队友位置坐标
            
        开发者: @ConceptualGod
        """
        try:
            if not teammate_pos:
                return
            
            # 计算跟随位置（队友后方300码）
            follow_distance = self.game_params.get("follow_distance", 300)
            
            # 简化计算：在队友位置附近随机选择跟随点
            follow_x = teammate_pos[0] + random.randint(-follow_distance//2, follow_distance//2)
            follow_y = teammate_pos[1] + random.randint(-follow_distance//2, follow_distance//2)
            
            self.move_to_position(follow_x, follow_y)
            
        except Exception as e:
            self.logger.error(f"跟随队友失败: {str(e)} - By @ConceptualGod")
    
    def use_skills_on_enemies(self, enemies_nearby: int) -> bool:
        """
        对附近敌人使用技能
        
        Args:
            enemies_nearby: 附近敌人数量
            
        Returns:
            bool: 是否成功释放技能
            
        开发者: @ConceptualGod
        """
        try:
            if enemies_nearby <= 0:
                return False
            
            skills_used = False
            hero_skills = self.hero_config.get("skills", [])
            skill_priority = self.hero_config.get("skill_priority", hero_skills)
            
            # 按优先级释放技能
            for skill in skill_priority:
                if self.is_skill_ready(skill):
                    if skill in self.hotkeys.get("skills", {}):
                        keyboard.press_and_release(self.hotkeys["skills"][skill])
                        self.last_skill_time[skill] = time.time()
                        skills_used = True
                        
                        self.logger.info(f"释放技能 {skill} 攻击敌人 - By @ConceptualGod")
                        time.sleep(0.3)  # 技能间隔
            
            return skills_used
            
        except Exception as e:
            self.logger.error(f"释放技能失败: {str(e)} - By @ConceptualGod")
            return False
    
    def heal_teammates(self, teammates_status: List[Dict]) -> bool:
        """
        治疗队友（华佗专用）
        
        Args:
            teammates_status: 队友状态列表
            
        Returns:
            bool: 是否成功治疗
            
        开发者: @ConceptualGod
        """
        try:
            if self.hero_name != "华佗":
                return False
            
            heal_skill = self.hero_config.get("heal_skill", "W")
            heal_threshold = self.hero_config.get("heal_threshold", 80)
            
            if not self.is_skill_ready(heal_skill):
                return False
            
            # 寻找需要治疗的队友
            for teammate in teammates_status:
                if teammate.get("blood_percentage", 100) < heal_threshold:
                    # 释放治疗技能
                    if heal_skill in self.hotkeys.get("skills", {}):
                        keyboard.press_and_release(self.hotkeys["skills"][heal_skill])
                        self.last_skill_time[heal_skill] = time.time()
                        
                        self.logger.info(f"治疗队友，血量: {teammate.get('blood_percentage')}% - By @ConceptualGod")
                        return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"治疗队友失败: {str(e)} - By @ConceptualGod")
            return False
    
    def use_item(self, item_slot: str, reason: str = "") -> bool:
        """
        使用装备
        
        Args:
            item_slot: 装备槽位
            reason: 使用原因
            
        Returns:
            bool: 是否成功使用
            
        开发者: @ConceptualGod
        """
        try:
            if not self.is_item_ready(item_slot):
                return False
            
            if item_slot in self.hotkeys.get("items", {}):
                keyboard.press_and_release(self.hotkeys["items"][item_slot])
                self.last_item_time[item_slot] = time.time()
                
                self.logger.info(f"使用装备 {item_slot} - {reason} - By @ConceptualGod")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"使用装备失败: {str(e)} - By @ConceptualGod")
            return False
    
    def use_shield(self) -> bool:
        """
        使用玄铁盾
        
        Returns:
            bool: 是否成功使用
            
        开发者: @ConceptualGod
        """
        return self.use_item("item_2", "血量低使用玄铁盾")
    
    def use_boots(self) -> bool:
        """
        使用奔雷靴
        
        Returns:
            bool: 是否成功使用
            
        开发者: @ConceptualGod
        """
        return self.use_item("item_1", "撤离使用奔雷靴")
    
    def return_to_base(self) -> bool:
        """
        回城
        
        Returns:
            bool: 是否成功回城
            
        开发者: @ConceptualGod
        """
        try:
            keyboard.press_and_release(self.hotkeys.get("return_city", "y"))
            self.logger.info("执行回城操作 - By @ConceptualGod")
            return True
            
        except Exception as e:
            self.logger.error(f"回城失败: {str(e)} - By @ConceptualGod")
            return False
    
    def buy_equipment(self) -> bool:
        """
        购买装备
        
        Returns:
            bool: 是否成功打开商店
            
        开发者: @ConceptualGod
        """
        try:
            keyboard.press_and_release(self.hotkeys.get("buy_equipment", "b"))
            self.logger.info("打开商店购买装备 - By @ConceptualGod")
            return True
            
        except Exception as e:
            self.logger.error(f"打开商店失败: {str(e)} - By @ConceptualGod")
            return False
    
    def retreat_to_safety(self, safe_position: Optional[Tuple[int, int]] = None):
        """
        撤退到安全位置
        
        Args:
            safe_position: 安全位置坐标
            
        开发者: @ConceptualGod
        """
        try:
            # 使用奔雷靴加速撤退
            self.use_boots()
            
            if safe_position:
                self.move_to_position(safe_position[0], safe_position[1])
            else:
                # 默认向屏幕中心下方撤退（主城方向）
                screen_width, screen_height = pyautogui.size()
                safe_x = screen_width // 2
                safe_y = screen_height * 3 // 4
                self.move_to_position(safe_x, safe_y)
            
            self.logger.info("执行撤退操作 - By @ConceptualGod")
            
        except Exception as e:
            self.logger.error(f"撤退失败: {str(e)} - By @ConceptualGod")
    
    def is_skill_ready(self, skill: str) -> bool:
        """
        检查技能是否准备就绪
        
        Args:
            skill: 技能名称
            
        Returns:
            bool: 技能是否可用
            
        开发者: @ConceptualGod
        """
        try:
            skill_cooldown = self.game_params.get("skill_cooldown", 3)
            last_use_time = self.last_skill_time.get(skill, 0)
            
            return (time.time() - last_use_time) >= skill_cooldown
            
        except Exception as e:
            self.logger.error(f"检查技能CD失败: {str(e)} - By @ConceptualGod")
            return False
    
    def is_item_ready(self, item_slot: str) -> bool:
        """
        检查装备是否准备就绪
        
        Args:
            item_slot: 装备槽位
            
        Returns:
            bool: 装备是否可用
            
        开发者: @ConceptualGod
        """
        try:
            item_cooldown = self.game_params.get("item_cooldown", 5)
            last_use_time = self.last_item_time.get(item_slot, 0)
            
            return (time.time() - last_use_time) >= item_cooldown
            
        except Exception as e:
            self.logger.error(f"检查装备CD失败: {str(e)} - By @ConceptualGod")
            return False
    
    def auto_level_up(self, level: int):
        """
        自动加点
        
        Args:
            level: 当前等级
            
        开发者: @ConceptualGod
        """
        try:
            # 15级和25级加点生命值
            if level in [15, 25]:
                keyboard.press_and_release(self.hotkeys.get("add_points", "+"))
                time.sleep(0.5)
                
                self.logger.info(f"{level}级自动加点生命值 - By @ConceptualGod")
                
        except Exception as e:
            self.logger.error(f"自动加点失败: {str(e)} - By @ConceptualGod")
    
    def get_operation_status(self) -> Dict:
        """
        获取操作状态
        
        Returns:
            Dict: 操作状态信息
            
        开发者: @ConceptualGod
        """
        return {
            "hero_name": self.hero_name,
            "skills_ready": {skill: self.is_skill_ready(skill) 
                           for skill in self.hero_config.get("skills", [])},
            "items_ready": {
                "shield": self.is_item_ready("item_2"),
                "boots": self.is_item_ready("item_1")
            },
            "last_skill_time": self.last_skill_time.copy(),
            "last_item_time": self.last_item_time.copy()
        }
