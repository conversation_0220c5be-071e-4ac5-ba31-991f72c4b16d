#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏结束检测器
负责检测游戏是否结束并返回结果

开发者: @ConceptualGod
创建时间: 2025-08-05
"""

import cv2
import numpy as np
import easyocr
import logging
import time
from PIL import ImageGrab
from typing import Dict, Optional, Tuple

class GameEndDetector:
    """
    游戏结束检测器
    
    功能包括：
    - 检测游戏结束界面
    - 识别胜利/失败状态
    - 检测是否返回大厅
    
    开发者: @ConceptualGod
    """
    
    def __init__(self):
        """
        初始化游戏结束检测器
        
        开发者: @ConceptualGod
        """
        self.logger = logging.getLogger(__name__)
        
        # 初始化OCR引擎
        self.ocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        
        # 检测区域配置
        self.detection_areas = {
            "game_result": (600, 200, 1320, 400),      # 游戏结果区域
            "return_hall": (600, 600, 1320, 800),      # 返回大厅按钮区域
            "full_screen": (0, 0, 1920, 1080)          # 全屏检测
        }
        
        # 关键词配置
        self.victory_keywords = ["胜利", "victory", "win", "获胜"]
        self.defeat_keywords = ["失败", "defeat", "lose", "失利", "战败"]
        self.hall_keywords = ["返回大厅", "大厅", "hall", "返回", "确定"]
        
        # 检测状态
        self.last_detection_time = 0
        self.detection_interval = 2  # 每2秒检测一次
        
        self.logger.info("游戏结束检测器初始化完成 - By @ConceptualGod")
    
    def detect_game_end(self) -> Dict[str, any]:
        """
        检测游戏是否结束
        
        Returns:
            Dict: 检测结果
            {
                "game_ended": bool,
                "result": str,  # "victory", "defeat", "unknown"
                "confidence": float,
                "timestamp": float
            }
            
        开发者: @ConceptualGod
        """
        try:
            current_time = time.time()
            
            # 控制检测频率
            if current_time - self.last_detection_time < self.detection_interval:
                return {"game_ended": False, "result": "checking", "confidence": 0.0}
            
            self.last_detection_time = current_time
            
            # 截取游戏结果区域
            result_area = self._capture_area(self.detection_areas["game_result"])
            if result_area is None:
                return {"game_ended": False, "result": "error", "confidence": 0.0}
            
            # OCR识别文本
            ocr_results = self.ocr_reader.readtext(result_area)
            
            # 分析识别结果
            game_result = self._analyze_game_result(ocr_results)
            
            if game_result["game_ended"]:
                self.logger.info(f"检测到游戏结束: {game_result['result']} - By @ConceptualGod")
            
            game_result["timestamp"] = current_time
            return game_result
            
        except Exception as e:
            self.logger.error(f"游戏结束检测失败: {str(e)} - By @ConceptualGod")
            return {"game_ended": False, "result": "error", "confidence": 0.0}
    
    def detect_return_to_hall(self) -> bool:
        """
        检测是否已返回大厅
        
        Returns:
            bool: 是否已返回大厅
            
        开发者: @ConceptualGod
        """
        try:
            # 截取返回大厅区域
            hall_area = self._capture_area(self.detection_areas["return_hall"])
            if hall_area is None:
                return False
            
            # OCR识别
            ocr_results = self.ocr_reader.readtext(hall_area)
            
            # 检查是否包含大厅相关关键词
            for (bbox, text, confidence) in ocr_results:
                if confidence > 0.5:
                    text_lower = text.lower()
                    for keyword in self.hall_keywords:
                        if keyword.lower() in text_lower:
                            self.logger.info(f"检测到返回大厅界面: {text} - By @ConceptualGod")
                            return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检测返回大厅失败: {str(e)} - By @ConceptualGod")
            return False
    
    def wait_for_game_end(self, timeout: int = 1800) -> Dict[str, any]:
        """
        等待游戏结束
        
        Args:
            timeout: 超时时间（秒），默认30分钟
            
        Returns:
            Dict: 游戏结束结果
            
        开发者: @ConceptualGod
        """
        try:
            start_time = time.time()
            self.logger.info(f"开始等待游戏结束，超时时间: {timeout}秒 - By @ConceptualGod")
            
            while time.time() - start_time < timeout:
                # 检测游戏结束
                result = self.detect_game_end()
                
                if result["game_ended"]:
                    elapsed_time = time.time() - start_time
                    result["game_duration"] = elapsed_time
                    self.logger.info(f"游戏结束，用时: {elapsed_time:.1f}秒 - By @ConceptualGod")
                    return result
                
                # 短暂等待
                time.sleep(1)
            
            # 超时
            self.logger.warning(f"等待游戏结束超时: {timeout}秒 - By @ConceptualGod")
            return {
                "game_ended": True,
                "result": "timeout",
                "confidence": 0.0,
                "game_duration": timeout,
                "timestamp": time.time()
            }
            
        except Exception as e:
            self.logger.error(f"等待游戏结束失败: {str(e)} - By @ConceptualGod")
            return {
                "game_ended": True,
                "result": "error",
                "confidence": 0.0,
                "timestamp": time.time()
            }
    
    def wait_for_return_to_hall(self, timeout: int = 30) -> bool:
        """
        等待返回大厅
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功返回大厅
            
        开发者: @ConceptualGod
        """
        try:
            start_time = time.time()
            self.logger.info(f"等待返回大厅，超时时间: {timeout}秒 - By @ConceptualGod")
            
            while time.time() - start_time < timeout:
                if self.detect_return_to_hall():
                    elapsed_time = time.time() - start_time
                    self.logger.info(f"成功返回大厅，用时: {elapsed_time:.1f}秒 - By @ConceptualGod")
                    return True
                
                time.sleep(1)
            
            self.logger.warning(f"等待返回大厅超时: {timeout}秒 - By @ConceptualGod")
            return False
            
        except Exception as e:
            self.logger.error(f"等待返回大厅失败: {str(e)} - By @ConceptualGod")
            return False
    
    def _capture_area(self, area: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """
        截取指定区域
        
        Args:
            area: 区域坐标 (x1, y1, x2, y2)
            
        Returns:
            Optional[np.ndarray]: 截取的图像
            
        开发者: @ConceptualGod
        """
        try:
            x1, y1, x2, y2 = area
            screenshot = ImageGrab.grab(bbox=(x1, y1, x2, y2))
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            self.logger.error(f"截取区域失败: {str(e)} - By @ConceptualGod")
            return None
    
    def _analyze_game_result(self, ocr_results: list) -> Dict[str, any]:
        """
        分析游戏结果
        
        Args:
            ocr_results: OCR识别结果
            
        Returns:
            Dict: 分析结果
            
        开发者: @ConceptualGod
        """
        try:
            best_confidence = 0.0
            game_result = "unknown"
            game_ended = False
            
            for (bbox, text, confidence) in ocr_results:
                if confidence > 0.5:  # 置信度阈值
                    text_lower = text.lower()
                    
                    # 检查胜利关键词
                    for keyword in self.victory_keywords:
                        if keyword.lower() in text_lower:
                            if confidence > best_confidence:
                                best_confidence = confidence
                                game_result = "victory"
                                game_ended = True
                            break
                    
                    # 检查失败关键词
                    for keyword in self.defeat_keywords:
                        if keyword.lower() in text_lower:
                            if confidence > best_confidence:
                                best_confidence = confidence
                                game_result = "defeat"
                                game_ended = True
                            break
            
            return {
                "game_ended": game_ended,
                "result": game_result,
                "confidence": best_confidence
            }
            
        except Exception as e:
            self.logger.error(f"分析游戏结果失败: {str(e)} - By @ConceptualGod")
            return {
                "game_ended": False,
                "result": "error",
                "confidence": 0.0
            }
    
    def get_detection_stats(self) -> Dict[str, any]:
        """
        获取检测统计信息
        
        Returns:
            Dict: 统计信息
            
        开发者: @ConceptualGod
        """
        return {
            "last_detection_time": self.last_detection_time,
            "detection_interval": self.detection_interval,
            "detection_areas": self.detection_areas,
            "victory_keywords": self.victory_keywords,
            "defeat_keywords": self.defeat_keywords,
            "hall_keywords": self.hall_keywords
        }
    
    def update_detection_areas(self, new_areas: Dict[str, Tuple[int, int, int, int]]):
        """
        更新检测区域
        
        Args:
            new_areas: 新的检测区域配置
            
        开发者: @ConceptualGod
        """
        self.detection_areas.update(new_areas)
        self.logger.info("检测区域配置已更新 - By @ConceptualGod")
    
    def update_keywords(self, victory_keywords: list = None, defeat_keywords: list = None, 
                       hall_keywords: list = None):
        """
        更新关键词配置
        
        Args:
            victory_keywords: 胜利关键词
            defeat_keywords: 失败关键词
            hall_keywords: 大厅关键词
            
        开发者: @ConceptualGod
        """
        if victory_keywords:
            self.victory_keywords = victory_keywords
        if defeat_keywords:
            self.defeat_keywords = defeat_keywords
        if hall_keywords:
            self.hall_keywords = hall_keywords
        
        self.logger.info("关键词配置已更新 - By @ConceptualGod")
