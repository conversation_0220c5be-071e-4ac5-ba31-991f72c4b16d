# 任务日志监控说明

## 功能概述

基于当天日期的日志监控系统，用于替代OCR识别，监控战功任务进度。

## 核心功能

### 1. 任务进度监控
- 击杀数监控 (15个击杀任务)
- 助攻数监控 (20个助攻、30个助攻任务)
- MVP分数监控 (150MVP任务)
- 胜利状态监控

### 2. 游戏状态检测
- 游戏是否结束
- 胜负结果判断

### 3. 日志文件解析
- scoreLOG.log: 游戏统计数据
- end.log: 游戏结束状态

## 使用方法

### 基础使用
```python
from task_log_monitor import TaskLogMonitor

# 创建监控器
monitor = TaskLogMonitor("7fgame")

# 获取任务进度
progress = monitor.get_task_progress()

# 检查游戏状态
is_ended = monitor.is_game_ended()
is_win = monitor.is_victory()
```

### 替代OCR系统
```python
from task_log_monitor import TaskRecognitionController

# 创建控制器
controller = TaskRecognitionController("7fgame")

# 替代OCR识别
progress = controller.recognize_task_progress()
game_ended = controller.is_game_ended()
victory = controller.is_victory()

# 获取具体数值
assists = controller.get_assists_count()
kills = controller.get_kills_count()
mvp = controller.get_mvp_score()
```

### 集成到现有系统
```python
# 在您的主程序中
def get_current_task_progress():
    controller = TaskRecognitionController("7fgame")
    return controller.recognize_task_progress()

# 主循环
while True:
    progress = get_current_task_progress()
    
    if progress:
        # 检查任务完成
        if progress['assists_30']['completed']:
            print("30个助攻任务完成")
            # 触发奖励领取
        
        if progress['game_ended']:
            if progress['victory']['completed']:
                print("游戏胜利")
            # 开始新游戏
    
    time.sleep(5)
```

## 数据格式

### 任务进度返回格式
```python
{
    'assists_20': {
        'current': 25,      # 当前助攻数
        'target': 20,       # 目标助攻数
        'completed': True   # 是否完成
    },
    'assists_30': {
        'current': 25,
        'target': 30,
        'completed': False
    },
    'kills_15': {
        'current': 8,
        'target': 15,
        'completed': False
    },
    'mvp_150': {
        'current': 120,
        'target': 150,
        'completed': False
    },
    'victory': {
        'completed': False  # 是否胜利
    },
    'game_ended': False     # 游戏是否结束
}
```

## 技术原理

### 日期识别
- 自动获取系统当前日期
- 格式: YYYY.MM.DD (如: 2025.08.05)
- 只监控当天的日志文件夹

### 日志解析
- scoreLOG.log数据索引:
  - [21] = 击杀数
  - [22] = 助攻数  
  - [29] = MVP分数
- end.log结果判断:
  - 查询结果=5 表示胜利
  - 查询结果=0 表示失败

### 文件监控
- 扫描格式: log{PID}-{日期}-{时间}
- 自动选择最新的游戏会话
- 实时解析日志文件

## 优势

### 相比OCR识别
- 准确率100% (官方数据)
- 不受界面变化影响
- 不受分辨率影响
- 实时性强，延迟极低
- 资源占用极少

### 相比坐标识别
- 不受窗口位置影响
- 不受界面更新影响
- 不需要录制坐标
- 稳定性极高

## 实施步骤

### 1. 立即测试
```bash
python task_log_monitor.py
```

### 2. 集成现有系统
```python
# 替换OCR模块
from task_log_monitor import TaskRecognitionController

# 在现有代码中替换
# 原来: ocr_result = ocr_recognize()
# 现在: progress = controller.recognize_task_progress()
```

### 3. 验证功能
- 启动游戏进行一局
- 观察任务进度变化
- 确认游戏结束检测

## 注意事项

### 路径配置
- 确保7fgame路径正确
- 确保GameLog文件夹存在

### 日志文件
- 需要当天有游戏日志
- 日志文件格式必须正确

### 编码处理
- 使用utf-8编码读取
- 忽略编码错误

## 错误处理

### 常见问题
1. 找不到日志文件夹
   - 检查游戏路径
   - 确认今天已玩过游戏

2. 解析失败
   - 检查文件权限
   - 确认文件格式

3. 数据为0
   - 确认游戏正在进行
   - 检查日志文件是否更新

### 调试方法
```python
# 打印调试信息
monitor = TaskLogMonitor("7fgame")
folders = monitor.scan_today_logs()
print(f"找到文件夹: {folders}")

session = monitor.get_latest_session()
print(f"当前会话: {session}")

stats = monitor.parse_score_log(session)
print(f"统计数据: {stats}")
```
