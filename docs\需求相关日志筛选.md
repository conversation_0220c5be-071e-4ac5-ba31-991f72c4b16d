# 基于需求的7fgame有用日志筛选

**开发者:** @ConceptualGod  
**基于文档:** 起凡游戏自动化说明.md + 起凡游戏问题及逻辑说明.md  
**筛选原则:** 只保留对您自动化需求有直接帮助的日志文件

## 🎯 核心需求映射

### **需求1: 战功任务识别与监控**
**文档要求:** 识别助攻、胜利、完整局、MVP、牺牲值任务，显示完成数值

**有用日志:**
```
📊 scoreLOG.log (⭐⭐⭐⭐⭐ 最重要)
├── [22] = 助攻数 (助攻任务核心)
├── [29] = MVP分数 (MVP任务核心)
├── [74] = 总伤害值 (牺牲值计算基础)
└── 实时更新，100%准确

📊 end.log (⭐⭐⭐⭐⭐ 最重要)
├── 查询结果=5 (胜利任务核心)
├── 查询结果=0 (失败判断)
├── 游戏结束检测
└── 完整局统计
```

### **需求2: 游戏内状态监控**
**文档要求:** 血量<80%用玄铁盾，血量<40%或蓝量<10%撤离回城

**有用日志:**
```
📊 scoreLOG.log (⭐⭐⭐⭐)
├── [16] = 英雄等级 (15级、25级加生命值)
├── [11] = 金币数量 (5000金币出装触发)
├── [70] = 死亡数 (复活检测)
├── [2] = 经验值 (发育进度)
└── [73] = 治疗量 (华佗W技能效果)

📊 Perf-fps.log (⭐⭐⭐)
├── [Ping,47.0,47.0,47.0] (网络延迟监控)
├── [FPS,49.0,49.0,49.0] (游戏流畅度)
├── [SysCPU,37.2,0.0,100.0] (系统负载)
└── [ProcMemPhy,946.0,946.0,946.0] (内存使用)

注意: 血量蓝量百分比日志中无法直接获取，需要其他方式
```

### **需求3: 英雄选择与搭配**
**文档要求:** 根据战功任务选择对应英雄(华佗、刘备、诸葛瑾、陆逊、孙权、曹操)

**有用日志:**
```
📊 scoreLOG.log (⭐⭐⭐)
├── [1] = 英雄ID (确认当前选择的英雄)
└── [3] = 队伍ID (1=蜀国, 2=魏国, 3=吴国)

注意: 英雄选择主要通过游戏操作控制，日志只用于确认
```

### **需求4: 游戏进程控制**
**文档要求:** 游戏开始、30分钟操作、游戏结束检测、模式切换(20分钟)

**有用日志:**
```
📊 end.log (⭐⭐⭐⭐⭐)
├── 游戏结束精确检测
├── 胜负结果判断
└── 奖励获得统计

📊 allcmd.log (⭐⭐⭐⭐)
├── 301 = 移动命令 (跟随模式)
├── 346 = 技能使用 (战斗模式)
├── 342 = 攻击命令 (攻击优先级)
├── 309 = 选择目标
└── 时间戳精确记录

📊 guajitime.log (⭐⭐⭐)
├── control_id = 挂机检测
├── op_time = 操作时间
└── time = 挂机时长

📊 disconnection.log (⭐⭐⭐)
├── 断线重连记录
├── roomID = 房间状态
└── 网络稳定性监控
```

### **需求5: 装备与锦囊管理**
**文档要求:** B键买速度之靴，黄金锦囊选玄铁盾，出装顺序，白色箱子选麒麟心

**有用日志:**
```
📊 buyItemCmd.log (⭐⭐⭐⭐)
├── 所有装备购买记录
├── 购买时间和物品ID
├── 金币消耗统计
└── 出装路线分析

📊 scoreLOG.log (⭐⭐⭐⭐)
├── [11] = 金币数量 (出装触发条件)
└── 金币变化监控

📊 goldass.log (⭐⭐⭐)
├── 金币和积分计算
└── 经济管理数据

📊 turn.log (⭐⭐⭐)
├── sanguo/model/JIAXU.apm (英雄模型)
├── sanguo/model/TOUSHICHE.apm (投石车)
├── sanguo/model/SHIPINSHANG.apm (商店)
└── 游戏对象识别
```

### **需求6: 性能与网络监控**
**文档要求:** 确保游戏稳定运行，网络连接正常

**有用日志:**
```
📊 Perf-fps.log (⭐⭐⭐⭐)
├── 帧率监控 (游戏流畅度)
├── 延迟监控 (网络质量)
├── CPU使用率 (系统负载)
└── 内存使用量 (资源管理)

📊 net_state.log (⭐⭐⭐⭐)
├── HostSrvIP: *************** (服务器IP)
├── HostSrvPort: 2175 (服务器端口)
└── 连接状态监控

📊 error.log (⭐⭐⭐)
├── 纹理加载错误
├── 文件访问错误
├── 音频格式问题
└── 异常诊断

📊 voice.log (⭐⭐)
├── 语音通信状态
├── 团队协作监控
└── 通信质量
```

## 🚫 对您需求无用的日志文件

### **无关日志 (可忽略):**
```
❌ init.log - 系统初始化信息 (一次性，无实时价值)
❌ setting.log - 游戏设置信息 (静态配置)
❌ Perf-Detail.log - 详细性能数据 (Perf-fps.log已足够)
❌ Perf-File.log - 文件加载性能 (对游戏逻辑无用)
❌ PreLoad.log - 预加载信息 (启动阶段)
❌ save_time_Lua.log - Lua保存时间 (开发调试用)
❌ _G output.log - 全局输出日志 (开发调试用)
❌ MD5.log - 文件校验日志 (完整性检查)
❌ TransModel.log - 模型转换日志 (资源加载)
❌ SoundList.log - 声音列表日志 (音频资源)
❌ WG_Report.log - WG报告日志 (反外挂)
❌ Live_data.log - 直播数据日志 (直播功能)
❌ syySnapshot.log - 快照日志 (调试用)
❌ to_wgs.log - WGS通信日志 (反外挂通信)
❌ !Live.log - 直播相关 (非核心功能)
❌ !Looker.log - 观战相关 (非核心功能)
❌ !sound.log - 声音相关 (非核心功能)
❌ !video.log - 视频相关 (非核心功能)
❌ replay*.7fr - 游戏录像文件 (回放功能)
❌ replay_cmd.log - 录像命令 (回放功能)
❌ corelog*.txt - 核心日志 (系统级)
```

## 📊 优先级排序

### **第一优先级 (立即实施):**
1. **scoreLOG.log** - 战功任务核心数据
2. **end.log** - 游戏结束和胜负判断
3. **net_state.log** - 用户和房间信息

### **第二优先级 (重要功能):**
4. **buyItemCmd.log** - 装备购买监控
5. **allcmd.log** - 游戏操作记录
6. **Perf-fps.log** - 性能监控
7. **eloScore.log** - 玩家信息

### **第三优先级 (辅助功能):**
8. **guajitime.log** - 挂机检测
9. **disconnection.log** - 断线监控
10. **error.log** - 错误诊断

## 🎯 实施建议

### **立即可用的核心功能:**
```python
# 基于您需求的精简日志监控
class GameAutomationLogMonitor:
    def __init__(self):
        self.core_logs = {
            'task_progress': 'scoreLOG.log',      # 战功任务进度
            'game_end': 'end.log',                # 游戏结束检测
            'user_info': 'net_state.log',         # 用户信息
            'equipment': 'buyItemCmd.log',        # 装备购买
            'operations': 'allcmd.log',           # 游戏操作
            'performance': 'Perf-fps.log'         # 性能监控
        }
    
    def get_task_completion_status(self):
        """获取战功任务完成状态 - 替代OCR"""
        return {
            'assists': self.parse_assists(),      # 助攻任务
            'victories': self.parse_victories(),  # 胜利任务
            'mvp': self.parse_mvp(),             # MVP任务
            'sacrifice': self.parse_sacrifice(),  # 牺牲值任务
            'complete_games': self.parse_games() # 完整局任务
        }
    
    def get_game_state(self):
        """获取游戏状态 - 替代状态检测"""
        return {
            'hero_level': self.parse_hero_level(),    # 15级、25级检测
            'gold': self.parse_gold(),                # 5000金币出装
            'game_ended': self.parse_game_end(),      # 游戏结束
            'is_victory': self.parse_victory()        # 胜负判断
        }
```

**这个筛选完全基于您的自动化需求，去除了所有无关的日志文件，专注于核心功能实现！**
