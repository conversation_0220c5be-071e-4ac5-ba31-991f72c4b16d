#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进程和窗口扫描工具
开发者: @ConceptualGod
"""

import psutil
import time

def scan_all_processes():
    """扫描所有运行中的进程"""
    print("=== 扫描所有运行中的进程 ===")
    
    all_processes = []
    game_related = []
    
    # 游戏相关关键词（包含7FGame）
    game_keywords = ['7fgame', 'qifan', 'qf', '起凡', 'game', 'client', 'launcher', 'platform']
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                info = proc.info
                process_name = info['name']
                pid = info['pid']
                exe_path = info.get('exe', 'N/A')
                
                all_processes.append((process_name, pid, exe_path))
                
                # 检查是否是游戏相关进程
                name_lower = process_name.lower()
                for keyword in game_keywords:
                    if keyword in name_lower:
                        game_related.append((process_name, pid, exe_path))
                        break
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
    
    except Exception as e:
        print(f"扫描进程时出错: {e}")
        return [], []
    
    print(f"总共找到 {len(all_processes)} 个进程")
    print(f"其中 {len(game_related)} 个可能是游戏相关进程")
    
    if game_related:
        print("\n可能的游戏相关进程:")
        for name, pid, exe_path in game_related:
            print(f"  - {name} (PID: {pid})")
            if exe_path != 'N/A':
                print(f"    路径: {exe_path}")
    else:
        print("\n未找到明显的游戏相关进程")
    
    return all_processes, game_related

def scan_windows():
    """扫描所有可见窗口"""
    print("\n=== 扫描所有可见窗口 ===")
    
    try:
        import win32gui
        
        all_windows = []
        game_windows = []
        
        # 游戏相关窗口关键词（包含起凡游戏平台）
        window_keywords = ['起凡', '7fgame', 'qifan', '游戏', 'game', '群雄', '三国', '登录', 'login', 'platform']
        
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if window_title.strip():  # 只记录有标题的窗口
                    windows.append((hwnd, window_title))
            return True
        
        win32gui.EnumWindows(enum_windows_callback, all_windows)
        
        # 过滤游戏相关窗口
        for hwnd, title in all_windows:
            title_lower = title.lower()
            for keyword in window_keywords:
                if keyword in title_lower:
                    game_windows.append((hwnd, title))
                    break
        
        print(f"总共找到 {len(all_windows)} 个可见窗口")
        print(f"其中 {len(game_windows)} 个可能是游戏相关窗口")
        
        if game_windows:
            print("\n可能的游戏相关窗口:")
            for hwnd, title in game_windows:
                print(f"  - '{title}' (句柄: {hwnd})")
        else:
            print("\n未找到明显的游戏相关窗口")
        
        # 显示所有窗口标题（前30个，方便查找）
        print(f"\n所有窗口标题（前30个）:")
        for i, (hwnd, title) in enumerate(all_windows[:30]):
            print(f"  {i+1:2d}. '{title}'")
        
        return all_windows, game_windows
        
    except ImportError:
        print("win32gui未安装，无法扫描窗口")
        return [], []
    except Exception as e:
        print(f"扫描窗口时出错: {e}")
        return [], []

def main():
    """主函数"""
    print("起凡游戏进程和窗口扫描工具")
    print("开发者: @ConceptualGod")
    print("=" * 50)
    
    # 扫描进程
    all_processes, game_processes = scan_all_processes()
    
    # 扫描窗口
    all_windows, game_windows = scan_windows()
    
    # 总结
    print("\n" + "=" * 50)
    print("扫描总结:")
    print(f"- 总进程数: {len(all_processes)}")
    print(f"- 游戏相关进程: {len(game_processes)}")
    print(f"- 总窗口数: {len(all_windows)}")
    print(f"- 游戏相关窗口: {len(game_windows)}")
    
    if game_processes:
        print(f"\n建议检查的游戏进程:")
        for name, pid, exe_path in game_processes:
            print(f"  - {name}")
    
    if game_windows:
        print(f"\n建议检查的游戏窗口:")
        for hwnd, title in game_windows:
            print(f"  - '{title}'")

if __name__ == "__main__":
    main()
