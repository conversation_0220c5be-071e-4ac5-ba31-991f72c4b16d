#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
决策引擎
负责综合分析游戏状态并做出操作决策

开发者: @ConceptualGod
创建时间: 2025-08-05
"""

import time
import logging
from typing import Dict, List, Any, Optional
from .mode_manager import GameMode

class Decision:
    """决策类"""
    def __init__(self, action: str, priority: int, params: Dict = None):
        self.action = action
        self.priority = priority
        self.params = params or {}
        self.timestamp = time.time()

class DecisionEngine:
    """
    决策引擎
    
    功能包括：
    - 综合分析游戏状态
    - 做出操作决策
    - 优先级管理
    
    开发者: @ConceptualGod
    """
    
    def __init__(self, game_params: Dict):
        """
        初始化决策引擎
        
        Args:
            game_params: 游戏参数配置
            
        开发者: @ConceptualGod
        """
        self.game_params = game_params
        self.logger = logging.getLogger(__name__)
        
        # 决策历史
        self.decision_history = []
        self.last_decision_time = 0
        
        # 决策优先级定义
        self.priority_levels = {
            "emergency_retreat": 10,    # 紧急撤退
            "use_shield": 9,            # 使用护盾
            "retreat": 8,               # 撤退
            "return_base": 7,           # 回城
            "use_skills": 6,            # 使用技能
            "follow_teammate": 5,       # 跟随队友
            "buy_equipment": 4,         # 购买装备
            "handle_jinnang": 3,        # 处理锦囊
            "level_up": 2,              # 升级加点
            "idle": 1                   # 空闲
        }
        
        self.logger.info("决策引擎初始化完成 - By @ConceptualGod")
    
    def make_decision(self, game_state: Dict[str, Any], current_mode: GameMode, 
                     hero_status: Dict[str, Any]) -> Optional[Decision]:
        """
        根据游戏状态和当前模式做出决策
        
        Args:
            game_state: 游戏状态信息
            current_mode: 当前游戏模式
            hero_status: 英雄状态信息
            
        Returns:
            Optional[Decision]: 决策结果
            
        开发者: @ConceptualGod
        """
        try:
            decisions = []
            
            # 生存优先决策
            decisions.extend(self._make_survival_decisions(game_state))
            
            # 战斗决策
            decisions.extend(self._make_combat_decisions(game_state, current_mode))
            
            # 移动决策
            decisions.extend(self._make_movement_decisions(game_state, current_mode))
            
            # 装备和锦囊决策
            decisions.extend(self._make_equipment_decisions(game_state))
            
            # 升级决策
            decisions.extend(self._make_level_decisions(game_state))
            
            # 按优先级排序
            decisions.sort(key=lambda x: x.priority, reverse=True)
            
            # 返回最高优先级决策
            if decisions:
                best_decision = decisions[0]
                self._record_decision(best_decision)
                return best_decision
            
            # 没有特殊决策时返回空闲
            idle_decision = Decision("idle", self.priority_levels["idle"])
            self._record_decision(idle_decision)
            return idle_decision
            
        except Exception as e:
            self.logger.error(f"决策制定失败: {str(e)} - By @ConceptualGod")
            return None
    
    def _make_survival_decisions(self, game_state: Dict[str, Any]) -> List[Decision]:
        """
        制定生存相关决策
        
        Args:
            game_state: 游戏状态信息
            
        Returns:
            List[Decision]: 生存决策列表
            
        开发者: @ConceptualGod
        """
        decisions = []
        
        try:
            blood_percentage = game_state.get("blood_percentage", 100)
            mana_percentage = game_state.get("mana_percentage", 100)
            enemies_nearby = game_state.get("enemies_nearby", 0)
            
            blood_threshold = self.game_params.get("blood_threshold", 80)
            low_blood_threshold = self.game_params.get("low_blood_threshold", 40)
            mana_threshold = self.game_params.get("mana_threshold", 10)
            enemy_threshold = self.game_params.get("enemy_threshold", 3)
            
            # 紧急撤退：血量极低或敌人过多
            if (blood_percentage < low_blood_threshold or 
                mana_percentage < mana_threshold or
                enemies_nearby >= enemy_threshold):
                
                decisions.append(Decision(
                    "emergency_retreat",
                    self.priority_levels["emergency_retreat"],
                    {"reason": f"血量{blood_percentage}%，蓝量{mana_percentage}%，敌人{enemies_nearby}个"}
                ))
            
            # 使用护盾：血量中等偏低
            elif blood_percentage < blood_threshold:
                decisions.append(Decision(
                    "use_shield",
                    self.priority_levels["use_shield"],
                    {"reason": f"血量{blood_percentage}%"}
                ))
            
            # 回城：在安全区域且血量不满
            elif blood_percentage < 90 and enemies_nearby == 0:
                safe_distance = self.game_params.get("safe_distance", 2000)
                decisions.append(Decision(
                    "return_base",
                    self.priority_levels["return_base"],
                    {"reason": "安全区域补血"}
                ))
            
        except Exception as e:
            self.logger.error(f"制定生存决策失败: {str(e)} - By @ConceptualGod")
        
        return decisions
    
    def _make_combat_decisions(self, game_state: Dict[str, Any], 
                              current_mode: GameMode) -> List[Decision]:
        """
        制定战斗相关决策
        
        Args:
            game_state: 游戏状态信息
            current_mode: 当前游戏模式
            
        Returns:
            List[Decision]: 战斗决策列表
            
        开发者: @ConceptualGod
        """
        decisions = []
        
        try:
            enemies_nearby = game_state.get("enemies_nearby", 0)
            allies_nearby = game_state.get("allies_nearby", 0)
            
            # 战斗模式或有利条件下使用技能
            if (current_mode == GameMode.BATTLE or 
                (enemies_nearby > 0 and allies_nearby >= enemies_nearby)):
                
                decisions.append(Decision(
                    "use_skills",
                    self.priority_levels["use_skills"],
                    {
                        "enemies": enemies_nearby,
                        "allies": allies_nearby,
                        "mode": current_mode.value
                    }
                ))
            
        except Exception as e:
            self.logger.error(f"制定战斗决策失败: {str(e)} - By @ConceptualGod")
        
        return decisions
    
    def _make_movement_decisions(self, game_state: Dict[str, Any], 
                                current_mode: GameMode) -> List[Decision]:
        """
        制定移动相关决策
        
        Args:
            game_state: 游戏状态信息
            current_mode: 当前游戏模式
            
        Returns:
            List[Decision]: 移动决策列表
            
        开发者: @ConceptualGod
        """
        decisions = []
        
        try:
            # 非撤退模式下的跟随决策
            if current_mode != GameMode.RETREAT:
                decisions.append(Decision(
                    "follow_teammate",
                    self.priority_levels["follow_teammate"],
                    {"mode": current_mode.value}
                ))
            
        except Exception as e:
            self.logger.error(f"制定移动决策失败: {str(e)} - By @ConceptualGod")
        
        return decisions
    
    def _make_equipment_decisions(self, game_state: Dict[str, Any]) -> List[Decision]:
        """
        制定装备和锦囊相关决策
        
        Args:
            game_state: 游戏状态信息
            
        Returns:
            List[Decision]: 装备决策列表
            
        开发者: @ConceptualGod
        """
        decisions = []
        
        try:
            game_time = game_state.get("game_time", 0)
            
            # 锦囊处理时机
            if self._should_handle_jinnang(game_time):
                decisions.append(Decision(
                    "handle_jinnang",
                    self.priority_levels["handle_jinnang"],
                    {"game_time": game_time}
                ))
            
            # 装备购买时机
            if self._should_buy_equipment(game_time):
                decisions.append(Decision(
                    "buy_equipment",
                    self.priority_levels["buy_equipment"],
                    {"game_time": game_time}
                ))
            
        except Exception as e:
            self.logger.error(f"制定装备决策失败: {str(e)} - By @ConceptualGod")
        
        return decisions
    
    def _make_level_decisions(self, game_state: Dict[str, Any]) -> List[Decision]:
        """
        制定升级相关决策
        
        Args:
            game_state: 游戏状态信息
            
        Returns:
            List[Decision]: 升级决策列表
            
        开发者: @ConceptualGod
        """
        decisions = []
        
        try:
            # 这里可以添加等级检测和加点逻辑
            # 目前简化处理
            pass
            
        except Exception as e:
            self.logger.error(f"制定升级决策失败: {str(e)} - By @ConceptualGod")
        
        return decisions
    
    def _should_handle_jinnang(self, game_time: float) -> bool:
        """
        检查是否应该处理锦囊
        
        Args:
            game_time: 游戏时间（秒）
            
        Returns:
            bool: 是否应该处理锦囊
            
        开发者: @ConceptualGod
        """
        try:
            game_minutes = game_time / 60
            
            # 2分钟和10分钟处理锦囊
            jinnang_times = [2, 10]
            
            for target_time in jinnang_times:
                if abs(game_minutes - target_time) < 0.5:  # 30秒误差范围
                    # 检查是否已经处理过
                    recent_decisions = [d for d in self.decision_history[-10:] 
                                      if d.action == "handle_jinnang"]
                    if not recent_decisions:
                        return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查锦囊处理时机失败: {str(e)} - By @ConceptualGod")
            return False
    
    def _should_buy_equipment(self, game_time: float) -> bool:
        """
        检查是否应该购买装备
        
        Args:
            game_time: 游戏时间（秒）
            
        Returns:
            bool: 是否应该购买装备
            
        开发者: @ConceptualGod
        """
        try:
            game_minutes = game_time / 60
            
            # 每5分钟检查装备购买
            if game_minutes > 0 and int(game_minutes) % 5 == 0:
                # 检查是否已经处理过
                recent_decisions = [d for d in self.decision_history[-5:] 
                                  if d.action == "buy_equipment"]
                if not recent_decisions:
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查装备购买时机失败: {str(e)} - By @ConceptualGod")
            return False
    
    def _record_decision(self, decision: Decision):
        """
        记录决策历史
        
        Args:
            decision: 决策对象
            
        开发者: @ConceptualGod
        """
        try:
            self.decision_history.append(decision)
            self.last_decision_time = time.time()
            
            # 保持历史记录在合理范围内
            if len(self.decision_history) > 100:
                self.decision_history = self.decision_history[-50:]
            
            self.logger.debug(f"决策记录: {decision.action} (优先级: {decision.priority}) - By @ConceptualGod")
            
        except Exception as e:
            self.logger.error(f"记录决策失败: {str(e)} - By @ConceptualGod")
    
    def get_recent_decisions(self, count: int = 10) -> List[Decision]:
        """
        获取最近的决策记录
        
        Args:
            count: 获取数量
            
        Returns:
            List[Decision]: 最近的决策列表
            
        开发者: @ConceptualGod
        """
        return self.decision_history[-count:] if self.decision_history else []
    
    def get_decision_stats(self) -> Dict[str, Any]:
        """
        获取决策统计信息
        
        Returns:
            Dict: 决策统计
            
        开发者: @ConceptualGod
        """
        try:
            if not self.decision_history:
                return {"total_decisions": 0}
            
            # 统计各类决策数量
            action_counts = {}
            for decision in self.decision_history:
                action_counts[decision.action] = action_counts.get(decision.action, 0) + 1
            
            return {
                "total_decisions": len(self.decision_history),
                "action_counts": action_counts,
                "last_decision_time": self.last_decision_time,
                "recent_decisions": [d.action for d in self.get_recent_decisions(5)]
            }
            
        except Exception as e:
            self.logger.error(f"获取决策统计失败: {str(e)} - By @ConceptualGod")
            return {"error": str(e)}
