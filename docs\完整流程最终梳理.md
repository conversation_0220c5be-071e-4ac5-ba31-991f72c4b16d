# 起凡自动化脚本完整流程最终梳理

**开发者:** @ConceptualGod  
**版本:** v2.0 Final  
**梳理时间:** 2025-08-05  
**项目:** 起凡游戏自动化脚本

## 📋 完整流程概述

按照您的完整项目步骤流程，所有功能已集成到登录控制器中，清除了所有TODO，实现了完整的自动化流程。

## 🔄 单个账号完整流程（约33分钟）

### 阶段1：登录准备（~3秒）
**步骤1-3：窗口检测和准备**
```python
# 1. 检测游戏窗口
if not self.automation.detect_game_window():
    # 跳过当前账号

# 2. 将游戏窗口置于前台  
if not self.automation.bring_window_to_front():
    # 跳过当前账号

# 3. 等待窗口稳定
time.sleep(1)
```

### 阶段2：账号登录（~20秒）
**步骤4：执行login.json坐标操作**
```python
# 4.1 点击账号输入框 (827, 580)
# 4.2 点击密码输入框 (767, 604)  
# 4.3 点击登录按钮 (917, 592)
# 4.4 输入账号密码
# 4.5 等待登录结果（15秒网络延迟）
```

### 阶段3：游戏前准备（~10秒）
**步骤5：游戏任务前等待**
```python
time.sleep(10)  # 确保界面完全稳定
```

### 阶段4：任务大厅操作（~10秒）
**步骤6：执行coordinates_1.json操作**
```python
# 6.1 点击任务大厅 (322, 327)
# 6.2 领取每日登录奖励 (902, 647)
# 6.3 确认每日奖励 (953, 663)
# 6.4 领取每周奖励 (1028, 659)
# 6.5 选择胜利挑战困难模式 (1160, 604)
# 6.6 接受挑战 (888, 706)
```

### 阶段5：界面关闭（~2秒）
**步骤7：执行close.json操作**
```python
# 7.1 关闭欢迎窗口 (1892, 905)
```

### 阶段6：战功操作（~8秒）
**步骤8：执行coordinates_2.json操作**
```python
# 8.1 点击战功 (320, 553)
# 8.2 点击切换期数按钮 (1474, 487)
# 8.3 选择第四期战功 (896, 659)
# 8.4 点击开启战功按钮 (806, 727)
```

### 阶段7：任务识别（~10秒）
**步骤9：战功任务识别**
```python
recommended_hero = self._execute_integrated_task_recognition(username, account_index)
# 9.1 初始化任务识别系统
# 9.2 截取战功界面区域
# 9.3 EasyOCR识别任务文本
# 9.4 智能匹配任务类型
# 9.5 推荐适合的英雄
# 9.6 返回推荐英雄名称
```

### 阶段8：游戏启动（~25秒）
**步骤10：执行coordinates_3.json操作**
```python
game_result = self._execute_integrated_game_starter(username, account_index, recommended_hero)
# 10.1 点击群雄逐鹿 (27, 249)
# 10.2 双击武勋专房1 (124, 232)
# 10.3 EasyOCR检测确定按钮
# 10.4 处理对话框（如有）
# 10.5 点击开始游戏 (972, 174)
```

### 阶段9：游戏窗口监控（~10分钟等待）
**步骤11：游戏窗口监控**
```python
# 11.1 每5秒扫描系统窗口
# 11.2 查找"起凡游戏：群雄逐鹿+版本号"窗口
# 11.3 优先选择第二个窗口（游戏开始窗口）
# 11.4 最多等待10分钟
# 11.5 超时重试：两次点击开始游戏（取消+重新开始）
# 11.6 最多重试3次
# 11.7 检测到游戏窗口后立即强制置前
```

### 阶段10：英雄选择（~20秒）
**步骤12：英雄选择流程**
```python
# 12.1 等待15秒让游戏界面完全加载
# 12.2 根据推荐英雄选择坐标（herochoose.json）
#      - 刘备: (675, 223)
#      - 华佗: (743, 230)
#      - 陆逊: (927, 277)
#      - 诸葛瑾: (922, 387)
#      - 孙权: (982, 275)
#      - 曹操: (1335, 224)
# 12.3 确认英雄选择（querenhero.json）: (1342, 685)
```

### 阶段11：魂玉搭配（~15秒）
**步骤13：魂玉搭配流程（hunyudapei.json）**
```python
# 13.1 生命选择 (783, 239)
# 13.2 生命上限 (867, 410)
# 13.3 防御选择 (777, 308)
# 13.4 物理防御 (921, 468)
# 13.5 法术选择 (775, 388)
# 13.6 法术抗性 (855, 532)
# 13.7 冷却选择 (772, 473)
# 13.8 冷却缩减 (851, 483)
# 13.9 套装选择 (767, 536)
# 13.10 玄武套装 (1037, 546)
# 13.11 进入游戏 (926, 689)
```

### 阶段12：游戏内操作（~30分钟）
**步骤14：游戏内智能操作系统**
```python
# 14.1 初始操作（0-30秒）
#      - 等待3秒游戏加载完成
#      - 按B键购买速度之靴
#      - 执行1级加点生命值
#      - 关闭商店界面

# 14.2 三模式智能操作系统
#      A. 发育模式（前20分钟）
#         - 兵线发育，吃共享经济
#         - 2000码范围敌人≥3个时后退
#         - 跟随队友300码距离
#         - 野区规则：蜀国下路，魏国上路
#      
#      B. 跟随模式（20分钟后）
#         - 跟随MVP最高队友
#         - 可进入防御塔范围
#         - 主动寻找战斗机会
#      
#      C. 战斗模式（遇敌触发）
#         - 攻击优先级：英雄>小兵>野怪
#         - 600码范围技能释放
#         - 1500码安全撤退

# 14.3 英雄专属技能释放
#      - 华佗: W治疗血量<80%队友，D攻击敌人
#      - 刘备: C和E攻击600码内敌人
#      - 诸葛瑾: E和W攻击600码内敌人
#      - 陆逊: E攻击600码内敌人
#      - 孙权: E攻击600码内敌人
#      - 曹操: C攻击600码内敌人

# 14.4 生存保障系统
#      - 血量<80%: 自动使用玄铁盾（快捷键2）
#      - 血量<40%或蓝量<10%: 紧急撤退使用奔雷靴（快捷键1）
#      - 自动回城: 撤离到安全距离后按Y回城
#      - 复活处理: 血量满后自动重新出门

# 14.5 锦囊装备管理
#      - 军机锦囊: 游戏2分钟时处理，重转获取黄金锦囊玄铁盾
#      - 白色锦囊: 游戏10分钟时处理，选择中间的麒麟心
#      - 装备购买: 每5分钟检查金钱，按chuzhuang.json顺序出装
#      - 特殊装备: 跳鞋放第1格，玄铁盾放第2格

# 14.6 升级加点系统
#      - 15级加点: 检测到15级时执行jiadianshengmingzhi.json步骤4
#      - 25级加点: 检测到25级时执行jiadianshengmingzhi.json步骤5
#      - 加点内容: 全部选择生命值提升
```

### 阶段13：游戏结束检测（~5秒）
**步骤15：游戏结束检测和处理**
```python
# 15.1 每2秒检测游戏结束界面
# 15.2 OCR识别胜利/失败关键词
# 15.3 记录游戏结果和游戏时长
# 15.4 更新账号状态统计
# 15.5 等待返回大厅界面（最多30秒）
# 15.6 停止游戏内操作循环
```

### 阶段14：战功监控（~10秒）
**步骤16：战功任务进度监控**
```python
has_completed_tasks = self._execute_integrated_progress_monitor(username, account_index)
# 16.1 等待3秒界面稳定
# 16.2 执行zhangongpick.json监控坐标（11个监控点）
# 16.3 OCR识别任务完成状态
# 16.4 检测数字是否达到一定数值（如30/30）
# 16.5 如果达到目标数值，自动点击领取奖励
# 16.6 返回是否有完成的战功任务
```

### 阶段15：换号判断（智能决策）
**步骤17：判断是否需要换号**
```python
if has_completed_tasks:
    # 17.1 有完成的战功任务：自动领取奖励
    # 17.2 继续当前账号，重新开始游戏流程（回到步骤8：战功操作）
    continue  # 重新开始游戏循环
else:
    # 17.3 无对应战功任务：执行换号流程
```

### 阶段16：换号前奖励领取（~10秒）
**步骤18：换号前领取任务大厅奖励**
```python
# 18.1 执行coordinates_1.json操作
# 18.2 领取每日/每周奖励
# 18.3 确保所有奖励都已领取
```

### 阶段17：账号切换（~46秒）
**步骤19：执行exit.json退出操作**
```python
# 19.1 点击退出 (1891, 2)
# 19.2 点击切换账号 (1112, 572)
# 19.3 等待6秒退出操作完成
# 19.4 检测回到登录平台（最多20秒）
# 19.5 确认窗口标题包含"发布时间"
# 19.6 等待15秒登录平台界面稳定
```

**步骤20：准备下一个账号**
```python
# 20.1 点击账号输入框 (827, 580)
# 20.2 使用退格键清除上一个账号名（删除15个字符）
# 20.3 开始下一个账号的完整流程（回到步骤1）
```

## 🎯 关键实现点

### 1. 战功识别英雄推荐 ✅
- EasyOCR识别战功任务类型
- 智能匹配推荐最适合的英雄
- 传递推荐英雄给游戏启动控制器

### 2. 自动英雄选择 ✅
- 根据推荐英雄在herochoose.json中查找坐标
- 自动点击对应英雄
- 确认选择并进行魂玉搭配

### 3. 战功监控数字检测 ✅
- 扫描zhangongpick.json的11个监控点
- OCR识别当前进度数字
- 检测是否达到目标数值（如30/30）
- 达到目标自动点击领取

### 4. 智能换号逻辑 ✅
- 有完成任务：继续当前账号，重新开始游戏
- 无对应任务：领取任务大厅奖励后换号

### 5. 状态显示集成 ✅
- 账号状态：胜利/失败/逃跑统计显示在日志中
- 任务进度：战功任务完成数显示在日志中

## 🚫 已清除的TODO

- ❌ 删除了所有TODO注释
- ✅ 完整实现了游戏内操作系统
- ✅ 完整实现了英雄选择和魂玉搭配
- ✅ 完整实现了战功监控和智能换号
- ✅ 完整实现了状态显示和日志记录

---

**最终确认:** 2025-08-05  
**开发者:** @ConceptualGod  
**状态:** 完整流程已实现，无TODO，按步骤执行  
**特点:** 智能英雄选择，数字监控领取，智能换号判断
