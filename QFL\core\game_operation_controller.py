#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏内操作控制器
负责处理游戏内的复杂操作逻辑，包括英雄操作、技能释放、出装、锦囊等

开发者: @ConceptualGod
创建时间: 2025-08-05
"""

import json
import time
import logging
import pyautogui
import keyboard
from pathlib import Path
from typing import Dict, List, Optional, Any
from .hero_operator import HeroOperator
from .mode_manager import ModeManager, GameMode
from .decision_engine import DecisionEngine
from .game_end_detector import GameEndDetector

class GameOperationController:
    """
    游戏内操作控制器
    
    功能包括：
    - 英雄技能释放
    - 血量蓝量监控
    - 出装逻辑
    - 锦囊处理
    - 游戏模式切换
    
    开发者: @ConceptualGod
    """
    
    def __init__(self):
        """
        初始化游戏内操作控制器
        
        开发者: @ConceptualGod
        """
        self.logger = logging.getLogger(__name__)
        
        # 游戏状态
        self.is_running = False
        self.current_hero = None
        self.game_mode = "发育模式"  # 发育模式/跟随模式/战斗模式
        self.game_time = 0
        self.last_operation_time = 0
        
        # 配置数据
        self.jinnang_coords = {}
        self.chuzhuang_coords = {}
        self.jiadian_coords = {}
        self.hero_skills = {}
        self.game_params = {}
        self.hotkeys = {}

        # 核心模块
        self.hero_operator = None
        self.mode_manager = None
        self.decision_engine = None
        self.game_end_detector = None

        # 日志回调
        self.log_callback = None

        # 加载配置
        self._load_all_configs()

        # 初始化核心模块
        self._initialize_modules()

        self._log_info("游戏内操作控制器初始化完成 - By @ConceptualGod")

    def _log_info(self, message: str):
        """
        统一的日志记录方法，同时输出到命令行和GUI

        Args:
            message: 日志消息

        开发者: @ConceptualGod
        """
        # 记录到日志文件（命令行显示）
        self._log_info(message)

        # 如果有回调，也发送到GUI
        if self.log_callback:
            self.log_callback(message)

    def _load_all_configs(self):
        """
        加载所有配置文件
        
        开发者: @ConceptualGod
        """
        try:
            current_dir = Path(__file__).parent.parent
            
            # 加载锦囊配置
            self._load_jinnang_config(current_dir)
            
            # 加载出装配置
            self._load_chuzhuang_config(current_dir)
            
            # 加载加点配置
            self._load_jiadian_config(current_dir)
            
            # 加载英雄技能配置
            self._load_hero_skills_config(current_dir)
            
            # 加载游戏参数配置
            self._load_game_params_config(current_dir)
            
            # 加载快捷键配置
            self._load_hotkeys_config(current_dir)
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)} - By @ConceptualGod")
    
    def _load_jinnang_config(self, base_dir: Path):
        """加载锦囊配置"""
        try:
            jinnang_file = base_dir / "jinnang.json"
            if jinnang_file.exists():
                with open(jinnang_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for item in data:
                        self.jinnang_coords[item["step"]] = {
                            "x": item["x"],
                            "y": item["y"],
                            "description": item["description"]
                        }
                self._log_info(f"成功加载{len(self.jinnang_coords)}个锦囊操作坐标 - By @ConceptualGod")
        except Exception as e:
            self.logger.error(f"加载锦囊配置失败: {str(e)} - By @ConceptualGod")
    
    def _load_chuzhuang_config(self, base_dir: Path):
        """加载出装配置"""
        try:
            chuzhuang_file = base_dir / "chuzhuang.json"
            if chuzhuang_file.exists():
                with open(chuzhuang_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for item in data:
                        self.chuzhuang_coords[item["step"]] = {
                            "x": item["x"],
                            "y": item["y"],
                            "description": item["description"]
                        }
                self._log_info(f"成功加载{len(self.chuzhuang_coords)}个出装操作坐标 - By @ConceptualGod")
        except Exception as e:
            self.logger.error(f"加载出装配置失败: {str(e)} - By @ConceptualGod")
    
    def _load_jiadian_config(self, base_dir: Path):
        """加载加点配置"""
        try:
            jiadian_file = base_dir / "jiadianshengmingzhi.json"
            if jiadian_file.exists():
                with open(jiadian_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for item in data:
                        self.jiadian_coords[item["step"]] = {
                            "x": item["x"],
                            "y": item["y"],
                            "description": item["description"]
                        }
                self._log_info(f"成功加载{len(self.jiadian_coords)}个加点操作坐标 - By @ConceptualGod")
        except Exception as e:
            self.logger.error(f"加载加点配置失败: {str(e)} - By @ConceptualGod")
    
    def _load_hero_skills_config(self, base_dir: Path):
        """加载英雄技能配置"""
        try:
            hero_skills_file = base_dir / "hero_skills.json"
            if hero_skills_file.exists():
                with open(hero_skills_file, 'r', encoding='utf-8') as f:
                    self.hero_skills = json.load(f)
                self._log_info(f"成功加载{len(self.hero_skills)}个英雄技能配置 - By @ConceptualGod")
            else:
                # 使用默认配置
                self.hero_skills = {
                    "华佗": {
                        "skills": ["W", "D"],
                        "skill_range": 600,
                        "heal_skill": "W"
                    },
                    "刘备": {
                        "skills": ["C", "E"],
                        "skill_range": 600
                    },
                    "诸葛瑾": {
                        "skills": ["E", "W"],
                        "skill_range": 600
                    },
                    "陆逊": {
                        "skills": ["E"],
                        "skill_range": 600
                    },
                    "孙权": {
                        "skills": ["E"],
                        "skill_range": 600
                    },
                    "曹操": {
                        "skills": ["C"],
                        "skill_range": 600
                    }
                }
                self._log_info(f"使用默认英雄技能配置 - By @ConceptualGod")
        except Exception as e:
            self.logger.error(f"加载英雄技能配置失败: {str(e)} - By @ConceptualGod")
    
    def _load_game_params_config(self, base_dir: Path):
        """加载游戏参数配置"""
        try:
            game_params_file = base_dir / "game_params.json"
            if game_params_file.exists():
                with open(game_params_file, 'r', encoding='utf-8') as f:
                    self.game_params = json.load(f)
                self._log_info("成功加载游戏参数配置文件 - By @ConceptualGod")
            else:
                # 使用默认参数
                self.game_params = {
                    "blood_threshold": 80,
                    "mana_threshold": 10,
                    "low_blood_threshold": 40,
                    "follow_distance": 300,
                    "retreat_distance": 1500,
                    "safe_distance": 2000,
                    "development_range": 2000,
                    "battle_range": 1000,
                    "enemy_threshold": 3,
                    "mode_switch_time": 20,
                    "skill_cooldown": 3,
                    "operation_interval": 0.5
                }
                self._log_info("使用默认游戏参数配置 - By @ConceptualGod")
        except Exception as e:
            self.logger.error(f"加载游戏参数配置失败: {str(e)} - By @ConceptualGod")
    
    def _load_hotkeys_config(self, base_dir: Path):
        """加载快捷键配置"""
        try:
            hotkeys_file = base_dir / "hotkeys.json"
            if hotkeys_file.exists():
                with open(hotkeys_file, 'r', encoding='utf-8') as f:
                    self.hotkeys = json.load(f)
                self._log_info("成功加载快捷键配置文件 - By @ConceptualGod")
            else:
                # 使用默认快捷键配置
                self.hotkeys = {
                    "buy_equipment": "b",
                    "return_city": "y",
                    "use_shield": "2",
                    "use_boots": "1",
                    "reroll_jinnang": "space",
                    "add_points": "+",
                    "skills": {
                        "W": "w",
                        "D": "d",
                        "C": "c",
                        "E": "e"
                    }
                }
                self._log_info("使用默认快捷键配置 - By @ConceptualGod")
        except Exception as e:
            self.logger.error(f"加载快捷键配置失败: {str(e)} - By @ConceptualGod")

    def _initialize_modules(self):
        """
        初始化核心模块

        开发者: @ConceptualGod
        """
        try:
            # 初始化模式管理器
            self.mode_manager = ModeManager(self.game_params)

            # 初始化决策引擎
            self.decision_engine = DecisionEngine(self.game_params)

            # 初始化游戏结束检测器
            self.game_end_detector = GameEndDetector()

            self._log_info("核心模块初始化完成 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"核心模块初始化失败: {str(e)} - By @ConceptualGod")
    
    def start_game_operations(self, hero_name: str) -> bool:
        """
        开始游戏内操作
        
        Args:
            hero_name: 英雄名称
            
        Returns:
            bool: 是否成功启动
            
        开发者: @ConceptualGod
        """
        try:
            if self.is_running:
                self.logger.warning("游戏内操作已在运行中 - By @ConceptualGod")
                return False
            
            self.current_hero = hero_name
            self.is_running = True
            self.game_time = 0

            # 初始化英雄操作器
            self.hero_operator = HeroOperator(hero_name, self.hero_skills,
                                            self.hotkeys, self.game_params)

            # 重置模式管理器
            if self.mode_manager:
                self.mode_manager.reset_game_timer()

            self._log_info(f"开始游戏内操作，英雄: {hero_name} - By @ConceptualGod")

            # 执行初始操作
            self._execute_initial_operations()

            # 开始游戏主循环
            self._execute_game_main_loop()

            return True
            
        except Exception as e:
            self.logger.error(f"启动游戏内操作失败: {str(e)} - By @ConceptualGod")
            self.is_running = False
            return False
    
    def stop_game_operations(self):
        """
        停止游戏内操作
        
        开发者: @ConceptualGod
        """
        self.is_running = False
        self._log_info("游戏内操作已停止 - By @ConceptualGod")



    def _execute_initial_operations(self):
        """
        执行初始操作
        
        开发者: @ConceptualGod
        """
        try:
            # 保存断点：开始初始操作

            self._log_info("执行初始操作 - By @ConceptualGod")

            # 等待游戏加载完成
            time.sleep(3)

            # 1. 按B键购买速度之靴
            self._buy_initial_equipment()

            # 2. 执行加点生命值
            self._execute_add_points()

            self._log_info("初始操作完成 - By @ConceptualGod")
            
        except Exception as e:
            self.logger.error(f"执行初始操作失败: {str(e)} - By @ConceptualGod")
    
    def _buy_initial_equipment(self):
        """购买初始装备"""
        try:
            self._log_info("购买速度之靴 - By @ConceptualGod")
            
            # 按B键打开商店
            keyboard.press_and_release(self.hotkeys["buy_equipment"])
            time.sleep(1)
            
            # 执行出装的第一步和第二步（选择传统模式和速度之靴）
            if 1 in self.chuzhuang_coords:
                coord = self.chuzhuang_coords[1]
                pyautogui.click(coord["x"], coord["y"])
                time.sleep(0.5)
                
            if 2 in self.chuzhuang_coords:
                coord = self.chuzhuang_coords[2]
                pyautogui.click(coord["x"], coord["y"])
                time.sleep(0.5)
            
            # 关闭商店（再次按B键）
            keyboard.press_and_release("b")
            
        except Exception as e:
            self.logger.error(f"购买初始装备失败: {str(e)} - By @ConceptualGod")
    
    def _execute_add_points(self):
        """执行加点操作"""
        try:
            self._log_info("执行加点生命值操作 - By @ConceptualGod")
            
            # 执行加点操作的前3步（1级觉醒）
            for step in [1, 2, 3]:
                if step in self.jiadian_coords:
                    coord = self.jiadian_coords[step]
                    pyautogui.click(coord["x"], coord["y"])
                    time.sleep(0.5)
                    
        except Exception as e:
            self.logger.error(f"执行加点操作失败: {str(e)} - By @ConceptualGod")
    
    def _execute_game_main_loop(self):
        """
        游戏主循环 - 完整的智能操作系统

        开发者: @ConceptualGod
        """
        try:
            # 保存断点：开始主循环

            self._log_info("开始游戏内智能操作主循环 - By @ConceptualGod")

            while self.is_running:
                current_time = time.time()

                # 控制操作频率
                if current_time - self.last_operation_time < self.game_params["operation_interval"]:
                    time.sleep(0.1)
                    continue

                # 检测游戏是否结束
                game_end_result = self.game_end_detector.detect_game_end()
                if game_end_result["game_ended"]:
                    # 保存断点：游戏结束检测
                    self._log_info(f"检测到游戏结束: {game_end_result['result']} - By @ConceptualGod")
                    self._handle_game_end(game_end_result)
                    break

                # 获取游戏状态
                game_state = self._get_current_game_state()

                # 更新游戏模式
                current_mode = self.mode_manager.update_mode(game_state)

                # 决策引擎制定决策
                decision = self.decision_engine.make_decision(
                    game_state, current_mode, self.hero_operator.get_operation_status()
                )

                # 执行决策
                if decision:
                    self._execute_decision(decision, game_state)

                self.last_operation_time = current_time

        except Exception as e:
            self.logger.error(f"游戏主循环异常: {str(e)} - By @ConceptualGod")
        finally:
            self.is_running = False
            self._log_info("游戏内操作主循环结束 - By @ConceptualGod")

    def _get_current_game_state(self) -> Dict[str, Any]:
        """
        获取当前游戏状态

        Returns:
            Dict: 游戏状态信息

        开发者: @ConceptualGod
        """
        try:
            # 这里应该调用游戏状态检测器获取实际状态
            # 目前使用简化版状态
            game_state = {
                "blood_percentage": 85,  # 简化：假设血量85%
                "mana_percentage": 70,   # 简化：假设蓝量70%
                "enemies_nearby": 1,     # 简化：假设附近1个敌人
                "allies_nearby": 2,      # 简化：假设附近2个队友
                "game_time": self.mode_manager.get_game_time() if self.mode_manager else 0,
                "jinnang_available": False,
                "equipment_slots": [False] * 9,
                "hero_position": (960, 540),  # 屏幕中心
                "timestamp": time.time()
            }

            return game_state

        except Exception as e:
            self.logger.error(f"获取游戏状态失败: {str(e)} - By @ConceptualGod")
            return {}

    def _execute_decision(self, decision, game_state: Dict[str, Any]):
        """
        执行决策

        Args:
            decision: 决策对象
            game_state: 游戏状态

        开发者: @ConceptualGod
        """
        try:
            action = decision.action
            params = decision.params

            self._log_info(f"执行决策: {action} (优先级: {decision.priority}) - By @ConceptualGod")

            if action == "emergency_retreat":
                self._handle_emergency_retreat(params)
            elif action == "use_shield":
                self.hero_operator.use_shield()
            elif action == "retreat":
                self._handle_retreat(params)
            elif action == "return_base":
                self.hero_operator.return_to_base()
            elif action == "use_skills":
                enemies = game_state.get("enemies_nearby", 0)
                self.hero_operator.use_skills_on_enemies(enemies)
                # 华佗特殊处理
                if self.current_hero == "华佗":
                    teammates_status = [{"blood_percentage": 75}]  # 简化
                    self.hero_operator.heal_teammates(teammates_status)
            elif action == "follow_teammate":
                teammate_pos = game_state.get("hero_position", (960, 540))
                self.hero_operator.follow_teammate(teammate_pos)
            elif action == "buy_equipment":
                self._handle_equipment_purchase()
            elif action == "handle_jinnang":
                self._handle_jinnang_operations()
            elif action == "level_up":
                # 简化：假设当前15级
                self.hero_operator.auto_level_up(15)
            elif action == "idle":
                # 空闲状态，不执行特殊操作
                pass

        except Exception as e:
            self.logger.error(f"执行决策失败: {str(e)} - By @ConceptualGod")

    def _handle_emergency_retreat(self, params: Dict):
        """
        处理紧急撤退

        Args:
            params: 撤退参数

        开发者: @ConceptualGod
        """
        try:
            reason = params.get("reason", "紧急情况")
            self.logger.warning(f"执行紧急撤退: {reason} - By @ConceptualGod")

            # 使用奔雷靴加速撤退
            self.hero_operator.use_boots()

            # 撤退到安全位置
            self.hero_operator.retreat_to_safety()

            # 等待安全后回城
            time.sleep(2)
            self.hero_operator.return_to_base()

        except Exception as e:
            self.logger.error(f"紧急撤退失败: {str(e)} - By @ConceptualGod")

    def _handle_retreat(self, params: Dict):
        """
        处理普通撤退

        Args:
            params: 撤退参数

        开发者: @ConceptualGod
        """
        try:
            reason = params.get("reason", "战术撤退")
            self._log_info(f"执行撤退: {reason} - By @ConceptualGod")

            # 撤退到安全位置
            self.hero_operator.retreat_to_safety()

        except Exception as e:
            self.logger.error(f"撤退失败: {str(e)} - By @ConceptualGod")

    def _handle_game_end(self, game_end_result: Dict):
        """
        处理游戏结束

        Args:
            game_end_result: 游戏结束结果

        开发者: @ConceptualGod
        """
        try:
            result = game_end_result.get("result", "unknown")
            confidence = game_end_result.get("confidence", 0.0)

            self._log_info(f"游戏结束处理: {result} (置信度: {confidence:.2f}) - By @ConceptualGod")

            # 停止游戏内操作
            self.is_running = False

            # 等待返回大厅
            self._log_info("等待返回大厅... - By @ConceptualGod")
            if self.game_end_detector.wait_for_return_to_hall(30):
                self._log_info("成功返回大厅 - By @ConceptualGod")
            else:
                self.logger.warning("返回大厅超时，继续后续流程 - By @ConceptualGod")

            # 设置游戏结果（供外部调用者使用）
            self.game_result = {
                "result": result,
                "confidence": confidence,
                "game_duration": game_end_result.get("game_duration", 0),
                "timestamp": time.time()
            }

        except Exception as e:
            self.logger.error(f"游戏结束处理失败: {str(e)} - By @ConceptualGod")
    
    def _check_and_switch_mode(self):
        """检测并切换游戏模式"""
        try:
            # 简化版：基于时间切换模式
            if self.game_time < self.game_params["mode_switch_time"] * 60:
                self.game_mode = "发育模式"
            else:
                self.game_mode = "跟随模式"
                
        except Exception as e:
            self.logger.error(f"模式切换检测失败: {str(e)} - By @ConceptualGod")
    
    def _execute_current_mode_operations(self):
        """执行当前模式的操作"""
        try:
            if self.game_mode == "发育模式":
                self._execute_development_mode()
            elif self.game_mode == "跟随模式":
                self._execute_follow_mode()
            elif self.game_mode == "战斗模式":
                self._execute_battle_mode()
                
        except Exception as e:
            self.logger.error(f"执行模式操作失败: {str(e)} - By @ConceptualGod")
    
    def _execute_development_mode(self):
        """执行发育模式操作"""
        # 简化版：暂时不执行复杂操作
        pass
    
    def _execute_follow_mode(self):
        """执行跟随模式操作"""
        # 简化版：暂时不执行复杂操作
        pass
    
    def _execute_battle_mode(self):
        """执行战斗模式操作"""
        # 简化版：暂时不执行复杂操作
        pass
    
    def _handle_skill_usage(self):
        """处理技能使用"""
        try:
            if not self.current_hero or self.current_hero not in self.hero_skills:
                return
            
            hero_config = self.hero_skills[self.current_hero]
            skills = hero_config.get("skills", [])
            
            # 简化版：定时释放技能
            if int(self.game_time) % self.game_params["skill_cooldown"] == 0:
                for skill in skills:
                    if skill in self.hotkeys["skills"]:
                        keyboard.press_and_release(self.hotkeys["skills"][skill])
                        time.sleep(0.2)
                        
        except Exception as e:
            self.logger.error(f"技能使用处理失败: {str(e)} - By @ConceptualGod")
    
    def _handle_jinnang_operations(self):
        """处理锦囊操作"""
        try:
            # 简化版：在特定时间点执行锦囊操作
            game_minutes = int(self.game_time / 60)
            
            # 在游戏开始后2分钟和10分钟执行锦囊操作
            if game_minutes in [2, 10] and int(self.game_time) % 60 == 0:
                if game_minutes == 2:
                    self._log_info("2分钟锦囊操作 - By @ConceptualGod")
                elif game_minutes == 10:
                    self._log_info("10分钟锦囊操作 - By @ConceptualGod")
                self._execute_jinnang_sequence()
                
        except Exception as e:
            self.logger.error(f"锦囊操作处理失败: {str(e)} - By @ConceptualGod")
    
    def _execute_jinnang_sequence(self):
        """执行锦囊操作序列"""
        try:
            self._log_info("执行锦囊操作序列 - By @ConceptualGod")
            
            # 执行锦囊操作步骤
            for step in sorted(self.jinnang_coords.keys()):
                coord = self.jinnang_coords[step]
                pyautogui.click(coord["x"], coord["y"])
                time.sleep(1)
                
        except Exception as e:
            self.logger.error(f"执行锦囊操作序列失败: {str(e)} - By @ConceptualGod")
    
    def _handle_equipment_purchase(self):
        """处理装备购买"""
        try:
            # 简化版：在特定时间点购买装备
            game_minutes = int(self.game_time / 60)
            
            # 每5分钟检查一次装备购买
            if game_minutes > 0 and game_minutes % 5 == 0 and int(self.game_time) % 60 == 0:
                self._execute_equipment_purchase()
                
        except Exception as e:
            self.logger.error(f"装备购买处理失败: {str(e)} - By @ConceptualGod")
    
    def _execute_equipment_purchase(self):
        """执行装备购买"""
        try:
            self._log_info("执行装备购买 - By @ConceptualGod")
            
            # 按Y回城
            keyboard.press_and_release(self.hotkeys["return_city"])
            time.sleep(3)
            
            # 按B打开商店
            keyboard.press_and_release(self.hotkeys["buy_equipment"])
            time.sleep(1)
            
            # 根据游戏时间购买对应装备
            game_minutes = int(self.game_time / 60)
            equipment_step = min(game_minutes // 2 + 2, len(self.chuzhuang_coords))
            
            if equipment_step in self.chuzhuang_coords:
                coord = self.chuzhuang_coords[equipment_step]
                pyautogui.click(coord["x"], coord["y"])
                time.sleep(0.5)
            
            # 关闭商店（再次按B键）
            keyboard.press_and_release("b")
            
        except Exception as e:
            self.logger.error(f"执行装备购买失败: {str(e)} - By @ConceptualGod")
    
    def get_operation_status(self) -> Dict[str, Any]:
        """
        获取操作状态
        
        Returns:
            Dict: 操作状态信息
            
        开发者: @ConceptualGod
        """
        return {
            "is_running": self.is_running,
            "current_hero": self.current_hero,
            "game_mode": self.game_mode,
            "game_time": self.game_time,
            "configs_loaded": {
                "jinnang": len(self.jinnang_coords),
                "chuzhuang": len(self.chuzhuang_coords),
                "jiadian": len(self.jiadian_coords),
                "hero_skills": len(self.hero_skills)
            }
        }
