#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号状态管理器
负责管理每个账号的胜利/失败/逃跑状态和战功任务完成数

开发者: @ConceptualGod
创建时间: 2025-08-05
"""

import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any

class AccountStatusManager:
    """
    账号状态管理器
    
    功能包括：
    - 记录每个账号的游戏结果（胜利/失败/逃跑）
    - 统计战功任务完成数
    - 提供状态查询和更新接口
    - 数据持久化存储
    
    开发者: @ConceptualGod
    """
    
    def __init__(self):
        """
        初始化账号状态管理器
        
        开发者: @ConceptualGod
        """
        self.logger = logging.getLogger(__name__)
        
        # 状态数据
        self.account_status = {}  # 账号状态数据
        self.task_completion_stats = {}  # 任务完成统计
        
        # 数据文件路径
        self.data_dir = Path(__file__).parent.parent / "data"
        self.status_file = self.data_dir / "account_status.json"
        self.task_stats_file = self.data_dir / "task_completion_stats.json"
        
        # 确保数据目录存在
        self.data_dir.mkdir(exist_ok=True)
        
        # 加载现有数据
        self._load_data()
        
        self.logger.info("账号状态管理器初始化完成 - By @ConceptualGod")
    
    def _load_data(self):
        """
        加载现有数据
        
        开发者: @ConceptualGod
        """
        try:
            # 加载账号状态数据
            if self.status_file.exists():
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    self.account_status = json.load(f)
                self.logger.info(f"加载账号状态数据: {len(self.account_status)}个账号 - By @ConceptualGod")
            
            # 加载任务完成统计
            if self.task_stats_file.exists():
                with open(self.task_stats_file, 'r', encoding='utf-8') as f:
                    self.task_completion_stats = json.load(f)
                self.logger.info(f"加载任务完成统计: {len(self.task_completion_stats)}个任务 - By @ConceptualGod")
                
        except Exception as e:
            self.logger.error(f"加载数据失败: {str(e)} - By @ConceptualGod")
            self.account_status = {}
            self.task_completion_stats = {}
    
    def _save_data(self):
        """
        保存数据到文件
        
        开发者: @ConceptualGod
        """
        try:
            # 保存账号状态数据
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.account_status, f, ensure_ascii=False, indent=2)
            
            # 保存任务完成统计
            with open(self.task_stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.task_completion_stats, f, ensure_ascii=False, indent=2)
                
            self.logger.debug("数据保存成功 - By @ConceptualGod")
            
        except Exception as e:
            self.logger.error(f"保存数据失败: {str(e)} - By @ConceptualGod")
    
    def update_game_result(self, username: str, result: str, game_duration: float = 0):
        """
        更新账号游戏结果
        
        Args:
            username: 账号用户名
            result: 游戏结果 ("victory", "defeat", "escape", "timeout")
            game_duration: 游戏时长（秒）
            
        开发者: @ConceptualGod
        """
        try:
            if username not in self.account_status:
                self.account_status[username] = {
                    "victory_count": 0,
                    "defeat_count": 0,
                    "escape_count": 0,
                    "total_games": 0,
                    "total_duration": 0,
                    "last_result": "",
                    "last_update": "",
                    "game_history": []
                }
            
            account_data = self.account_status[username]
            
            # 更新计数
            if result == "victory":
                account_data["victory_count"] += 1
            elif result == "defeat":
                account_data["defeat_count"] += 1
            elif result in ["escape", "timeout"]:
                account_data["escape_count"] += 1
            
            account_data["total_games"] += 1
            account_data["total_duration"] += game_duration
            account_data["last_result"] = result
            account_data["last_update"] = time.strftime("%Y-%m-%d %H:%M:%S")
            
            # 添加到历史记录（保留最近10条）
            game_record = {
                "result": result,
                "duration": game_duration,
                "timestamp": time.time()
            }
            account_data["game_history"].append(game_record)
            if len(account_data["game_history"]) > 10:
                account_data["game_history"] = account_data["game_history"][-10:]
            
            # 保存数据
            self._save_data()
            
            self.logger.info(f"更新账号 {username} 游戏结果: {result} - By @ConceptualGod")
            
        except Exception as e:
            self.logger.error(f"更新游戏结果失败: {str(e)} - By @ConceptualGod")
    
    def update_task_completion(self, task_name: str, current_progress: int, target_progress: int):
        """
        更新任务完成进度
        
        Args:
            task_name: 任务名称（如"功高绩伟"）
            current_progress: 当前进度
            target_progress: 目标进度
            
        开发者: @ConceptualGod
        """
        try:
            if task_name not in self.task_completion_stats:
                self.task_completion_stats[task_name] = {
                    "current_progress": 0,
                    "target_progress": 0,
                    "completion_rate": 0.0,
                    "last_update": "",
                    "progress_history": []
                }
            
            task_data = self.task_completion_stats[task_name]
            task_data["current_progress"] = current_progress
            task_data["target_progress"] = target_progress
            task_data["completion_rate"] = (current_progress / target_progress * 100) if target_progress > 0 else 0
            task_data["last_update"] = time.strftime("%Y-%m-%d %H:%M:%S")
            
            # 添加进度历史（保留最近20条）
            progress_record = {
                "progress": current_progress,
                "timestamp": time.time()
            }
            task_data["progress_history"].append(progress_record)
            if len(task_data["progress_history"]) > 20:
                task_data["progress_history"] = task_data["progress_history"][-20:]
            
            # 保存数据
            self._save_data()
            
            self.logger.info(f"更新任务 {task_name} 进度: {current_progress}/{target_progress} - By @ConceptualGod")
            
        except Exception as e:
            self.logger.error(f"更新任务完成进度失败: {str(e)} - By @ConceptualGod")
    
    def get_account_status(self, username: str) -> Dict[str, Any]:
        """
        获取账号状态
        
        Args:
            username: 账号用户名
            
        Returns:
            Dict: 账号状态信息
            
        开发者: @ConceptualGod
        """
        return self.account_status.get(username, {
            "victory_count": 0,
            "defeat_count": 0,
            "escape_count": 0,
            "total_games": 0,
            "total_duration": 0,
            "last_result": "未开始",
            "last_update": "",
            "game_history": []
        })
    
    def get_all_accounts_status(self) -> Dict[str, Dict]:
        """
        获取所有账号状态
        
        Returns:
            Dict: 所有账号状态
            
        开发者: @ConceptualGod
        """
        return self.account_status.copy()
    
    def get_task_completion_stats(self) -> Dict[str, Dict]:
        """
        获取任务完成统计
        
        Returns:
            Dict: 任务完成统计
            
        开发者: @ConceptualGod
        """
        return self.task_completion_stats.copy()
    
    def get_account_summary(self, username: str) -> str:
        """
        获取账号状态摘要字符串
        
        Args:
            username: 账号用户名
            
        Returns:
            str: 状态摘要
            
        开发者: @ConceptualGod
        """
        try:
            status = self.get_account_status(username)
            
            if status["total_games"] == 0:
                return f"{username}: 未开始"
            
            victory = status["victory_count"]
            defeat = status["defeat_count"]
            escape = status["escape_count"]
            last_result = status["last_result"]
            
            # 转换结果显示
            result_map = {
                "victory": "胜利",
                "defeat": "失败",
                "escape": "逃跑",
                "timeout": "逃跑"
            }
            last_result_cn = result_map.get(last_result, last_result)
            
            return f"{username}: 胜{victory}/败{defeat}/逃{escape} (最近:{last_result_cn})"
            
        except Exception as e:
            self.logger.error(f"获取账号摘要失败: {str(e)} - By @ConceptualGod")
            return f"{username}: 状态异常"
    
    def get_task_summary(self, task_name: str) -> str:
        """
        获取任务完成摘要字符串
        
        Args:
            task_name: 任务名称
            
        Returns:
            str: 任务摘要
            
        开发者: @ConceptualGod
        """
        try:
            if task_name not in self.task_completion_stats:
                return f"{task_name}: 未开始"
            
            task_data = self.task_completion_stats[task_name]
            current = task_data["current_progress"]
            target = task_data["target_progress"]
            rate = task_data["completion_rate"]
            
            return f"{task_name}: {current}/{target} ({rate:.1f}%)"
            
        except Exception as e:
            self.logger.error(f"获取任务摘要失败: {str(e)} - By @ConceptualGod")
            return f"{task_name}: 状态异常"
    
    def clear_account_data(self, username: str):
        """
        清除指定账号数据
        
        Args:
            username: 账号用户名
            
        开发者: @ConceptualGod
        """
        try:
            if username in self.account_status:
                del self.account_status[username]
                self._save_data()
                self.logger.info(f"清除账号 {username} 数据 - By @ConceptualGod")
                
        except Exception as e:
            self.logger.error(f"清除账号数据失败: {str(e)} - By @ConceptualGod")
    
    def clear_all_data(self):
        """
        清除所有数据
        
        开发者: @ConceptualGod
        """
        try:
            self.account_status = {}
            self.task_completion_stats = {}
            self._save_data()
            self.logger.info("清除所有数据 - By @ConceptualGod")
            
        except Exception as e:
            self.logger.error(f"清除所有数据失败: {str(e)} - By @ConceptualGod")
