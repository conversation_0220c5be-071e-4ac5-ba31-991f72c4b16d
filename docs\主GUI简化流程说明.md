# 起凡自动化脚本主GUI简化流程说明

**开发者:** @ConceptualGod  
**版本:** v2.0 Final  
**更新时间:** 2025-08-05  
**项目:** 起凡游戏自动化脚本

## 📋 主GUI界面概述

起凡自动化脚本主GUI已简化为专注于多号轮登的集成化控制界面。移除了不必要的单号登录、间隔设置和重试设置，专注于高效的多账号轮换操作。

## 🏗️ 简化后界面结构

### 界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 起凡自动化脚本 - By @ConceptualGod                          │
├─────────────────────────────────────────────────────────────┤
│ 菜单栏: 文件 | 工具 | 帮助                                  │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│ │   账号管理      │ │  多号轮登控制   │ │   状态日志      │ │
│ │                 │ │                 │ │                 │ │
│ │ - 导入账号      │ │ - 账号信息      │ │ - 实时日志      │ │
│ │ - 账号列表      │ │ - 开始轮登      │ │ - 账号状态      │ │
│ │ - 编辑删除      │ │ - 集成功能      │ │ - 任务进度      │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 状态栏: 就绪 | By @ConceptualGod | v2.0                     │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 简化后功能模块

### 1. 账号管理模块 ✅
**位置:** 主界面左侧  
**功能:** 管理游戏账号数据

#### 界面组件
- **导入账号按钮**: 支持CSV/Excel文件导入
- **账号列表**: 显示所有已导入的账号
- **编辑按钮**: 修改选中账号信息
- **删除按钮**: 删除选中账号
- **清空按钮**: 清空所有账号数据

### 2. 多号轮登控制模块 ✅
**位置:** 主界面中央  
**功能:** 专注于多账号轮换登录

#### 界面组件
- **账号信息显示**: 显示已导入账号数量
- **刷新账号按钮**: 刷新账号信息显示
- **开始多号轮登按钮**: 开始多账号轮换登录
- **停止按钮**: 停止当前操作
- **集成功能选项**: 任务识别、进度监控、游戏启动

#### 简化特点
- ❌ **移除单号登录**: 专注于多号轮登，不再支持单号登录
- ❌ **移除间隔设置**: 轮登不需要间隔，连续执行更高效
- ❌ **移除重试设置**: 简化操作，失败直接跳过下一个账号

### 3. 状态日志模块 ✅
**位置:** 主界面右侧  
**功能:** 显示实时操作日志和状态信息

#### 显示内容
- **实时操作日志**: 详细的步骤执行情况
- **账号状态统计**: 胜利/失败/逃跑状态显示在日志中
- **任务完成进度**: 战功任务完成数显示在日志中
- **错误信息**: 异常情况和错误处理

## 🔄 简化后操作流程

### 唯一操作流程：多号轮登
1. **导入账号**: 
   - 点击"导入账号"
   - 选择包含账号信息的CSV/Excel文件
   - 确认导入成功

2. **选择功能**: 勾选"任务识别"、"进度监控"、"游戏启动"

3. **开始轮登**: 点击"开始多号轮登"

4. **全程监控**: 
   - 系统按顺序处理每个账号
   - 实时显示当前处理状态
   - 自动循环直到手动停止

5. **查看状态**: 在状态日志中查看：
   - 账号状态: "用户名: 胜2/败1/逃0 (最近:胜利)"
   - 任务进度: "功高绩伟: 25/30 (83.3%)"

## 📊 集成功能详解

### 任务识别功能 ✅
**作用:** 识别当前战功任务并推荐英雄  
**流程:**
1. OCR扫描战功界面
2. 智能匹配任务类型
3. 推荐适合的英雄
4. 设置给游戏启动模块

### 进度监控功能 ✅
**作用:** 监控战功任务完成进度  
**流程:**
1. 扫描11个监控点
2. 检测任务完成状态
3. 自动领取已完成奖励
4. 更新任务完成统计

### 游戏启动功能 ✅
**作用:** 自动启动游戏并执行游戏内操作  
**流程:**
1. 智能窗口检测
2. 选择游戏房间
3. 选择推荐英雄
4. 执行魂玉搭配
5. 开始游戏内智能操作

## 🎮 游戏内操作系统

### 三模式智能操作 ✅
1. **发育模式**（前20分钟）
   - 安全发育，跟随队友
   - 敌人≥3个时后退

2. **跟随模式**（20分钟后）
   - 跟随MVP最高队友
   - 可进入防御塔范围

3. **战斗模式**（遇敌触发）
   - 攻击优先级：英雄>小兵>野怪
   - 600码范围技能释放

4. **撤退模式**（危险触发）
   - 紧急撤退和生存保障
   - 自动回城和复活处理

### 英雄专属技能 ✅
- **华佗**: W治疗血量<80%队友，D攻击敌人
- **刘备**: C和E攻击600码内敌人
- **诸葛瑾**: E和W攻击600码内敌人
- **陆逊**: E攻击600码内敌人
- **孙权**: E攻击600码内敌人
- **曹操**: C攻击600码内敌人

### 生存保障系统 ✅
- **血量<80%**: 自动使用玄铁盾（快捷键2）
- **血量<40%或蓝量<10%**: 紧急撤退使用奔雷靴（快捷键1）
- **自动回城**: 撤离到安全距离后按Y回城
- **复活处理**: 血量满后自动重新出门

## 🔚 游戏结束后流程

### 完整结束流程 ✅
1. **游戏结束检测**: OCR识别胜利/失败界面
2. **状态记录**: 更新账号胜利/失败/逃跑统计
3. **战功监控**: 检查战功任务完成情况
4. **任务判断**:
   - **有完成任务**: 自动领取奖励，继续当前账号
   - **无对应任务**: 执行换号流程
5. **任务大厅奖励**: 换号前领取任务大厅奖励（coordinates_1.json）
6. **账号切换**: 执行exit.json切换下一个账号

### 状态显示更新 ✅
- **账号状态**: 实时显示每个账号的胜/败/逃统计
- **任务进度**: 显示战功任务完成数值（如"功高绩伟"30/30）
- **实时更新**: 所有状态信息都在日志中实时显示

## 📝 日志显示系统

### 日志内容 ✅
- **操作进度**: 详细的步骤执行情况
- **状态更新**: 账号状态和任务进度变化
- **错误信息**: 异常情况和错误处理
- **时间戳**: 每条日志都包含精确时间

### 日志格式
```
[HH:MM:SS] 操作内容 - By @ConceptualGod
[HH:MM:SS] 账号状态更新: 用户名: 胜1/败0/逃0 (最近:胜利) - By @ConceptualGod
[HH:MM:SS] 战功任务进度: 功高绩伟: 25/30 (83.3%) - By @ConceptualGod
```

## 🎯 简化优势

### 操作简化
1. **一键启动**: 导入账号后一键启动多号轮登
2. **无需设置**: 移除复杂的间隔和重试设置
3. **专注轮登**: 专门为多账号轮换优化
4. **连续执行**: 无间隔连续处理，效率更高

### 界面简化
1. **减少选项**: 移除不必要的单号登录选项
2. **清晰布局**: 三大模块分工明确
3. **实时反馈**: 所有状态信息集中在日志显示
4. **操作直观**: 一个按钮开始所有操作

### 功能专注
1. **多号轮登**: 专门为批量账号处理设计
2. **智能操作**: 完整的游戏内智能操作系统
3. **状态监控**: 实时监控账号状态和任务进度
4. **自动换号**: 根据战功任务完成情况智能换号

## 🛠️ 使用建议

### 最佳实践
1. **首次使用**: 先导入少量账号测试多号轮登功能
2. **批量操作**: 确认流程正常后再导入大量账号
3. **监控日志**: 定期查看状态日志的实时信息
4. **问题排查**: 遇到问题时仔细查看日志的详细信息

### 注意事项
1. **游戏窗口**: 确保游戏已启动并可见
2. **坐标配置**: 确保所有JSON配置文件存在且正确
3. **网络稳定**: 保持网络连接稳定
4. **系统资源**: 确保系统有足够的内存和CPU资源

---

**简化完成:** 2025-08-05  
**开发者:** @ConceptualGod  
**状态:** 简化完成，专注多号轮登  
**特点:** 操作简化，界面清晰，功能专注，效率提升
