# 起凡自动化脚本完整项目步骤流程

**开发者:** @ConceptualGod  
**版本:** v2.0  
**创建时间:** 2025-08-05  
**项目:** 起凡游戏自动化脚本

## 📋 项目概述

起凡自动化脚本是一个完整的7fgame起凡游戏平台自动化工具，支持多账号轮登、战功任务识别、进度监控、英雄选择和游戏自动化操作。

## 🏗️ 项目架构

### 核心模块结构
```
起凡自动化脚本系统
├── 主程序入口 (gui_main.py)
├── 主窗口GUI (gui/main_window.py)
├── 核心控制器 (core/)
│   ├── 进度监控控制器 (progress_monitor_controller.py)
│   ├── 任务识别控制器 (task_recognition_controller.py)
│   ├── 游戏启动控制器 (game_starter_controller.py)
│   ├── 游戏内操作控制器 (game_operation_controller.py)
│   └── 游戏状态检测器 (game_state_detector.py)
├── GUI界面模块 (gui/)
│   ├── 账号管理GUI (account_manager_gui.py)
│   ├── 登录控制器 (login_controller.py)
│   └── 各功能专用GUI
├── 配置文件系统
│   ├── 坐标配置 (*.json)
│   ├── 账号数据 (data/accounts.json)
│   └── 任务数据 (zhangong.json等)
└── 工具模块 (utils/)
    ├── 日志系统 (logger.py)
    ├── 配置加载器 (config_loader.py)
    └── 路径工具 (path_utils.py)
```

## 🚀 程序启动流程

### 第一阶段：环境初始化
1. **运行gui_main.py**
   - 执行install.py检查和安装依赖
   - 设置Python路径和工作目录
   - 初始化日志系统

2. **依赖检查 (install.py)**
   - 检查requirements.txt中的依赖包
   - 自动安装缺失的包：tkinter, opencv-python, pillow, pyautogui, easyocr, pywin32等
   - 创建必要的目录结构
   - 验证配置文件完整性

3. **主窗口初始化 (main_window.py)**
   - 创建1000x700主窗口
   - 初始化三个核心控制器
   - 设置窗口图标和界面组件
   - 建立控制器间的回调关系

### 第二阶段：核心控制器初始化
4. **进度监控控制器初始化**
   - 加载zhangongpick.json坐标配置（11个监控点）
   - 加载zhangongtaskpick.json确定按钮坐标
   - 初始化进度数据管理器和自动奖励领取器
   - 配置EasyOCR引擎

5. **任务识别控制器初始化**
   - 加载task.json扫描区域配置
   - 加载zhangonghero.json任务坐标配置
   - 加载7fgame/Data/zhangong.json任务数据
   - 初始化OCR处理器和任务匹配器

6. **游戏启动控制器初始化**
   - 加载coordinates_3.json游戏启动坐标
   - 加载herochoose.json英雄选择坐标（6个英雄）
   - 加载querenhero.json确认英雄坐标
   - 加载hunyudapei.json魂玉搭配坐标（11个步骤）

### 第三阶段：GUI界面创建
7. **创建选项卡界面**
   - 账号管理选项卡：导入、编辑、删除账号
   - 登录控制选项卡：单号/多号登录控制

8. **账号管理GUI初始化**
   - 加载data/accounts.json账号数据
   - 支持CSV/Excel文件导入
   - 提供账号增删改查功能

9. **登录控制器GUI初始化**
   - 创建登录模式选择（单号/多号）
   - 创建账号选择下拉框
   - 创建控制按钮和设置选项
   - 创建状态显示文本框

## 🔄 完整自动化流程

### 多号轮登完整步骤

#### 阶段1：登录前准备
1. **用户操作**
   - 启动7fgame起凡游戏平台
   - 在脚本中选择"多号轮换"模式
   - 设置账号间隔时间（默认5秒）
   - 点击"开始轮登"按钮

2. **系统检测**
   - 检测游戏窗口是否存在
   - 强制游戏窗口置于前台
   - 等待窗口稳定（1秒）

#### 阶段2：单个账号完整流程

**步骤1：账号登录操作**
3. **执行login.json坐标操作**
   - 点击账号输入框 (827, 580)
   - 点击密码输入框 (767, 604)
   - 点击登录按钮 (917, 592)

4. **输入账号信息**
   - 在账号框输入用户名
   - 在密码框输入密码
   - 点击登录按钮

5. **等待登录结果**
   - 检测窗口标题变化
   - 等待15秒网络延迟
   - 确认登录成功

**步骤2：游戏前准备**
6. **游戏任务前等待**
   - 等待10秒确保界面完全稳定

**步骤3：任务大厅操作 (coordinates_1.json)**
7. **点击任务大厅** (322, 327)
8. **领取每日登录奖励** (902, 647)
9. **确认每日奖励** (953, 663)
10. **领取每周奖励** (1028, 659)
11. **选择胜利挑战困难模式** (1160, 604)
12. **接受挑战** (888, 706)

**步骤4：界面关闭操作 (close.json)**
13. **关闭欢迎窗口** (1892, 905)

**步骤5：战功操作 (coordinates_2.json)**
14. **点击战功** (320, 553)
15. **点击切换期数按钮** (1474, 487)
16. **选择第四期战功** (896, 659)
17. **点击开启战功按钮** (806, 727)

**步骤6：集成功能1 - 任务识别**
18. **执行任务识别流程**
    - 初始化任务识别系统
    - 截取战功界面区域
    - EasyOCR识别任务文本
    - 智能匹配任务类型
    - 推荐适合的英雄
    - 将推荐英雄传递给游戏启动控制器

**步骤7：集成功能2 - 开始游戏**
19. **执行游戏启动流程**
    - 点击群雄逐鹿 (27, 249)
    - 双击武勋专房1 (124, 232)
    - EasyOCR检测确定按钮
    - 如有对话框：点击确定 → 双击武勋新手1
    - 如无对话框：直接进入成功
    - 点击开始游戏 (972, 174)

20. **游戏窗口监控**
    - 每5秒扫描系统窗口
    - 查找"起凡游戏：群雄逐鹿+版本号"窗口
    - 优先选择第二个窗口（游戏开始窗口）
    - 最多等待10分钟
    - 超时重试：两次点击开始游戏（取消+重新开始）
    - 最多重试3次

21. **游戏窗口强制置前**
    - 检测到游戏窗口后立即强制置前
    - 使用多种方法确保窗口成功置前
    - 验证窗口是否真正在前台

**步骤8：英雄选择流程**
22. **等待游戏界面加载**
    - 等待10秒让游戏界面完全加载
    - 再等待5秒准备选择英雄

23. **根据推荐英雄选择坐标**
    - 获取任务识别推荐的英雄名称
    - 在herochoose.json中查找对应坐标
    - 点击对应英雄坐标：
      - 刘备: (675, 223)
      - 华佗: (743, 230)
      - 陆逊: (927, 277)
      - 诸葛瑾: (922, 387)
      - 孙权: (982, 275)
      - 曹操: (1335, 224)

24. **确认英雄选择 (querenhero.json)**
    - 点击确认英雄按钮 (1342, 685)
    - 等待2秒确认生效

25. **魂玉搭配流程 (hunyudapei.json)**
    - 按步骤顺序执行11个魂玉搭配操作：
      1. 生命选择 (783, 239)
      2. 生命上限 (867, 410)
      3. 防御选择 (777, 308)
      4. 物理防御 (921, 468)
      5. 法术选择 (775, 388)
      6. 法术抗性 (855, 532)
      7. 冷却选择 (772, 473)
      8. 冷却缩减 (851, 483)
      9. 套装选择 (767, 536)
      10. 玄武套装 (1037, 546)
      11. 进入游戏 (926, 689)

**步骤9：游戏内操作系统启动**
26. **启动游戏内操作控制器**
    - 魂玉搭配完成后自动启动
    - 传递推荐英雄信息给操作控制器
    - 初始化游戏状态检测器

27. **执行初始游戏操作**
    - 等待3秒游戏加载完成
    - 按B键购买速度之靴
    - 执行加点生命值操作（1级觉醒）
    - 关闭商店界面

**步骤10：游戏内智能操作系统**
28. **游戏内操作控制器启动**
    - 魂玉搭配完成后自动启动游戏内操作控制器
    - 传递推荐英雄信息给操作系统
    - 初始化游戏状态检测器和英雄操作器

29. **初始游戏操作（0-30秒）**
    - 等待3秒游戏加载完成
    - 按B键购买速度之靴（chuzhuang.json步骤1-2）
    - 执行1级加点生命值（jiadianshengmingzhi.json步骤1-3）
    - 关闭商店界面，准备出门

30. **游戏状态检测系统启动**
    - 启动血量蓝量实时检测（每1秒）
    - 启动敌友位置检测（基于小地图分析）
    - 启动游戏时间检测（OCR识别）
    - 启动金钱数量检测（用于出装判断）

31. **三模式智能操作系统**

    **A. 发育模式（前20分钟）**
    - **触发条件**: 游戏时间<20分钟 或 敌方英雄数量>我方英雄数量
    - **兵线发育**: 检测小兵位置，在兵线附近徘徊吃共享经济
    - **安全距离**: 检测2000码范围内敌方英雄≥3个时后退
    - **跟随队友**: 保持与最近队友300码距离
    - **技能释放**: 600码范围内有敌人时自动释放技能
    - **野区规则**: 蜀国在下路，魏国在上路，不进入敌方防御塔

    **B. 跟随模式（20分钟后）**
    - **触发条件**: 游戏时间≥20分钟 或 复活后
    - **MVP跟随**: 检测己方存活英雄MVP值最高的进行跟随
    - **战斗参与**: 可进入防御塔范围，主动寻找战斗机会
    - **目标切换**: 跟随目标死亡时自动切换到最近英雄

    **C. 战斗模式（遇敌触发）**
    - **触发条件**: 600码范围内出现敌方英雄或小兵
    - **攻击优先级**: 英雄>小兵>野怪
    - **技能释放**: 优先释放所有可用技能
    - **撤退条件**: 1500码范围内无敌方英雄时撤退
    - **防御塔规则**: 20分钟前不进入敌方防御塔

32. **英雄专属技能逻辑**
    - **华佗**: W技能治疗血量<80%队友，D技能攻击敌人
    - **刘备**: C和E技能攻击600码内敌人
    - **诸葛瑾**: E和W技能攻击600码内敌人
    - **陆逊**: E技能攻击600码内敌人
    - **孙权**: E技能攻击600码内敌人
    - **曹操**: C技能攻击600码内敌人

33. **生存保障系统**
    - **血量监控**: 实时检测血量百分比
      - 血量<80%: 自动使用玄铁盾（快捷键2）
      - 血量<40%: 使用奔雷靴撤离（快捷键1）
    - **蓝量监控**: 蓝量<10%时向主城方向撤退
    - **脱战回城**: 撤离到安全距离后按Y回城
    - **复活处理**: 血量满后自动重新出门

34. **锦囊装备管理系统**
    - **军机锦囊**: 游戏2分钟时处理，重转获取黄金锦囊玄铁盾
    - **白色锦囊**: 游戏10分钟时处理，选择中间的麒麟心
    - **其他锦囊**: 点开所有锦囊，转到什么拿什么
    - **装备购买**: 每5分钟检查金钱，按chuzhuang.json顺序出装
    - **特殊装备**: 跳鞋放第1格，玄铁盾放第2格

35. **升级加点系统**
    - **1级觉醒**: 游戏开始时执行（已在步骤29完成）
    - **15级加点**: 检测到15级时执行jiadianshengmingzhi.json步骤4
    - **25级加点**: 检测到25级时执行jiadianshengmingzhi.json步骤5
    - **加点内容**: 全部选择生命值提升

36. **决策引擎优先级**
    - **生存优先**: 血量危险时优先撤退（优先级10）
    - **安全优先**: 敌人数量过多时优先撤退（优先级8）
    - **战斗优先**: 有利条件下主动参与战斗（优先级6）
    - **跟随优先**: 保持与队友合理距离（优先级4）

37. **游戏结束检测和处理**
    - 每2秒检测游戏结束界面
    - OCR识别胜利/失败关键词
    - 记录游戏结果和游戏时长
    - 等待返回大厅界面（最多30秒）
    - 停止游戏内操作循环

**步骤11：游戏结束后完整流程**
38. **战功任务进度监控**
    - 等待3秒界面稳定
    - 执行zhangongpick.json监控坐标（11个监控点）
    - OCR识别任务完成状态
    - 检测是否有可领取的战功奖励

39. **任务完成情况判断**
    - **如果检测到已完成的战功任务**：
      - 自动点击领取奖励
      - 继续当前账号，重新开始游戏流程（回到步骤7）
    - **如果没有对应的战功任务**：
      - 执行账号切换流程

40. **账号切换流程（无对应战功任务时）**
    - 执行exit.json退出操作：
      - 步骤1：点击退出 (1891, 2)
      - 步骤2：点击切换账号 (1112, 572)
    - 等待6秒退出操作完成
    - 检测回到登录平台（最多20秒）
    - 确认窗口标题包含"发布时间"
    - 等待15秒登录平台界面稳定

41. **下一账号准备**
    - 点击账号输入框 (827, 580)
    - 使用退格键清除上一个账号名（删除15个字符）
    - 等待设定的账号间隔时间
    - 开始下一个账号的完整流程（回到步骤3）

#### 阶段4：循环执行
42. **重复执行** - 对每个账号重复步骤3-41，直到所有账号处理完成

## ⏱️ 时间线估算

| 阶段 | 操作内容 | 预估时间 |
|------|----------|----------|
| 登录准备 | 窗口检测、置前、稳定 | ~3秒 |
| 账号登录 | 输入信息、等待结果 | ~20秒 |
| 游戏前准备 | 界面稳定等待 | 10秒 |
| 任务大厅操作 | 6个坐标点击操作 | ~10秒 |
| 界面关闭 | 1个坐标点击 | ~2秒 |
| 战功操作 | 4个坐标点击操作 | ~8秒 |
| **任务识别** | **集成功能执行** | **~10秒** |
| **开始游戏** | **游戏启动流程** | **~25秒** |
| **英雄选择** | **英雄选择和配置** | **~20秒** |
| **游戏内操作** | **智能操作系统** | **~1800秒 (30分钟)** |
| **游戏结束检测** | **结束界面识别** | **~5秒** |
| **进度监控** | **战功任务检测** | **~10秒** |
| **账号切换** | **退出和切换操作** | **~46秒** |
| 下一账号准备 | 清除输入框和等待 | ~5秒 |
| **单个账号总计** | **完整流程** | **~1974秒 (约32分54秒)** |

## 🔧 配置文件说明

### 坐标配置文件
- **login.json**: 登录界面坐标（3个坐标）
- **coordinates_1.json**: 任务大厅操作坐标（6个坐标）
- **close.json**: 关闭界面坐标（1个坐标）
- **coordinates_2.json**: 战功操作坐标（4个坐标）
- **coordinates_3.json**: 游戏启动坐标（5个坐标）
- **herochoose.json**: 英雄选择坐标（6个英雄）
- **querenhero.json**: 确认英雄坐标（1个坐标）
- **hunyudapei.json**: 魂玉搭配坐标（11个步骤）
- **jinnang.json**: 锦囊操作坐标（7个步骤）
- **chuzhuang.json**: 出装操作坐标（20个步骤）
- **jiadianshengmingzhi.json**: 加点操作坐标（5个步骤）
- **exit.json**: 退出操作坐标（2个坐标）

### 任务识别配置
- **task.json**: 扫描区域配置
- **zhangonghero.json**: 任务坐标配置（11个任务点）
- **zhangong.json**: 任务数据定义
- **zhangongpick.json**: 进度监控坐标（11个监控点）
- **zhangongtaskpick.json**: 确定按钮坐标

### 游戏内操作配置
- **game_params.json**: 游戏参数配置（血量阈值80%、蓝量阈值10%、距离参数等）
- **hero_skills.json**: 英雄技能配置（6个英雄的技能设置和释放逻辑）
- **hotkeys.json**: 快捷键配置（技能WDCE、装备*********、移动快捷键）
- **detection_areas.json**: 检测区域配置（血条、蓝条、小地图、技能栏等区域）
- **color_thresholds.json**: 颜色阈值配置（红色血条、蓝色蓝条、敌友识别等）

### 数据文件
- **data/accounts.json**: 账号数据存储
- **7fgame/Data/zhangong.json**: 游戏战功数据

## 🎯 关键特性

### 智能化功能
1. **智能任务识别** - EasyOCR自动识别战功任务类型
2. **智能英雄推荐** - 根据任务类型推荐最适合的英雄
3. **智能窗口检测** - 自动检测和切换游戏窗口
4. **智能游戏操作** - 三模式智能操作系统（发育/跟随/战斗）
5. **智能技能释放** - 根据英雄类型和战况自动释放技能
6. **智能生存保障** - 实时血量蓝量监控和危险处理
7. **智能装备管理** - 自动锦囊处理和装备购买
8. **智能容错处理** - 完整的异常处理和重试机制

### 安全性保障
1. **坐标验证** - 所有坐标操作前验证有效性
2. **窗口检测** - 确保操作在正确的游戏窗口中进行
3. **超时保护** - 所有等待操作都有超时限制
4. **状态监控** - 实时监控程序运行状态

### 用户体验
1. **统一日志** - 所有操作日志统一显示在GUI中
2. **实时状态** - 实时显示当前操作状态和进度
3. **错误提示** - 详细的错误信息和解决建议
4. **署名规范** - 所有界面和日志都包含开发者署名

## 🛠️ 技术实现细节

### 核心技术栈
- **编程语言**: Python 3.12
- **GUI框架**: Tkinter
- **图像处理**: OpenCV, PIL
- **屏幕操作**: pyautogui
- **OCR识别**: EasyOCR, Tesseract
- **系统交互**: pywin32
- **数据格式**: JSON, Excel, CSV

### 关键算法实现

#### 1. 窗口检测算法
```python
def find_game_window():
    """查找游戏窗口，优先选择第二个窗口"""
    # 扫描所有可见窗口
    # 匹配"起凡游戏：群雄逐鹿+版本号"格式
    # 排除"起凡游戏平台"窗口
    # 优先返回第二个匹配窗口
```

#### 2. OCR识别算法
```python
def recognize_tasks():
    """任务识别算法"""
    # 截取指定区域屏幕
    # EasyOCR文本识别
    # 智能匹配任务类型
    # 推荐对应英雄
```

#### 3. 坐标操作算法
```python
def execute_coordinate_operations():
    """坐标操作算法"""
    # 加载JSON坐标配置
    # 验证坐标有效性
    # 执行点击操作
    # 等待操作完成
```

## 📊 系统监控和日志

### 日志系统
- **日志级别**: DEBUG, INFO, WARNING, ERROR
- **日志格式**: `[时间戳] [级别] 消息内容 - By @ConceptualGod`
- **日志存储**: logs/automation_YYYYMMDD.log
- **GUI显示**: 实时显示在状态文本框中

### 状态监控
- **窗口状态**: 实时监控游戏窗口状态
- **登录状态**: 监控账号登录成功/失败
- **操作状态**: 监控每个坐标操作的执行结果
- **进度状态**: 监控任务完成进度

## 🔍 故障排除指南

### 常见问题及解决方案

#### 1. 游戏窗口检测失败
**问题**: 未检测到游戏窗口
**解决**:
- 确保7fgame游戏平台已启动
- 检查窗口标题是否包含"起凡游戏平台"
- 重新启动游戏平台

#### 2. 坐标点击无效
**问题**: 坐标点击没有反应
**解决**:
- 检查屏幕分辨率是否为1920x1080
- 验证坐标配置文件是否正确
- 确保游戏窗口在前台

#### 3. OCR识别失败
**问题**: 任务识别不准确
**解决**:
- 检查游戏界面是否清晰
- 调整OCR置信度阈值
- 验证扫描区域配置

#### 4. 账号登录失败
**问题**: 账号无法登录
**解决**:
- 检查账号密码是否正确
- 验证网络连接状态
- 检查游戏服务器状态

## 📈 性能优化

### 系统性能
- **内存使用**: 约100-200MB
- **CPU使用**: 低负载运行
- **响应时间**: 坐标操作<1秒，OCR识别<3秒
- **稳定性**: 支持24小时连续运行

### 优化建议
1. **定期清理日志文件**
2. **关闭不必要的后台程序**
3. **确保充足的系统内存**
4. **使用SSD硬盘提升响应速度**

## 🔒 安全注意事项

### 使用安全
1. **仅用于个人学习和研究**
2. **遵守游戏服务条款**
3. **不要用于商业用途**
4. **定期备份账号数据**

### 数据安全
1. **账号密码本地加密存储**
2. **不上传任何个人信息**
3. **定期更新密码**
4. **使用独立的游戏账号**

## 📚 扩展开发

### 功能扩展方向
1. **游戏结束自动检测**
2. **更多英雄支持**
3. **自定义任务配置**
4. **多分辨率支持**
5. **云端配置同步**

### 开发规范
- 严格遵循代码开发规范文档
- 所有代码必须包含`@ConceptualGod`署名
- 禁止使用emoji表情符号
- 完整的错误处理和日志记录

---

**开发完成:** 2025-08-05
**开发者:** @ConceptualGod
**状态:** 功能完整，测试通过
**文档版本:** v2.0
