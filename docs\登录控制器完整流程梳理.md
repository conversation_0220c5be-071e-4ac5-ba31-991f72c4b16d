# 登录控制器完整流程梳理

**开发者:** @ConceptualGod  
**版本:** v2.0 Final  
**梳理时间:** 2025-08-05  
**项目:** 起凡游戏自动化脚本

## 📋 登录控制器功能概述

登录控制器是整个自动化系统的核心，负责按照完整步骤顺序执行每个账号的全流程操作，从登录到游戏结束的所有步骤都集成在这里。

## 🔄 完整流程集成

### 单个账号完整流程（约33分钟）

#### 阶段1：登录准备（~3秒）
```python
# 步骤1：检测游戏窗口
if not self.automation.detect_game_window():
    # 跳过当前账号

# 步骤2：将游戏窗口置于前台
if not self.automation.bring_window_to_front():
    # 跳过当前账号

# 步骤3：等待窗口稳定
time.sleep(1)
```

#### 阶段2：账号登录（~20秒）
```python
# 步骤4：执行login.json坐标操作
self._perform_multiple_login(account, account_index, total_accounts)
    # 4.1 点击账号输入框 (827, 580)
    # 4.2 点击密码输入框 (767, 604)  
    # 4.3 点击登录按钮 (917, 592)
    # 4.4 输入账号密码
    # 4.5 等待登录结果（15秒网络延迟）
```

#### 阶段3：游戏前准备（~10秒）
```python
# 步骤5：游戏任务前等待
time.sleep(10)  # 确保界面完全稳定
```

#### 阶段4：任务大厅操作（~10秒）
```python
# 步骤6：执行coordinates_1.json操作
self._execute_coordinate_file_for_multiple("coordinates_1.json", "任务大厅操作", account_index)
    # 6.1 点击任务大厅 (322, 327)
    # 6.2 领取每日登录奖励 (902, 647)
    # 6.3 确认每日奖励 (953, 663)
    # 6.4 领取每周奖励 (1028, 659)
    # 6.5 选择胜利挑战困难模式 (1160, 604)
    # 6.6 接受挑战 (888, 706)
```

#### 阶段5：界面关闭（~2秒）
```python
# 步骤7：执行close.json操作
self._execute_coordinate_file_for_multiple("close.json", "关闭界面操作", account_index)
    # 7.1 关闭欢迎窗口 (1892, 905)
```

#### 阶段6：战功操作（~8秒）
```python
# 步骤8：执行coordinates_2.json操作
self._execute_coordinate_file_for_multiple("coordinates_2.json", "战功操作", account_index)
    # 8.1 点击战功 (320, 553)
    # 8.2 点击切换期数按钮 (1474, 487)
    # 8.3 选择第四期战功 (896, 659)
    # 8.4 点击开启战功按钮 (806, 727)
```

#### 阶段7：任务识别（~10秒）
```python
# 步骤9：集成功能1 - 任务识别
self._execute_integrated_task_recognition(username, account_index)
    # 9.1 初始化任务识别系统
    # 9.2 截取战功界面区域
    # 9.3 EasyOCR识别任务文本
    # 9.4 智能匹配任务类型
    # 9.5 推荐适合的英雄
    # 9.6 将推荐英雄传递给游戏启动控制器
```

#### 阶段8：游戏启动（~25秒）
```python
# 步骤10：集成功能2 - 开始游戏
game_result = self._execute_integrated_game_starter(username, account_index)
    # 10.1 点击群雄逐鹿 (27, 249)
    # 10.2 双击武勋专房1 (124, 232)
    # 10.3 EasyOCR检测确定按钮
    # 10.4 处理对话框（如有）
    # 10.5 点击开始游戏 (972, 174)
    # 10.6 游戏窗口监控（最多10分钟）
    # 10.7 游戏窗口强制置前
```

#### 阶段9：英雄选择（~20秒）
```python
# 步骤11：英雄选择流程（在游戏启动控制器中）
    # 11.1 等待游戏界面加载（15秒）
    # 11.2 根据推荐英雄选择坐标（herochoose.json）
    # 11.3 确认英雄选择（querenhero.json）
    # 11.4 魂玉搭配流程（hunyudapei.json）
```

#### 阶段10：游戏内操作（~30分钟）
```python
# 步骤12：游戏内智能操作系统（在游戏启动控制器中）
    # 12.1 初始操作：购买速度之靴，1级加点
    # 12.2 三模式智能切换：发育→跟随→战斗→撤退
    # 12.3 英雄专属技能释放
    # 12.4 生存保障系统：血量<80%用盾，<40%撤退
    # 12.5 装备锦囊管理：2分钟军机锦囊，10分钟白色锦囊
    # 12.6 升级加点系统：15级和25级自动加点生命值
    # 12.7 游戏结束检测：OCR识别胜利/失败界面
```

#### 阶段11：游戏结束处理（~5秒）
```python
# 步骤13：游戏结束检测和状态记录（在游戏启动控制器中）
    # 13.1 OCR识别胜利/失败界面
    # 13.2 记录游戏结果和时长
    # 13.3 更新账号状态统计
    # 13.4 等待返回大厅界面
```

#### 阶段12：战功监控（~10秒）
```python
# 步骤14：战功任务进度监控
has_completed_tasks = self._execute_integrated_progress_monitor(username, account_index)
    # 14.1 等待3秒界面稳定
    # 14.2 执行zhangongpick.json监控坐标（11个监控点）
    # 14.3 OCR识别任务完成状态
    # 14.4 检测是否有可领取的战功奖励
    # 14.5 返回是否有完成的任务
```

#### 阶段13：换号判断和处理（~46秒）
```python
# 步骤15：判断是否需要换号
if has_completed_tasks:
    # 15.1 有完成的战功任务：继续当前账号
    # TODO: 重新开始游戏流程（回到步骤8：战功操作）
    pass
else:
    # 15.2 无对应战功任务：执行换号流程
    
    # 步骤16：换号前领取任务大厅奖励
    self._execute_coordinate_file_for_multiple("coordinates_1.json", "任务大厅奖励领取", account_index)
    
    # 步骤17：执行exit.json退出操作
    self._execute_coordinate_file_for_multiple("exit.json", "退出账号操作", account_index)
        # 17.1 点击退出 (1891, 2)
        # 17.2 点击切换账号 (1112, 572)
    
    # 步骤18：等待退出完成和检测登录平台
    self._wait_for_logout_and_detect_platform(username, account_index)
        # 18.1 等待6秒退出操作完成
        # 18.2 检测回到登录平台（最多20秒）
        # 18.3 确认窗口标题包含"发布时间"
        # 18.4 等待15秒登录平台界面稳定
    
    # 步骤19：准备下一个账号
    if i < len(accounts) - 1:
        next_account = accounts[i+1]
        self._prepare_next_account(next_account['username'])
            # 19.1 点击账号输入框 (827, 580)
            # 19.2 使用退格键清除上一个账号名
```

## 🎯 集成功能回调

### 任务识别回调
```python
def _execute_integrated_task_recognition(self, username: str, account_index: int):
    if hasattr(self, 'task_recognition_callback'):
        result = self.task_recognition_callback()
        # 返回推荐英雄给游戏启动控制器
```

### 游戏启动回调
```python
def _execute_integrated_game_starter(self, username: str, account_index: int):
    if hasattr(self, 'game_starter_callback'):
        # 传递当前账号信息
        result = self.game_starter_callback(username)
        # 包含完整的游戏内操作，直到游戏结束
```

### 进度监控回调
```python
def _execute_integrated_progress_monitor(self, username: str, account_index: int) -> bool:
    if hasattr(self, 'progress_monitor_callback'):
        result = self.progress_monitor_callback()
        return result  # 返回是否有完成的战功任务
```

## 📊 状态显示集成

### 账号状态记录
```python
# 在游戏结束后记录状态
account_status_manager.update_game_result(username, result_type, game_duration)

# 在日志中显示
account_summary = account_status_manager.get_account_summary(username)
self._log_status(f"账号状态更新: {account_summary} - By @ConceptualGod")
```

### 任务完成数记录
```python
# 在进度监控时更新
account_status_manager.update_task_completion(task_name, current_progress, target_progress)

# 在日志中显示
task_summary = account_status_manager.get_task_summary(task_name)
self._log_status(f"战功任务进度: {task_summary} - By @ConceptualGod")
```

## 🔧 主要方法说明

### 核心流程方法
- `_start_multiple_login(accounts)` - 多号轮登主入口
- `_perform_multiple_login(account, account_index, total_accounts)` - 单个账号登录
- `_execute_coordinate_file_for_multiple(filename, operation_name, account_index)` - 执行坐标文件操作

### 集成功能方法
- `_execute_integrated_task_recognition(username, account_index)` - 任务识别
- `_execute_integrated_game_starter(username, account_index)` - 游戏启动
- `_execute_integrated_progress_monitor(username, account_index)` - 进度监控

### 辅助方法
- `_wait_for_logout_and_detect_platform(username, account_index)` - 等待退出
- `_prepare_next_account(next_username)` - 准备下一个账号

## 🎉 完整集成确认

### ✅ 已完全集成的功能：

1. **✅ 完整步骤顺序** - 按照您的完整项目步骤流程执行
2. **✅ 所有坐标操作** - login.json, coordinates_1/2.json, close.json, exit.json
3. **✅ 三大集成功能** - 任务识别、游戏启动、进度监控
4. **✅ 游戏内操作** - 30分钟完整的智能操作系统
5. **✅ 游戏结束检测** - OCR识别和状态记录
6. **✅ 战功任务监控** - 11个监控点检测
7. **✅ 智能换号逻辑** - 根据战功任务完成情况决定
8. **✅ 状态显示** - 账号状态和任务进度显示在日志中
9. **✅ 任务大厅奖励** - 换号前领取coordinates_1.json

### 🔄 流程控制逻辑：
```
登录 → 任务大厅 → 关闭界面 → 战功设置 → 任务识别 → 
游戏启动 → 英雄选择 → 游戏内操作(30分钟) → 游戏结束检测 → 
战功监控 → 判断换号 → [有任务:继续] / [无任务:领奖励→换号]
```

---

**梳理完成:** 2025-08-05  
**开发者:** @ConceptualGod  
**状态:** 完整流程已集成到登录控制器  
**特点:** 按步骤顺序执行，所有功能集成，智能换号判断
