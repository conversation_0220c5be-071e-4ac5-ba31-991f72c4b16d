#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏状态检测器
负责检测游戏内的各种状态，包括血量、蓝量、位置、敌人等

开发者: @ConceptualGod
创建时间: 2025-08-05
"""

import cv2
import numpy as np
import pyautogui
import easyocr
import logging
import time
from PIL import ImageGrab
from typing import Dict, List, Tuple, Optional

class GameStateDetector:
    """
    游戏状态检测器
    
    功能包括：
    - 血量蓝量检测
    - 英雄位置检测
    - 敌人检测
    - 游戏时间检测
    - 锦囊状态检测
    
    开发者: @ConceptualGod
    """
    
    def __init__(self):
        """
        初始化游戏状态检测器
        
        开发者: @ConceptualGod
        """
        self.logger = logging.getLogger(__name__)
        
        # 初始化OCR引擎
        self.ocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        
        # 检测区域配置（需要根据实际游戏界面调整）
        self.detection_areas = {
            "blood_bar": (50, 50, 200, 80),      # 血条区域
            "mana_bar": (50, 80, 200, 110),      # 蓝条区域
            "game_time": (1800, 10, 1920, 40),   # 游戏时间区域
            "minimap": (1600, 800, 1920, 1080),  # 小地图区域
            "jinnang_area": (1500, 800, 1920, 1000),  # 锦囊区域
            "equipment_area": (1400, 900, 1920, 1080)  # 装备区域
        }
        
        # 颜色阈值配置
        self.color_thresholds = {
            "red_blood": ([0, 100, 100], [10, 255, 255]),    # 红色血条
            "blue_mana": ([100, 100, 100], [130, 255, 255]), # 蓝色蓝条
            "enemy_red": ([0, 100, 100], [10, 255, 255]),    # 敌方红色
            "ally_blue": ([100, 100, 100], [130, 255, 255])  # 友方蓝色
        }
        
        self.logger.info("游戏状态检测器初始化完成 - By @ConceptualGod")
    
    def capture_screen_area(self, area: Tuple[int, int, int, int]) -> np.ndarray:
        """
        截取屏幕指定区域
        
        Args:
            area: 区域坐标 (x1, y1, x2, y2)
            
        Returns:
            np.ndarray: 截取的图像
            
        开发者: @ConceptualGod
        """
        try:
            x1, y1, x2, y2 = area
            screenshot = ImageGrab.grab(bbox=(x1, y1, x2, y2))
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            self.logger.error(f"截取屏幕区域失败: {str(e)} - By @ConceptualGod")
            return None
    
    def detect_blood_percentage(self) -> Optional[float]:
        """
        检测血量百分比
        
        Returns:
            Optional[float]: 血量百分比 (0-100)
            
        开发者: @ConceptualGod
        """
        try:
            # 截取血条区域
            blood_area = self.capture_screen_area(self.detection_areas["blood_bar"])
            if blood_area is None:
                return None
            
            # 转换为HSV颜色空间
            hsv = cv2.cvtColor(blood_area, cv2.COLOR_BGR2HSV)
            
            # 检测红色血条
            lower_red, upper_red = self.color_thresholds["red_blood"]
            mask = cv2.inRange(hsv, np.array(lower_red), np.array(upper_red))
            
            # 计算红色像素比例
            total_pixels = mask.shape[0] * mask.shape[1]
            red_pixels = cv2.countNonZero(mask)
            
            if total_pixels > 0:
                blood_percentage = (red_pixels / total_pixels) * 100
                return min(100, max(0, blood_percentage))
            
            return None
            
        except Exception as e:
            self.logger.error(f"检测血量失败: {str(e)} - By @ConceptualGod")
            return None
    
    def detect_mana_percentage(self) -> Optional[float]:
        """
        检测蓝量百分比
        
        Returns:
            Optional[float]: 蓝量百分比 (0-100)
            
        开发者: @ConceptualGod
        """
        try:
            # 截取蓝条区域
            mana_area = self.capture_screen_area(self.detection_areas["mana_bar"])
            if mana_area is None:
                return None
            
            # 转换为HSV颜色空间
            hsv = cv2.cvtColor(mana_area, cv2.COLOR_BGR2HSV)
            
            # 检测蓝色蓝条
            lower_blue, upper_blue = self.color_thresholds["blue_mana"]
            mask = cv2.inRange(hsv, np.array(lower_blue), np.array(upper_blue))
            
            # 计算蓝色像素比例
            total_pixels = mask.shape[0] * mask.shape[1]
            blue_pixels = cv2.countNonZero(mask)
            
            if total_pixels > 0:
                mana_percentage = (blue_pixels / total_pixels) * 100
                return min(100, max(0, mana_percentage))
            
            return None
            
        except Exception as e:
            self.logger.error(f"检测蓝量失败: {str(e)} - By @ConceptualGod")
            return None
    
    def detect_game_time(self) -> Optional[str]:
        """
        检测游戏时间
        
        Returns:
            Optional[str]: 游戏时间字符串
            
        开发者: @ConceptualGod
        """
        try:
            # 截取游戏时间区域
            time_area = self.capture_screen_area(self.detection_areas["game_time"])
            if time_area is None:
                return None
            
            # 使用OCR识别时间
            results = self.ocr_reader.readtext(time_area)
            
            for (bbox, text, confidence) in results:
                if confidence > 0.5:
                    # 查找时间格式的文本 (如: 12:34)
                    if ":" in text and len(text.split(":")) == 2:
                        return text.strip()
            
            return None
            
        except Exception as e:
            self.logger.error(f"检测游戏时间失败: {str(e)} - By @ConceptualGod")
            return None
    
    def detect_enemies_nearby(self) -> int:
        """
        检测附近敌人数量
        
        Returns:
            int: 附近敌人数量
            
        开发者: @ConceptualGod
        """
        try:
            # 截取小地图区域
            minimap_area = self.capture_screen_area(self.detection_areas["minimap"])
            if minimap_area is None:
                return 0
            
            # 转换为HSV颜色空间
            hsv = cv2.cvtColor(minimap_area, cv2.COLOR_BGR2HSV)
            
            # 检测敌方红色标记
            lower_red, upper_red = self.color_thresholds["enemy_red"]
            mask = cv2.inRange(hsv, np.array(lower_red), np.array(upper_red))
            
            # 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 过滤小的噪点，只计算较大的红色区域
            enemy_count = 0
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 50:  # 最小面积阈值
                    enemy_count += 1
            
            return enemy_count
            
        except Exception as e:
            self.logger.error(f"检测附近敌人失败: {str(e)} - By @ConceptualGod")
            return 0
    
    def detect_allies_nearby(self) -> int:
        """
        检测附近队友数量
        
        Returns:
            int: 附近队友数量
            
        开发者: @ConceptualGod
        """
        try:
            # 截取小地图区域
            minimap_area = self.capture_screen_area(self.detection_areas["minimap"])
            if minimap_area is None:
                return 0
            
            # 转换为HSV颜色空间
            hsv = cv2.cvtColor(minimap_area, cv2.COLOR_BGR2HSV)
            
            # 检测友方蓝色标记
            lower_blue, upper_blue = self.color_thresholds["ally_blue"]
            mask = cv2.inRange(hsv, np.array(lower_blue), np.array(upper_blue))
            
            # 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 过滤小的噪点，只计算较大的蓝色区域
            ally_count = 0
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 50:  # 最小面积阈值
                    ally_count += 1
            
            return ally_count
            
        except Exception as e:
            self.logger.error(f"检测附近队友失败: {str(e)} - By @ConceptualGod")
            return 0
    
    def detect_jinnang_available(self) -> bool:
        """
        检测锦囊是否可用
        
        Returns:
            bool: 锦囊是否可用
            
        开发者: @ConceptualGod
        """
        try:
            # 截取锦囊区域
            jinnang_area = self.capture_screen_area(self.detection_areas["jinnang_area"])
            if jinnang_area is None:
                return False
            
            # 使用OCR识别锦囊相关文字
            results = self.ocr_reader.readtext(jinnang_area)
            
            for (bbox, text, confidence) in results:
                if confidence > 0.5:
                    # 查找锦囊相关关键词
                    if any(keyword in text for keyword in ["锦囊", "军机", "黄金"]):
                        return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检测锦囊状态失败: {str(e)} - By @ConceptualGod")
            return False
    
    def detect_equipment_slots(self) -> List[bool]:
        """
        检测装备栏状态
        
        Returns:
            List[bool]: 装备栏是否有装备的列表
            
        开发者: @ConceptualGod
        """
        try:
            # 截取装备区域
            equipment_area = self.capture_screen_area(self.detection_areas["equipment_area"])
            if equipment_area is None:
                return [False] * 9
            
            # 简化版：返回默认状态
            # 实际实现需要检测每个装备槽的状态
            return [False] * 9
            
        except Exception as e:
            self.logger.error(f"检测装备栏状态失败: {str(e)} - By @ConceptualGod")
            return [False] * 9
    
    def detect_hero_position(self) -> Optional[Tuple[int, int]]:
        """
        检测英雄位置
        
        Returns:
            Optional[Tuple[int, int]]: 英雄位置坐标
            
        开发者: @ConceptualGod
        """
        try:
            # 简化版：返回屏幕中心位置
            # 实际实现需要通过小地图或其他方式检测英雄位置
            screen_width, screen_height = pyautogui.size()
            return (screen_width // 2, screen_height // 2)
            
        except Exception as e:
            self.logger.error(f"检测英雄位置失败: {str(e)} - By @ConceptualGod")
            return None
    
    def get_game_state(self) -> Dict:
        """
        获取完整的游戏状态
        
        Returns:
            Dict: 游戏状态信息
            
        开发者: @ConceptualGod
        """
        try:
            state = {
                "blood_percentage": self.detect_blood_percentage(),
                "mana_percentage": self.detect_mana_percentage(),
                "game_time": self.detect_game_time(),
                "enemies_nearby": self.detect_enemies_nearby(),
                "allies_nearby": self.detect_allies_nearby(),
                "jinnang_available": self.detect_jinnang_available(),
                "equipment_slots": self.detect_equipment_slots(),
                "hero_position": self.detect_hero_position(),
                "timestamp": time.time()
            }
            
            return state
            
        except Exception as e:
            self.logger.error(f"获取游戏状态失败: {str(e)} - By @ConceptualGod")
            return {}
    
    def update_detection_areas(self, new_areas: Dict[str, Tuple[int, int, int, int]]):
        """
        更新检测区域配置
        
        Args:
            new_areas: 新的检测区域配置
            
        开发者: @ConceptualGod
        """
        self.detection_areas.update(new_areas)
        self.logger.info("检测区域配置已更新 - By @ConceptualGod")
    
    def update_color_thresholds(self, new_thresholds: Dict[str, Tuple[List[int], List[int]]]):
        """
        更新颜色阈值配置
        
        Args:
            new_thresholds: 新的颜色阈值配置
            
        开发者: @ConceptualGod
        """
        self.color_thresholds.update(new_thresholds)
        self.logger.info("颜色阈值配置已更新 - By @ConceptualGod")
