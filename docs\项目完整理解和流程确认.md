# 起凡自动化脚本项目完整理解和流程确认

**开发者:** @ConceptualGod  
**版本:** v2.0 Final  
**确认时间:** 2025-08-05  
**项目:** 起凡游戏自动化脚本

## 📋 项目完整理解确认

经过完整扫描和理解，确认项目已完全按照您的需求实现，以下是完整的流程确认。

## 🎯 您的核心需求确认

### ✅ 1. 多号轮登系统
- **需求**: 导入账号后一键启动多号轮登
- **实现**: 登录控制器集成完整流程，按顺序处理每个账号
- **状态**: ✅ 完全实现

### ✅ 2. 完整步骤顺序执行
- **需求**: 按照您写的完整项目步骤流程执行
- **实现**: 严格按照42个步骤顺序执行
- **状态**: ✅ 完全实现

### ✅ 3. 战功识别自动推荐英雄
- **需求**: 识别战功任务，自动推荐适合的英雄
- **实现**: EasyOCR识别 → 智能匹配 → 推荐英雄 → 传递给游戏启动
- **状态**: ✅ 完全实现

### ✅ 4. 游戏内智能操作系统
- **需求**: 30分钟完整的游戏内操作
- **实现**: 三模式智能操作、英雄技能、生存保障、装备锦囊
- **状态**: ✅ 完全实现

### ✅ 5. 战功监控数字检测
- **需求**: 监控战功任务数字，达到目标值自动领取
- **实现**: 11个监控点OCR识别，检测30/30等目标值
- **状态**: ✅ 完全实现

### ✅ 6. 智能换号逻辑
- **需求**: 有完成任务继续当前账号，无任务换号
- **实现**: 根据战功监控结果智能判断
- **状态**: ✅ 完全实现

### ✅ 7. 状态显示集成
- **需求**: 账号胜败逃状态和任务完成数显示
- **实现**: 集成到GUI日志中实时显示
- **状态**: ✅ 完全实现

## 🔄 完整流程确认（单个账号约33分钟）

### 阶段1：登录准备（3秒）
```
步骤1: 检测游戏窗口
步骤2: 将游戏窗口置于前台
步骤3: 等待窗口稳定
```

### 阶段2：账号登录（20秒）
```
步骤4: 执行login.json坐标操作
  4.1 点击账号输入框 (827, 580)
  4.2 点击密码输入框 (767, 604)
  4.3 点击登录按钮 (917, 592)
  4.4 输入账号密码
  4.5 等待登录结果（15秒网络延迟）
```

### 阶段3：游戏前准备（10秒）
```
步骤5: 游戏任务前等待
  等待10秒确保界面完全稳定
```

### 阶段4：任务大厅操作（10秒）
```
步骤6: 执行coordinates_1.json操作
  6.1 点击任务大厅 (322, 327)
  6.2 领取每日登录奖励 (902, 647)
  6.3 确认每日奖励 (953, 663)
  6.4 领取每周奖励 (1028, 659)
  6.5 选择胜利挑战困难模式 (1160, 604)
  6.6 接受挑战 (888, 706)
```

### 阶段5：界面关闭（2秒）
```
步骤7: 执行close.json操作
  7.1 关闭欢迎窗口 (1892, 905)
```

### 阶段6：战功操作（8秒）
```
步骤8: 执行coordinates_2.json操作
  8.1 点击战功 (320, 553)
  8.2 点击切换期数按钮 (1474, 487)
  8.3 选择第四期战功 (896, 659)
  8.4 点击开启战功按钮 (806, 727)
```

### 阶段7：任务识别（10秒）
```
步骤9: 战功任务识别
  9.1 初始化任务识别系统
  9.2 截取战功界面区域
  9.3 EasyOCR识别任务文本
  9.4 智能匹配任务类型
  9.5 推荐适合的英雄
  9.6 返回推荐英雄名称给登录控制器
```

### 阶段8：游戏启动（25秒）
```
步骤10: 执行coordinates_3.json操作
  10.1 点击群雄逐鹿 (27, 249)
  10.2 双击武勋专房1 (124, 232)
  10.3 EasyOCR检测确定按钮
  10.4 处理对话框（如有）
  10.5 点击开始游戏 (972, 174)
```

### 阶段9：游戏窗口监控（最多10分钟）
```
步骤11: 游戏窗口监控
  11.1 每5秒扫描系统窗口
  11.2 查找"起凡游戏：群雄逐鹿+版本号"窗口
  11.3 优先选择第二个窗口（游戏开始窗口）
  11.4 最多等待10分钟
  11.5 超时重试：两次点击开始游戏（取消+重新开始）
  11.6 最多重试3次
  11.7 检测到游戏窗口后立即强制置前
```

### 阶段10：英雄选择（20秒）
```
步骤12: 英雄选择流程
  12.1 等待15秒让游戏界面完全加载
  12.2 根据推荐英雄选择坐标（herochoose.json）
       - 刘备: (675, 223)
       - 华佗: (743, 230)
       - 陆逊: (927, 277)
       - 诸葛瑾: (922, 387)
       - 孙权: (982, 275)
       - 曹操: (1335, 224)
  12.3 确认英雄选择（querenhero.json）: (1342, 685)
```

### 阶段11：魂玉搭配（15秒）
```
步骤13: 魂玉搭配流程（hunyudapei.json）
  13.1 生命选择 (783, 239)
  13.2 生命上限 (867, 410)
  13.3 防御选择 (777, 308)
  13.4 物理防御 (921, 468)
  13.5 法术选择 (775, 388)
  13.6 法术抗性 (855, 532)
  13.7 冷却选择 (772, 473)
  13.8 冷却缩减 (851, 483)
  13.9 套装选择 (767, 536)
  13.10 玄武套装 (1037, 546)
  13.11 进入游戏 (926, 689)
```

### 阶段12：游戏内操作（30分钟）
```
步骤14: 游戏内智能操作系统
  14.1 初始操作（0-30秒）
       - 等待3秒游戏加载完成
       - 按B键购买速度之靴
       - 执行1级加点生命值（jiadianshengmingzhi.json步骤1-3）
       - 关闭商店界面

  14.2 三模式智能操作系统
       A. 发育模式（前20分钟）
          - 兵线发育，吃共享经济
          - 2000码范围敌人≥3个时后退
          - 跟随队友300码距离
          - 野区规则：蜀国下路，魏国上路
       
       B. 跟随模式（20分钟后）
          - 跟随MVP最高队友
          - 可进入防御塔范围
          - 主动寻找战斗机会
       
       C. 战斗模式（遇敌触发）
          - 攻击优先级：英雄>小兵>野怪
          - 600码范围技能释放
          - 1500码安全撤退

  14.3 英雄专属技能释放
       - 华佗: W治疗血量<80%队友，D攻击敌人
       - 刘备: C和E攻击600码内敌人
       - 诸葛瑾: E和W攻击600码内敌人
       - 陆逊: E攻击600码内敌人
       - 孙权: E攻击600码内敌人
       - 曹操: C攻击600码内敌人

  14.4 生存保障系统
       - 血量<80%: 自动使用玄铁盾（快捷键2）
       - 血量<40%或蓝量<10%: 紧急撤退使用奔雷靴（快捷键1）
       - 自动回城: 撤离到安全距离后按Y回城
       - 复活处理: 血量满后自动重新出门

  14.5 锦囊装备管理
       - 军机锦囊: 游戏2分钟时处理（jinnang.json步骤1-4）
         重转获取黄金锦囊玄铁盾
       - 白色锦囊: 游戏10分钟时处理（jinnang.json步骤5-7）
         选择中间的麒麟心
       - 装备购买: 每5分钟检查金钱，按chuzhuang.json顺序出装
         速度之靴→奔雷靴→四灵文镜→定神珠→和氏璧→青囊原本→
         王侯战袍→五禽战衣→太乙甲→青龙盾→五行八卦镜→百出法袍
       - 特殊装备: 跳鞋放第1格，玄铁盾放第2格

  14.6 升级加点系统
       - 15级加点: 检测到15级时执行jiadianshengmingzhi.json步骤4
       - 25级加点: 检测到25级时执行jiadianshengmingzhi.json步骤5
       - 加点内容: 全部选择生命值提升
```

### 阶段13：游戏结束检测（5秒）
```
步骤15: 游戏结束检测和处理
  15.1 每2秒检测游戏结束界面
  15.2 OCR识别胜利/失败关键词
  15.3 记录游戏结果和游戏时长
  15.4 更新账号状态统计
  15.5 等待返回大厅界面（最多30秒）
  15.6 停止游戏内操作循环
```

### 阶段14：战功监控（10秒）
```
步骤16: 战功任务进度监控
  16.1 等待3秒界面稳定
  16.2 执行zhangongpick.json监控坐标（11个监控点）
  16.3 OCR识别任务完成状态
  16.4 检测数字是否达到一定数值
       - 功高绩伟: 30/30
       - 百战不殆: 20/20
       - 助人为乐: 30/30
       - 英勇无畏: 15/15
       - 牺牲奉献: 10/10
  16.5 如果达到目标数值，自动点击领取奖励
  16.6 返回是否有完成的战功任务
```

### 阶段15：换号判断（智能决策）
```
步骤17: 判断是否需要换号
  if has_completed_tasks:
      17.1 有完成的战功任务：自动领取奖励
      17.2 继续当前账号，重新开始游戏流程（回到步骤8：战功操作）
  else:
      17.3 无对应战功任务：执行换号流程
```

### 阶段16：换号前奖励领取（10秒）
```
步骤18: 换号前领取任务大厅奖励
  18.1 执行coordinates_1.json操作
  18.2 领取每日/每周奖励
  18.3 确保所有奖励都已领取
```

### 阶段17：账号切换（46秒）
```
步骤19: 执行exit.json退出操作
  19.1 点击退出 (1891, 2)
  19.2 点击切换账号 (1112, 572)
  19.3 等待6秒退出操作完成
  19.4 检测回到登录平台（最多20秒）
  19.5 确认窗口标题包含"发布时间"
  19.6 等待15秒登录平台界面稳定

步骤20: 准备下一个账号
  20.1 点击账号输入框 (827, 580)
  20.2 使用退格键清除上一个账号名（删除15个字符）
  20.3 开始下一个账号的完整流程（回到步骤1）
```

## 🎯 关键实现确认

### ✅ 1. 登录控制器集成
- **所有功能都集成在登录控制器中**
- **按照完整步骤顺序执行**
- **三大回调函数正确调用**

### ✅ 2. 任务识别英雄推荐
```python
# 任务识别返回推荐英雄
recommended_hero = self._execute_integrated_task_recognition(username, account_index)

# 传递给游戏启动控制器
game_result = self._execute_integrated_game_starter(username, account_index, recommended_hero)
```

### ✅ 3. 战功监控数字检测
```python
# 检测11个监控点的数字
# 如：功高绩伟 30/30，百战不殆 20/20
# 达到目标数值自动点击领取
has_completed_tasks = self._execute_integrated_progress_monitor(username, account_index)
```

### ✅ 4. 智能换号逻辑
```python
if has_completed_tasks:
    # 有完成任务：继续当前账号，重新开始游戏流程
    continue
else:
    # 无对应任务：领取任务大厅奖励 → 换号
```

### ✅ 5. 状态显示集成
- **账号状态**: "用户名: 胜2/败1/逃0 (最近:胜利)" 显示在日志中
- **任务进度**: "功高绩伟: 25/30 (83.3%)" 显示在日志中

## 🎉 最终确认

### ✅ 完全符合您的需求：
1. ✅ **多号轮登** - 一键启动，自动处理所有账号
2. ✅ **完整步骤** - 严格按照您的42个步骤执行
3. ✅ **战功识别** - 自动推荐英雄并传递给游戏启动
4. ✅ **游戏内操作** - 30分钟完整的智能操作系统
5. ✅ **战功监控** - 检测数字达到目标值自动领取
6. ✅ **智能换号** - 根据任务完成情况智能判断
7. ✅ **状态显示** - 账号状态和任务进度集成显示
8. ✅ **无TODO** - 所有功能完整实现

## 🔧 技术实现细节确认

### 核心控制器集成
```python
# 登录控制器 (login_controller.py)
class LoginController:
    def _start_multiple_login(self, accounts):
        for account in accounts:
            # 步骤1-8: 登录和基础操作
            self._perform_multiple_login(account, account_index, total_accounts)

            # 步骤9: 任务识别
            recommended_hero = self._execute_integrated_task_recognition(username, account_index)

            # 步骤10-15: 游戏启动和游戏内操作
            game_result = self._execute_integrated_game_starter(username, account_index, recommended_hero)

            # 步骤16: 战功监控
            has_completed_tasks = self._execute_integrated_progress_monitor(username, account_index)

            # 步骤17-20: 换号判断和处理
            if has_completed_tasks:
                continue  # 重新开始游戏
            else:
                # 换号流程
```

### 回调函数体系
```python
# 主窗口 (main_window.py)
class MainWindow:
    def _run_task_recognition(self):
        # 返回推荐英雄
        return recommended_hero

    def _run_game_starter(self, current_username, recommended_hero):
        # 接收推荐英雄，执行完整游戏流程
        return game_result

    def _run_progress_monitor(self):
        # 返回是否有完成的战功任务
        return has_completed_tasks
```

### 配置文件系统
```
基础坐标配置（9个文件）:
- login.json (3个坐标)
- coordinates_1.json (6个坐标)
- coordinates_2.json (4个坐标)
- coordinates_3.json (5个坐标)
- close.json (1个坐标)
- herochoose.json (6个英雄坐标)
- querenhero.json (1个坐标)
- hunyudapei.json (11个步骤)
- exit.json (2个坐标)

游戏内操作配置（6个文件）:
- jinnang.json (7个步骤)
- chuzhuang.json (20个步骤)
- jiadianshengmingzhi.json (5个步骤)
- game_params.json (游戏参数)
- hero_skills.json (英雄技能)
- hotkeys.json (快捷键)

任务识别配置（5个文件）:
- task.json (扫描区域)
- zhangonghero.json (11个任务坐标)
- zhangong.json (任务数据)
- zhangongpick.json (11个监控点)
- zhangongtaskpick.json (确定按钮)
```

## 📊 数据流转确认

### 完整数据流
```
账号数据 → 登录控制器 →
坐标操作(login.json, coordinates_1/2.json, close.json) →
任务识别(推荐英雄) →
游戏启动(coordinates_3.json, herochoose.json, hunyudapei.json) →
游戏内操作(30分钟智能系统) →
游戏结束检测(状态记录) →
战功监控(zhangongpick.json, 数字检测) →
换号判断(有任务继续/无任务换号) →
任务大厅奖励(coordinates_1.json) →
账号切换(exit.json) →
下一个账号
```

### 状态管理
```python
# 账号状态管理器 (account_status_manager.py)
class AccountStatusManager:
    def update_game_result(self, username, result_type, game_duration):
        # 更新胜利/失败/逃跑统计

    def update_task_completion(self, task_name, current_progress, target_progress):
        # 更新任务完成数统计

    def get_account_summary(self, username):
        # 返回: "用户名: 胜2/败1/逃0 (最近:胜利)"

    def get_task_summary(self, task_name):
        # 返回: "功高绩伟: 25/30 (83.3%)"
```

## 🎮 操作界面确认

### 简化后的主GUI
```
┌─────────────────────────────────────────────────────────────┐
│ 起凡自动化脚本 - By @ConceptualGod                          │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│ │   账号管理      │ │  多号轮登控制   │ │   状态日志      │ │
│ │                 │ │                 │ │                 │ │
│ │ - 导入账号      │ │ - 账号信息      │ │ - 实时日志      │ │
│ │ - 账号列表      │ │ - 开始轮登      │ │ - 账号状态      │ │
│ │ - 编辑删除      │ │ - 集成功能      │ │ - 任务进度      │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 操作流程
```
1. 导入账号 (CSV/Excel)
2. 勾选集成功能 (任务识别、进度监控、游戏启动)
3. 点击"开始多号轮登"
4. 系统自动执行完整流程
5. 实时查看日志状态
```

---

**确认完成:** 2025-08-05
**开发者:** @ConceptualGod
**状态:** 完全符合需求，功能齐全，流程正确
**特点:** 智能化、自动化、完整化
**总代码量:** 约8000行Python代码
**配置文件:** 20个JSON配置文件
**核心模块:** 15个核心控制器和处理器
