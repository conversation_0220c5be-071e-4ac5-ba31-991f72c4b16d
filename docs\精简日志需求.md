# 精简版日志需求 - 只保留核心功能

**开发者:** @ConceptualGod  
**原则:** 只保留对自动化有直接帮助的核心日志，去除所有无关内容

## 🎯 真正需要的核心日志 (只有5个文件)

### **1. scoreLOG.log (⭐⭐⭐⭐⭐ 最重要)**
**用途:** 战功任务进度监控

**关键数据:**
```
[22] = 助攻数 (助攻任务: 20个、30个)
[29] = MVP分数 (MVP任务: 150、225)
[74] = 总伤害值 (牺牲值任务: 200K、300K、500K，需要计算转换)
[11] = 金币数量 (5000金币出装触发)
[16] = 英雄等级 (15级、25级加生命值)
```

**替代功能:**
- 替代助攻任务OCR识别
- 替代MVP任务OCR识别  
- 替代牺牲值任务OCR识别
- 替代金币监控OCR识别
- 替代等级监控OCR识别

### **2. end.log (⭐⭐⭐⭐⭐ 最重要)**
**用途:** 游戏结束和胜负判断

**关键数据:**
```
查询结果=5 (胜利)
查询结果=0 (失败)
战斗获得=824 (奖励)
```

**替代功能:**
- 替代游戏结束检测OCR
- 替代胜负判断OCR
- 替代胜利任务监控
- 替代完整局任务监控

### **3. buyItemCmd.log (⭐⭐⭐⭐)**
**用途:** 装备购买监控

**关键数据:**
```
装备购买记录
购买时间和物品ID
金币消耗统计
出装路线验证
```

**替代功能:**
- 监控B键买速度之靴
- 监控出装顺序执行
- 验证装备购买是否成功

### **4. Perf-fps.log (⭐⭐⭐)**
**用途:** 性能监控

**关键数据:**
```
[FPS,49.0,49.0,49.0] (帧率)
[Ping,47.0,47.0,47.0] (延迟)
```

**替代功能:**
- 监控游戏流畅度
- 监控网络连接质量
- 确保游戏稳定运行

### **5. error.log (⭐⭐)**
**用途:** 错误诊断

**关键数据:**
```
纹理加载错误
文件访问错误
异常情况记录
```

**替代功能:**
- 诊断游戏异常
- 监控系统稳定性

## 🚫 不需要的日志文件

### **明确不需要的:**
```
❌ net_state.log - 用户和房间信息 (您明确说不需要)
❌ eloScore.log - 玩家等级分数 (您明确说不需要)
❌ allcmd.log - 游戏命令记录 (操作由程序控制，不需要监控)
❌ guajitime.log - 挂机时间 (不需要监控挂机)
❌ disconnection.log - 断线重连 (网络问题程序会自动处理)
❌ goldass.log - 积分计算 (scoreLOG.log已有金币数据)
❌ turn.log - 游戏对象 (不需要监控游戏对象)
❌ voice.log - 语音通信 (不需要语音功能)
❌ powerScore.log - 英雄选择 (英雄选择由程序控制)
❌ HeroBackInfo_*.log - 英雄详细信息 (不需要详细信息)
❌ PlayerBackInfo_*.log - 玩家详细信息 (不需要详细信息)
```

### **击杀数说明:**
```
❌ [21] = 击杀数 (您明确说和牺牲值没关系，不需要)
✅ [74] = 总伤害值 (这个才是牺牲值计算基础)
```

## 📊 精简后的监控系统

### **核心功能实现:**
```python
class SimplifiedLogMonitor:
    def __init__(self):
        self.core_logs = {
            'scoreLOG.log': '战功任务进度',
            'end.log': '游戏结束判断', 
            'buyItemCmd.log': '装备购买',
            'Perf-fps.log': '性能监控',
            'error.log': '错误诊断'
        }
    
    def get_task_progress(self):
        """获取战功任务进度 - 替代OCR"""
        return {
            'assists': self.parse_assists(),      # [22] 助攻数
            'mvp': self.parse_mvp(),             # [29] MVP分数
            'sacrifice': self.parse_sacrifice(),  # [74] 牺牲值
            'gold': self.parse_gold(),           # [11] 金币
            'level': self.parse_level()          # [16] 等级
        }
    
    def check_game_end(self):
        """检查游戏结束 - 替代OCR"""
        return {
            'ended': self.parse_game_end(),      # 游戏是否结束
            'victory': self.parse_victory(),     # 是否胜利
            'complete_game': True               # 完整局+1
        }
    
    def monitor_equipment(self):
        """监控装备购买"""
        return self.parse_buy_items()
    
    def check_performance(self):
        """检查性能状态"""
        return {
            'fps': self.parse_fps(),
            'ping': self.parse_ping()
        }
```

## 🎯 实施优先级

### **第一阶段 (立即实施):**
1. **scoreLOG.log** - 战功任务核心
2. **end.log** - 游戏结束核心

### **第二阶段 (重要功能):**
3. **buyItemCmd.log** - 装备监控
4. **Perf-fps.log** - 性能监控

### **第三阶段 (辅助功能):**
5. **error.log** - 错误诊断

## 📋 数据映射关系

### **战功任务映射:**
```python
TASK_MAPPING = {
    # 助攻类任务
    'assists_20': {'source': '[22]', 'target': 20},
    'assists_30': {'source': '[22]', 'target': 30},
    
    # MVP类任务  
    'mvp_150': {'source': '[29]', 'target': 150},
    'mvp_225': {'source': '[29]', 'target': 225},
    
    # 牺牲值类任务 (需要转换公式)
    'sacrifice_200k': {'source': '[74]', 'target': 200, 'convert': True},
    'sacrifice_300k': {'source': '[74]', 'target': 300, 'convert': True},
    'sacrifice_500k': {'source': '[74]', 'target': 500, 'convert': True},
    
    # 胜利类任务
    'victory': {'source': 'end.log', 'condition': '查询结果=5'},
    
    # 完整局类任务
    'complete_game': {'source': 'end.log', 'condition': '游戏结束'}
}
```

### **游戏状态映射:**
```python
GAME_STATE_MAPPING = {
    'gold_trigger': {'source': '[11]', 'threshold': 5000},  # 出装触发
    'level_15': {'source': '[16]', 'threshold': 15},        # 加生命值
    'level_25': {'source': '[16]', 'threshold': 25},        # 加生命值
    'game_ended': {'source': 'end.log', 'condition': '查询结果'},
    'is_victory': {'source': 'end.log', 'condition': '查询结果=5'}
}
```

## ✅ 总结

**精简后只需要5个核心日志文件，去除了90%的无关内容：**

1. ✅ **scoreLOG.log** - 战功任务进度 (助攻、MVP、牺牲值、金币、等级)
2. ✅ **end.log** - 游戏结束和胜负 (胜利、完整局)  
3. ✅ **buyItemCmd.log** - 装备购买监控
4. ✅ **Perf-fps.log** - 性能监控
5. ✅ **error.log** - 错误诊断

**完全不需要:**
- ❌ 用户和房间信息
- ❌ 玩家等级分数  
- ❌ 击杀数 (和牺牲值无关)
- ❌ 游戏命令记录
- ❌ 其他60+个无关日志

**这个精简版本完全符合您的需求，专注于核心自动化功能！**
