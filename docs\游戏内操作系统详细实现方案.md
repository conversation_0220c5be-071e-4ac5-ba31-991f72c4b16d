# 游戏内操作系统详细实现方案

**开发者:** @ConceptualGod  
**版本:** v1.0  
**创建时间:** 2025-08-05  
**项目:** 起凡游戏自动化脚本

## 📋 系统概述

### 功能目标
基于现有的游戏内操作控制器，实现完整的游戏内智能操作系统，包括：
- **三种操作模式**: 发育模式、跟随模式、战斗模式
- **智能技能释放**: 根据英雄类型和敌人情况自动释放技能
- **血量蓝量监控**: 实时监控并自动处理危险情况
- **锦囊装备管理**: 自动处理锦囊和购买装备
- **位置和状态检测**: 基于OCR和图像识别的游戏状态检测

## 🏗️ 系统架构

### 核心模块结构
```
游戏内操作系统
├── 游戏内操作控制器 (game_operation_controller.py) [已实现]
├── 游戏状态检测器 (game_state_detector.py) [已实现]
├── 英雄操作器 (hero_operator.py) [需新增]
├── 模式管理器 (mode_manager.py) [需新增]
├── 位置检测器 (position_detector.py) [需新增]
└── 决策引擎 (decision_engine.py) [需新增]
```

## 🎯 详细实现方案

### 阶段1：基础操作系统（已完成）
✅ **游戏内操作控制器** - 基础框架和配置加载  
✅ **游戏状态检测器** - 血量、蓝量、时间检测  
✅ **配置文件系统** - JSON配置文件管理  

### 阶段2：英雄操作器实现

#### 2.1 英雄操作器 (hero_operator.py)
**功能职责:**
- 英雄移动控制
- 技能释放管理
- 装备使用控制
- 回城和复活处理

**核心方法:**
```python
class HeroOperator:
    def move_to_position(self, x, y)           # 移动到指定位置
    def follow_teammate(self, teammate_pos)    # 跟随队友
    def use_skill(self, skill_key, target)     # 使用技能
    def use_item(self, item_slot)              # 使用装备
    def return_to_base(self)                   # 回城
    def buy_equipment(self, equipment_list)    # 购买装备
```

**技能释放逻辑:**
- **华佗**: W技能治疗血量<80%的队友，D技能攻击敌人
- **刘备**: C和E技能攻击600码内敌人
- **诸葛瑾**: E和W技能攻击600码内敌人
- **陆逊**: E技能攻击600码内敌人
- **孙权**: E技能攻击600码内敌人
- **曹操**: C技能攻击600码内敌人

### 阶段3：模式管理器实现

#### 3.1 模式管理器 (mode_manager.py)
**功能职责:**
- 游戏模式切换管理
- 模式状态维护
- 切换条件检测

**三种模式详细设计:**

##### 发育模式 (前20分钟)
**触发条件:**
- 游戏时间 < 20分钟
- 或敌方英雄数量 > 我方英雄数量

**操作逻辑:**
1. **兵线发育**:
   - 检测小兵位置，在兵线附近徘徊
   - 保持能吃到共享经济的距离
   - 有技能CD时对小兵释放技能

2. **安全距离控制**:
   - 检测2000码范围内敌方英雄数量
   - 敌方英雄≥3个时后退到安全距离
   - 在防御塔下时放宽敌人数量限制

3. **跟随队友**:
   - 跟随最近的队友，保持300码距离
   - 队友回城(距离>2000码)时重新选择跟随目标

##### 跟随模式 (20分钟后)
**触发条件:**
- 游戏时间 ≥ 20分钟
- 复活后自动进入此模式

**操作逻辑:**
1. **MVP跟随**:
   - 检测己方存活英雄MVP值最高的进行跟随
   - 目标死亡则跟随最近的英雄

2. **战斗参与**:
   - 可进入防御塔范围
   - 碰到敌方单位后进入战斗模式

##### 战斗模式 (遇敌触发)
**触发条件:**
- 600码范围内出现敌方英雄或小兵
- 范围内必须有己方英雄支援

**操作逻辑:**
1. **攻击优先级**: 英雄 > 小兵 > 野怪
2. **技能释放**: 优先释放所有可用技能
3. **撤退条件**: 
   - 血量<40%或蓝量<10%
   - 1500码范围内无敌方英雄时撤退
4. **防御塔规则**: 20分钟前不进入敌方防御塔

### 阶段4：位置检测器实现

#### 4.1 位置检测器 (position_detector.py)
**功能职责:**
- 英雄位置检测
- 队友位置检测
- 敌人位置检测
- 距离计算

**检测方法:**
1. **小地图分析**: 通过小地图颜色识别位置
2. **屏幕中心检测**: 检测屏幕中心区域的单位
3. **血条检测**: 通过血条颜色识别敌友

### 阶段5：决策引擎实现

#### 5.1 决策引擎 (decision_engine.py)
**功能职责:**
- 综合分析游戏状态
- 做出操作决策
- 优先级管理

**决策优先级:**
1. **生存优先**: 血量<40%或蓝量<10%时优先撤退
2. **安全优先**: 敌人数量过多时优先撤退
3. **跟随优先**: 保持与队友的合理距离
4. **战斗优先**: 有利条件下主动参与战斗

## ⚙️ 配置参数详细说明

### 游戏参数配置 (game_params.json)
```json
{
  "blood_threshold": 80,              // 使用玄铁盾的血量阈值
  "mana_threshold": 10,               // 回城的蓝量阈值
  "low_blood_threshold": 40,          // 撤退的血量阈值
  "follow_distance": 300,             // 跟随队友的距离
  "retreat_distance": 1500,           // 撤退的安全距离
  "safe_distance": 2000,              // 重新跟随的距离阈值
  "development_range": 2000,          // 发育模式检索范围
  "battle_range": 600,                // 战斗模式技能释放范围
  "enemy_threshold": 3,               // 敌方英雄数量阈值
  "mode_switch_time": 20,             // 模式切换时间(分钟)
  "skill_cooldown": 3,                // 技能释放间隔(秒)
  "operation_interval": 0.5,          // 操作循环间隔(秒)
  "money_threshold": 5000,            // 出装金钱阈值
  "tower_range": 800,                 // 防御塔范围
  "respawn_check_interval": 5         // 复活检测间隔(秒)
}
```

### 英雄技能配置 (hero_skills.json)
```json
{
  "华佗": {
    "skills": ["W", "D"],
    "skill_range": 600,
    "heal_skill": "W",
    "heal_threshold": 80,
    "skill_priority": ["D", "W"],
    "description": "华佗：W治疗队友，D攻击敌人"
  },
  "刘备": {
    "skills": ["C", "E"],
    "skill_range": 600,
    "skill_priority": ["C", "E"],
    "description": "刘备：C和E攻击敌人"
  }
  // ... 其他英雄配置
}
```

### 快捷键配置 (hotkeys.json)
```json
{
  "buy_equipment": "b",
  "return_city": "y",
  "use_shield": "2",                  // 玄铁盾
  "use_boots": "1",                   // 奔雷靴
  "reroll_jinnang": "space",
  "add_points": "+",
  "skills": {
    "W": "w", "D": "d", "C": "c", "E": "e"
  },
  "items": {
    "item_1": "1", "item_2": "2", "item_3": "3",
    "item_4": "4", "item_5": "5", "item_6": "6",
    "item_7": "7", "item_8": "8", "item_9": "9"
  }
}
```

## 🔄 完整操作流程

### 游戏开始阶段
1. **初始化** (0-30秒)
   - 按B键购买速度之靴
   - 执行1级加点生命值
   - 启动游戏状态检测

2. **出门准备** (30-60秒)
   - 检测队友位置
   - 选择跟随目标
   - 进入发育模式

### 发育阶段 (1-20分钟)
1. **每0.5秒循环**:
   - 检测血量蓝量状态
   - 检测敌友位置
   - 根据情况释放技能
   - 维持跟随距离

2. **特殊时间点**:
   - 2分钟: 处理军机锦囊
   - 10分钟: 处理白色锦囊
   - 15级: 加点生命值
   - 每5分钟: 检查出装

### 跟随阶段 (20分钟后)
1. **模式切换**:
   - 自动切换到跟随模式
   - 寻找MVP最高队友
   - 可进入防御塔范围

2. **战斗参与**:
   - 主动寻找战斗机会
   - 优先攻击英雄目标
   - 技能CD好就释放

### 危险处理
1. **血量监控**:
   - 血量<80%: 使用玄铁盾(快捷键2)
   - 血量<40%: 使用奔雷靴撤离(快捷键1)

2. **撤退逻辑**:
   - 向主城方向移动
   - 到达安全距离后按Y回城
   - 血量满后重新出门

## 📊 实现优先级

### 高优先级 (必须实现)
1. ✅ **基础框架** - 已完成
2. 🔶 **英雄操作器** - 移动、技能、装备使用
3. 🔶 **血量蓝量监控** - 生存保障
4. 🔶 **简化模式管理** - 基于时间的模式切换

### 中优先级 (逐步完善)
1. 🔻 **位置检测器** - 基于小地图的位置识别
2. 🔻 **智能跟随** - 队友检测和跟随
3. 🔻 **战斗决策** - 攻击目标选择

### 低优先级 (后期优化)
1. 🔻 **复杂AI决策** - 高级战术决策
2. 🔻 **精确距离计算** - 精确的距离和范围判断
3. 🔻 **动态参数调整** - 根据战况调整参数

## 🛠️ 技术实现要点

### OCR识别优化
- **血量检测**: 识别血条数值或颜色比例
- **蓝量检测**: 识别蓝条数值或颜色比例
- **时间检测**: 识别游戏时间显示
- **金钱检测**: 识别当前金钱数量

### 图像识别优化
- **小地图分析**: 通过颜色识别敌友位置
- **血条检测**: 识别屏幕中的血条颜色
- **技能CD检测**: 识别技能图标状态

### 操作优化
- **随机化**: 操作时间和位置的随机化
- **人性化**: 模拟真实玩家的操作习惯
- **容错处理**: 完善的异常处理机制

## 🚀 分阶段实施计划

### 第一阶段：基础操作实现 (1-2天)
**目标**: 实现基本的游戏内操作功能

**任务清单:**
1. **完善英雄操作器**
   - 实现基础移动控制
   - 实现技能释放逻辑
   - 实现装备使用功能

2. **优化状态检测**
   - 完善血量蓝量检测
   - 添加金钱检测
   - 添加游戏时间检测

3. **基础模式实现**
   - 实现简化版发育模式
   - 实现基础跟随逻辑
   - 实现危险撤退机制

### 第二阶段：智能决策实现 (2-3天)
**目标**: 实现智能的游戏决策系统

**任务清单:**
1. **位置检测系统**
   - 实现小地图分析
   - 实现敌友识别
   - 实现距离计算

2. **模式管理器**
   - 实现三种模式切换
   - 实现模式状态管理
   - 实现切换条件检测

3. **决策引擎**
   - 实现优先级决策
   - 实现战斗决策
   - 实现撤退决策

### 第三阶段：系统优化完善 (1-2天)
**目标**: 优化系统性能和稳定性

**任务清单:**
1. **性能优化**
   - 优化OCR识别速度
   - 优化操作响应时间
   - 减少CPU和内存占用

2. **稳定性提升**
   - 完善异常处理
   - 添加重试机制
   - 优化容错能力

3. **参数调优**
   - 调整各种阈值参数
   - 优化操作时机
   - 提升成功率

## 📝 具体实现代码框架

### 英雄操作器实现示例
```python
class HeroOperator:
    def __init__(self, hero_name, hotkeys, game_params):
        self.hero_name = hero_name
        self.hotkeys = hotkeys
        self.game_params = game_params
        self.last_skill_time = {}

    def use_skills_on_enemies(self, enemies_nearby):
        """对附近敌人使用技能"""
        if not enemies_nearby:
            return

        hero_config = self.get_hero_config()
        for skill in hero_config.get("skills", []):
            if self.is_skill_ready(skill):
                keyboard.press_and_release(self.hotkeys["skills"][skill])
                self.last_skill_time[skill] = time.time()
                time.sleep(0.2)

    def heal_teammates(self):
        """华佗专用：治疗队友"""
        if self.hero_name == "华佗" and self.is_skill_ready("W"):
            # 检测队友血量，选择治疗目标
            keyboard.press_and_release(self.hotkeys["skills"]["W"])
            self.last_skill_time["W"] = time.time()
```

### 模式管理器实现示例
```python
class ModeManager:
    def __init__(self, game_params):
        self.current_mode = "发育模式"
        self.game_params = game_params
        self.game_start_time = time.time()

    def update_mode(self, game_state):
        """根据游戏状态更新模式"""
        game_time_minutes = (time.time() - self.game_start_time) / 60

        if game_time_minutes < self.game_params["mode_switch_time"]:
            # 前20分钟：发育模式
            if game_state["enemies_nearby"] > game_state["allies_nearby"]:
                self.current_mode = "发育模式"
            else:
                self.current_mode = "战斗模式"
        else:
            # 20分钟后：跟随模式
            if game_state["enemies_nearby"] > 0:
                self.current_mode = "战斗模式"
            else:
                self.current_mode = "跟随模式"
```

### 决策引擎实现示例
```python
class DecisionEngine:
    def __init__(self, game_params):
        self.game_params = game_params

    def make_decision(self, game_state, current_mode):
        """根据游戏状态和当前模式做出决策"""
        decisions = []

        # 生存优先决策
        if game_state["blood_percentage"] < self.game_params["low_blood_threshold"]:
            decisions.append({"action": "retreat", "priority": 10})

        if game_state["blood_percentage"] < self.game_params["blood_threshold"]:
            decisions.append({"action": "use_shield", "priority": 8})

        # 战斗决策
        if current_mode == "战斗模式" and game_state["enemies_nearby"] > 0:
            decisions.append({"action": "use_skills", "priority": 6})

        # 跟随决策
        if current_mode in ["发育模式", "跟随模式"]:
            decisions.append({"action": "follow_teammate", "priority": 4})

        # 按优先级排序并返回最高优先级决策
        decisions.sort(key=lambda x: x["priority"], reverse=True)
        return decisions[0] if decisions else {"action": "idle", "priority": 0}
```

## 🔧 配置文件扩展

### 检测区域配置 (detection_areas.json)
```json
{
  "blood_bar": [50, 50, 200, 80],
  "mana_bar": [50, 80, 200, 110],
  "game_time": [1800, 10, 1920, 40],
  "minimap": [1600, 800, 1920, 1080],
  "money_display": [1700, 50, 1850, 80],
  "skill_bar": [800, 950, 1120, 1000],
  "equipment_bar": [1400, 900, 1920, 1080]
}
```

### 颜色阈值配置 (color_thresholds.json)
```json
{
  "red_blood": [[0, 100, 100], [10, 255, 255]],
  "blue_mana": [[100, 100, 100], [130, 255, 255]],
  "enemy_red": [[0, 100, 100], [10, 255, 255]],
  "ally_blue": [[100, 100, 100], [130, 255, 255]],
  "gold_color": [[20, 100, 100], [30, 255, 255]]
}
```

## 📊 性能监控指标

### 关键性能指标 (KPI)
1. **响应时间**: 操作决策响应时间 < 1秒
2. **识别准确率**: OCR识别准确率 > 90%
3. **生存率**: 英雄死亡次数 < 3次/局
4. **技能释放率**: 技能CD好时释放率 > 80%
5. **跟随成功率**: 队友跟随成功率 > 85%

### 监控日志格式
```
[2025-08-05 10:30:15] [GAME_OP] 血量检测: 85% | 蓝量检测: 60% | 模式: 发育模式 - By @ConceptualGod
[2025-08-05 10:30:16] [GAME_OP] 检测到敌人: 2个 | 队友: 3个 | 决策: 使用技能 - By @ConceptualGod
[2025-08-05 10:30:17] [GAME_OP] 释放技能: D | 目标: 敌方英雄 | 成功: True - By @ConceptualGod
```

## 🎯 成功标准

### 功能完整性标准
- ✅ 能够自动完成30分钟游戏局
- ✅ 英雄死亡次数控制在合理范围
- ✅ 能够正确识别和处理各种游戏状态
- ✅ 能够自动处理锦囊和出装

### 稳定性标准
- ✅ 连续运行10局无崩溃
- ✅ 异常情况自动恢复
- ✅ 内存占用稳定在200MB以下
- ✅ CPU占用平均低于20%

### 智能化标准
- ✅ 能够根据战况自动调整策略
- ✅ 能够识别危险情况并及时撤退
- ✅ 能够有效跟随队友参与团战
- ✅ 能够合理使用技能和装备

---

**开发完成:** 2025-08-05
**开发者:** @ConceptualGod
**状态:** 详细实施方案已制定，包含完整的代码框架和实施计划
