#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI模块

开发者: @ConceptualGod
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional


# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from utils.logger import setup_logger
from .account_manager_gui import AccountManagerGUI
from .login_controller import LoginController

# 导入核心控制器模块
from core.progress_monitor_controller import ProgressMonitorController
from core.task_recognition_controller import TaskRecognitionController
from core.game_starter_controller import GameStarterController
from core.account_status_manager import AccountStatusManager

class MainWindow:
    """主窗口"""
    
    def __init__(self):
        """初始化主窗口"""
        # 设置日志
        self.logger = setup_logger()

        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("起凡自动化脚本 By @ConceptualGod")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)

        # 初始化核心控制器
        self.progress_monitor = None
        self.task_recognition = None
        self.game_starter = None
        self.last_recognition_result = None  # 保存最新的任务识别结果
        self._init_controllers()

        # 设置窗口图标
        self._set_window_icon()

        # 创建界面
        self._create_widgets()

        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

        self._log_to_gui("主窗口初始化完成 - By @ConceptualGod")

    def _init_controllers(self):
        """
        初始化核心控制器
        开发者: @ConceptualGod
        """
        try:
            # 初始化进度监控控制器
            self.progress_monitor = ProgressMonitorController()
            self._log_to_gui("进度监控控制器初始化完成 - By @ConceptualGod")

            # 初始化任务识别控制器
            self.task_recognition = TaskRecognitionController()
            self._log_to_gui("任务识别控制器初始化完成 - By @ConceptualGod")

            # 初始化游戏启动控制器
            self.game_starter = GameStarterController()
            self._log_to_gui("游戏启动控制器初始化完成 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"控制器初始化失败: {str(e)} - By @ConceptualGod")
            messagebox.showerror("错误 - By @ConceptualGod", f"控制器初始化失败: {str(e)}")

    def _set_window_icon(self):
        """
        设置窗口图标
        开发者: @ConceptualGod
        """
        try:
            # 获取logo文件路径
            logo_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "logo", "logo.png")

            if os.path.exists(logo_path):
                # 使用tkinter原生方法设置PNG图标（不依赖PIL）
                try:
                    # 设置窗口图标和任务栏图标
                    self.root.iconphoto(True, tk.PhotoImage(file=logo_path))
                    self._log_to_gui(f"成功设置窗口图标: {logo_path} - By @ConceptualGod")
                except tk.TclError as e:
                    # 如果PNG不支持，尝试转换为ICO或使用默认图标
                    self.logger.warning(f"PNG图标设置失败，使用默认图标: {str(e)} - By @ConceptualGod")
            else:
                self.logger.warning(f"图标文件不存在: {logo_path} - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"设置窗口图标失败: {str(e)} - By @ConceptualGod")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 账号管理选项卡
        account_frame = ttk.Frame(self.notebook)
        self.notebook.add(account_frame, text="账号管理")
        
        # 登录控制选项卡
        login_frame = ttk.Frame(self.notebook)
        self.notebook.add(login_frame, text="登录控制")
        
        # 创建账号管理GUI
        self.account_manager = AccountManagerGUI(account_frame)
        
        # 创建登录控制器
        self.login_controller = LoginController(login_frame)
        
        # 设置回调函数
        self.login_controller.set_account_callbacks(
            self.account_manager.get_accounts
        )

        # 设置集成功能回调
        self._setup_integration_callbacks()
        
        # 创建状态栏
        self._create_status_bar()

    def _setup_integration_callbacks(self):
        """
        设置集成功能回调
        开发者: @ConceptualGod
        """
        try:
            # 为登录控制器设置集成功能的回调函数
            if hasattr(self.login_controller, 'set_integration_callbacks'):
                self.login_controller.set_integration_callbacks(
                    progress_monitor=self._run_progress_monitor,
                    task_recognition=self._run_task_recognition,
                    game_starter=self._run_game_starter,
                    log_callback=self._log_to_gui
                )
                self._log_to_gui("集成功能回调设置完成 - By @ConceptualGod")
            else:
                # 如果登录控制器没有集成回调方法，直接添加
                self.login_controller.progress_monitor_callback = self._run_progress_monitor
                self.login_controller.task_recognition_callback = self._run_task_recognition
                self.login_controller.game_starter_callback = self._run_game_starter
                self.login_controller.log_callback = self._log_to_gui



                # 添加停止回调
                self.login_controller.game_operation_stop_callback = self._stop_game_operations
                self.login_controller.game_starter_stop_callback = self._stop_game_starter
                self.login_controller.task_recognition_stop_callback = self._stop_task_recognition
                self.login_controller.progress_monitor_stop_callback = self._stop_progress_monitor

                self._log_to_gui("集成功能回调直接设置完成 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"设置集成功能回调失败: {str(e)} - By @ConceptualGod")


    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

        # 状态标签
        self.status_label = ttk.Label(self.status_bar, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=5)

        # 分隔符
        ttk.Separator(self.status_bar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)



        # 署名信息
        signature_label = ttk.Label(self.status_bar, text="By @ConceptualGod | v1.3")
        signature_label.pack(side=tk.RIGHT, padx=5)

        # 初始化账号状态管理器
        try:
            from core.account_status_manager import AccountStatusManager
            self.account_status_manager = AccountStatusManager()
        except Exception as e:
            self.logger.error(f"初始化账号状态管理器失败: {e}")
            self.account_status_manager = None

        # 初始化日志监控器
        try:
            from core.simple_log_monitor import CompleteGameLogMonitor
            self.log_monitor = CompleteGameLogMonitor("7fgame")
        except Exception as e:
            self.logger.error(f"初始化日志监控器失败: {e}")
            self.log_monitor = None

        # 启动状态更新线程
        self._start_status_update_thread()

    def _start_status_update_thread(self):
        """启动状态更新线程"""
        import threading
        import time

        def update_status_loop():
            """状态更新循环 - 只监控游戏结束状态"""
            while True:
                try:
                    # 只监控游戏状态变化，用于在日志中显示账号状态
                    self._monitor_game_status_for_logging()

                    time.sleep(5)  # 每5秒检查一次

                except Exception as e:
                    self.logger.error(f"状态监控异常: {e}")
                    time.sleep(10)  # 出错时等待10秒再重试

        # 启动后台线程
        status_thread = threading.Thread(target=update_status_loop, daemon=True)
        status_thread.start()

    def _monitor_game_status_for_logging(self):
        """监控游戏状态变化，用于在日志中显示账号状态"""
        try:
            if not self.log_monitor:
                return

            # 获取实时游戏状态
            game_status = self.log_monitor.get_game_status()
            task_progress = self.log_monitor.get_task_progress()

            # 显示监控状态（每30次循环显示一次，避免刷屏）
            if not hasattr(self, '_monitor_count'):
                self._monitor_count = 0
            self._monitor_count += 1

            if self._monitor_count % 30 == 1:  # 每30次显示一次状态
                if task_progress:
                    current_gold = task_progress.get('gold', 0)
                    current_level = task_progress.get('hero_level', 0)
                    self._log_to_gui(f"实时监控 - 金币:{current_gold} 等级:{current_level} - By @ConceptualGod")
                else:
                    self._log_to_gui(f"实时监控 - 等待游戏数据... - By @ConceptualGod")

            if game_status and task_progress:
                # 如果游戏结束，更新账号状态并在日志中显示
                if game_status.get('game_ended', False):
                    self._update_account_game_result(game_status.get('is_victory', False))

                # 检测逃跑状态 (被踢或退出)
                self._check_escape_status()

                # 检测5000金币出装提醒
                self._check_gold_equipment_trigger(task_progress)

                # 检测英雄等级加点
                self._check_level_upgrade_trigger(task_progress)

        except Exception as e:
            self.logger.error(f"监控游戏状态失败: {e}")
            self._log_to_gui(f"游戏状态监控异常: {str(e)} - By @ConceptualGod")

    def _update_account_game_result(self, is_victory):
        """更新账号游戏结果并在GUI日志中显示"""
        try:
            if not self.account_status_manager:
                return

            # 获取当前账号
            current_account = None
            if hasattr(self.login_controller, 'current_account') and self.login_controller.current_account:
                current_account = self.login_controller.current_account.get('username', '')

            if current_account:
                # 更新游戏结果
                result_type = "victory" if is_victory else "defeat"
                self.account_status_manager.update_game_result(current_account, result_type, 1800)  # 假设30分钟

                # 获取更新后的账号状态摘要
                account_summary = self.account_status_manager.get_account_summary(current_account)

                # 在GUI日志中显示游戏结果和账号状态
                result_text = "胜利" if is_victory else "失败"
                self._log_to_gui(f"=== 游戏结束 === - By @ConceptualGod")
                self._log_to_gui(f"游戏结果: {result_text} - By @ConceptualGod")
                self._log_to_gui(f"账号状态: {account_summary} - By @ConceptualGod")

                # 记录到日志文件
                self._log_to_gui(f"账号 {current_account} 游戏结果: {result_text}")
                self._log_to_gui(f"账号状态更新: {account_summary}")

        except Exception as e:
            self.logger.error(f"更新账号游戏结果失败: {e}")

    def _check_escape_status(self):
        """检测逃跑状态 (被踢或退出视为逃跑)"""
        try:
            if not self.log_monitor:
                return

            # 检查错误日志中是否有被踢或断线记录
            if hasattr(self.log_monitor, 'game_data') and 'errors' in self.log_monitor.game_data:
                error_data = self.log_monitor.game_data['errors']
                last_error = error_data.get('last_error', '')

                # 检测逃跑相关的错误信息
                escape_keywords = ['断线', '连接', '踢出', '退出', 'disconnect', 'timeout', 'kicked']
                is_escape = any(keyword in last_error.lower() for keyword in escape_keywords)

                if is_escape and error_data.get('error_count', 0) > 0:
                    # 获取当前账号
                    current_account = None
                    if hasattr(self.login_controller, 'current_account') and self.login_controller.current_account:
                        current_account = self.login_controller.current_account.get('username', '')

                    if current_account and self.account_status_manager:
                        # 更新逃跑状态
                        self.account_status_manager.update_game_result(current_account, "escape", 0)

                        # 获取更新后的账号状态摘要
                        account_summary = self.account_status_manager.get_account_summary(current_account)

                        # 在GUI日志中显示逃跑状态
                        self._log_to_gui(f"=== 检测到逃跑 === - By @ConceptualGod")
                        self._log_to_gui(f"逃跑原因: {last_error} - By @ConceptualGod")
                        self._log_to_gui(f"账号状态: {account_summary} - By @ConceptualGod")

                        # 记录到日志文件
                        self.logger.warning(f"账号 {current_account} 逃跑: {last_error}")
                        self._log_to_gui(f"账号状态更新: {account_summary}")

            # 检查挂机时间，超时也视为逃跑
            if hasattr(self.log_monitor, 'game_data') and 'afk_detection' in self.log_monitor.game_data:
                afk_data = self.log_monitor.game_data['afk_detection']
                afk_time = afk_data.get('afk_time', 0)

                # 如果挂机时间超过10分钟，视为逃跑
                if afk_time > 600:  # 600秒 = 10分钟
                    current_account = None
                    if hasattr(self.login_controller, 'current_account') and self.login_controller.current_account:
                        current_account = self.login_controller.current_account.get('username', '')

                    if current_account and self.account_status_manager:
                        # 更新逃跑状态
                        self.account_status_manager.update_game_result(current_account, "timeout", afk_time)

                        # 获取更新后的账号状态摘要
                        account_summary = self.account_status_manager.get_account_summary(current_account)

                        # 在GUI日志中显示超时逃跑状态
                        self._log_to_gui(f"=== 检测到超时逃跑 === - By @ConceptualGod")
                        self._log_to_gui(f"挂机时间: {afk_time}秒 (超过10分钟) - By @ConceptualGod")
                        self._log_to_gui(f"账号状态: {account_summary} - By @ConceptualGod")

                        # 记录到日志文件
                        self.logger.warning(f"账号 {current_account} 超时逃跑: 挂机{afk_time}秒")
                        self._log_to_gui(f"账号状态更新: {account_summary}")

        except Exception as e:
            self.logger.error(f"检测逃跑状态失败: {e}")

    def _check_gold_equipment_trigger(self, task_progress):
        """检测5000金币出装触发条件"""
        try:
            if not task_progress:
                return

            current_gold = task_progress.get('gold', 0)

            # 检查是否达到5000金币
            if current_gold >= 5000:
                # 检查是否已经提醒过（避免重复提醒）
                if not hasattr(self, '_gold_equipment_triggered'):
                    self._gold_equipment_triggered = True

                    # 在GUI日志中显示5000金币出装提醒
                    self._log_to_gui(f"=== 5000金币出装触发 === - By @ConceptualGod")
                    self._log_to_gui(f"当前金币: {current_gold} (已达到5000金币) - By @ConceptualGod")
                    self._log_to_gui(f"开始自动出装流程... - By @ConceptualGod")

                    # 自动执行出装操作
                    self._execute_auto_equipment()

                    # 记录到日志文件
                    self._log_to_gui(f"5000金币出装触发: 当前金币{current_gold}")

        except Exception as e:
            self.logger.error(f"检测5000金币出装失败: {e}")
            self._log_to_gui(f"5000金币出装检测异常: {str(e)} - By @ConceptualGod")

    def _check_level_upgrade_trigger(self, task_progress):
        """检测英雄等级加点触发条件"""
        try:
            if not task_progress:
                return

            current_level = task_progress.get('hero_level', 0)

            # 检查1级、15级、25级加点
            if current_level == 1:
                if not hasattr(self, '_level_1_triggered'):
                    self._level_1_triggered = True
                    self._log_to_gui(f"=== 检测到1级 === - By @ConceptualGod")
                    self._log_to_gui(f"触发1级觉醒生命值加点 - By @ConceptualGod")
                    self._execute_level_upgrade(1, "1级觉醒生命值")

            elif current_level == 15:
                if not hasattr(self, '_level_15_triggered'):
                    self._level_15_triggered = True
                    self._log_to_gui(f"=== 检测到15级 === - By @ConceptualGod")
                    self._log_to_gui(f"触发15级觉醒生命值加点 - By @ConceptualGod")
                    self._execute_level_upgrade(15, "15级觉醒生命值")

            elif current_level == 25:
                if not hasattr(self, '_level_25_triggered'):
                    self._level_25_triggered = True
                    self._log_to_gui(f"=== 检测到25级 === - By @ConceptualGod")
                    self._log_to_gui(f"触发25级觉醒生命值加点 - By @ConceptualGod")
                    self._execute_level_upgrade(25, "25级觉醒生命值")

        except Exception as e:
            self.logger.error(f"检测英雄等级加点失败: {e}")
            self._log_to_gui(f"英雄等级加点检测异常: {str(e)} - By @ConceptualGod")

    def _execute_auto_equipment(self):
        """执行自动出装操作"""
        try:
            # 在GUI日志中显示出装操作
            self._log_to_gui(f"=== 开始自动出装流程 === - By @ConceptualGod")
            self._log_to_gui(f"按照chuzhuang.json配置执行出装 - By @ConceptualGod")

            # 调用登录控制器的出装方法
            if hasattr(self.login_controller, '_execute_coordinate_file_for_multiple'):
                self._log_to_gui(f"正在执行出装操作... - By @ConceptualGod")
                result = self.login_controller._execute_coordinate_file_for_multiple(
                    "chuzhuang.json", "自动出装", 1
                )

                if result:
                    self._log_to_gui(f"自动出装执行成功！ - By @ConceptualGod")
                    self._log_to_gui(f"出装流程完成，装备已购买 - By @ConceptualGod")
                else:
                    self._log_to_gui(f"自动出装执行失败 - By @ConceptualGod")
                    self._log_to_gui(f"建议: 手动按B键检查商店 - By @ConceptualGod")
            else:
                self._log_to_gui(f"出装功能不可用 - By @ConceptualGod")
                self._log_to_gui(f"建议: 手动按B键购买装备 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"执行自动出装失败: {e}")
            self._log_to_gui(f"自动出装异常: {str(e)} - By @ConceptualGod")

    def _execute_level_upgrade(self, level, description):
        """执行等级加点操作"""
        try:
            # 在GUI日志中显示加点操作
            self._log_to_gui(f"=== {description}开始 === - By @ConceptualGod")
            self._log_to_gui(f"当前等级: {level}级，开始自动加点生命值 - By @ConceptualGod")

            # 根据等级确定执行的步骤
            if level == 1:
                # 1级觉醒：执行步骤1-3 (加点按钮 -> 1级觉醒生命值 -> 确定按钮)
                steps_to_execute = [1, 2, 3]
                self._log_to_gui(f"执行步骤: 加点按钮 -> 1级觉醒生命值 -> 确定按钮 - By @ConceptualGod")
            elif level == 15:
                # 15级加点：执行步骤1, 4, 3 (加点按钮 -> 15级觉醒生命值 -> 确定按钮)
                steps_to_execute = [1, 4, 3]
                self._log_to_gui(f"执行步骤: 加点按钮 -> 15级觉醒生命值 -> 确定按钮 - By @ConceptualGod")
            elif level == 25:
                # 25级加点：执行步骤1, 5, 3 (加点按钮 -> 25级觉醒生命值 -> 确定按钮)
                steps_to_execute = [1, 5, 3]
                self._log_to_gui(f"执行步骤: 加点按钮 -> 25级觉醒生命值 -> 确定按钮 - By @ConceptualGod")
            else:
                return

            # 执行加点操作
            self._log_to_gui(f"正在执行{level}级加点操作... - By @ConceptualGod")
            if hasattr(self.login_controller, '_execute_specific_steps_from_json'):
                result = self.login_controller._execute_specific_steps_from_json(
                    "jiadianshengmingzhi.json", steps_to_execute, f"{level}级加点"
                )

                if result:
                    self._log_to_gui(f"{description}执行成功！ - By @ConceptualGod")
                    self._log_to_gui(f"生命值加点完成，英雄更强了！ - By @ConceptualGod")
                else:
                    self._log_to_gui(f"{description}执行失败 - By @ConceptualGod")
                    self._log_to_gui(f"建议: 手动点击加点按钮 - By @ConceptualGod")
            else:
                # 备用方法：使用通用执行方法
                result = self.login_controller._execute_coordinate_file_for_multiple(
                    "jiadianshengmingzhi.json", description, 1
                )

                if result:
                    self._log_to_gui(f"{description}执行成功！ - By @ConceptualGod")
                    self._log_to_gui(f"生命值加点完成，英雄更强了！ - By @ConceptualGod")
                else:
                    self._log_to_gui(f"{description}执行失败 - By @ConceptualGod")
                    self._log_to_gui(f"建议: 手动点击加点按钮 - By @ConceptualGod")

            # 记录到日志文件
            self._log_to_gui(f"{description}执行完成")

        except Exception as e:
            self.logger.error(f"执行{description}失败: {e}")
            self._log_to_gui(f"{description}异常: {str(e)} - By @ConceptualGod")

    def update_status_bar(self, message):
        """更新主状态栏消息"""
        try:
            self.root.after(0, lambda: self.status_label.config(text=message))
        except Exception as e:
            self.logger.error(f"更新状态栏失败: {e}")

    def _on_closing(self):
        """窗口关闭事件"""
        try:
            # 停止登录操作
            if hasattr(self.login_controller, 'is_running') and self.login_controller.is_running:
                if messagebox.askyesno("确认 - By @ConceptualGod", "登录正在进行中，确定要退出吗？"):
                    self.login_controller._stop_login()
                else:
                    return
            
            self._log_to_gui("程序退出")
            self.root.destroy()
            
        except Exception as e:
            self.logger.error(f"程序退出异常: {str(e)}")
            self.root.destroy()
    
    def _run_progress_monitor(self):
        """
        运行进度监控，检测战功任务完成情况

        Returns:
            bool: 是否有已完成的战功任务

        开发者: @ConceptualGod
        """
        try:
            if self.progress_monitor:
                self._log_to_gui("开始战功任务进度监控 - By @ConceptualGod")

                # 初始化监控系统
                if not self.progress_monitor.is_initialized:
                    init_result = self.progress_monitor.initialize_monitoring()
                    if not init_result:
                        self._log_to_gui("进度监控初始化失败 - By @ConceptualGod")
                        return False

                # 执行监控检测
                monitoring_result = self.progress_monitor.execute_monitoring()
                if monitoring_result and monitoring_result.get('success', False):
                    completed_tasks = monitoring_result.get('completed_tasks', [])

                    if completed_tasks:
                        self._log_to_gui(f"检测到 {len(completed_tasks)} 个已完成的战功任务 - By @ConceptualGod")
                        for task in completed_tasks:
                            task_name = task.get('name', '未知任务')
                            progress = task.get('progress', '未知')
                            self._log_to_gui(f"已完成任务: {task_name} - 进度: {progress} - By @ConceptualGod")

                        # 自动领取奖励
                        self._log_to_gui("自动领取已完成的战功任务奖励 - By @ConceptualGod")
                        return True
                    else:
                        self._log_to_gui("未检测到已完成的战功任务 - By @ConceptualGod")
                        return False
                else:
                    self._log_to_gui("战功任务监控检测失败 - By @ConceptualGod")
                    return False
            else:
                self._log_to_gui("进度监控控制器未初始化 - By @ConceptualGod")
                return False
        except Exception as e:
            self._log_to_gui(f"进度监控异常: {str(e)} - By @ConceptualGod")
            return False

    def _run_task_recognition(self):
        """
        运行任务识别
        开发者: @ConceptualGod
        """
        try:
            if self.task_recognition:
                self._log_to_gui("开始任务识别 - By @ConceptualGod")

                # 设置日志回调，确保GUI和命令行日志一致
                self.task_recognition.log_callback = self._log_to_gui

                # 初始化任务识别系统
                if not self.task_recognition.is_initialized:
                    init_result = self.task_recognition.initialize()
                    if not init_result:
                        self._log_to_gui("任务识别系统初始化失败 - By @ConceptualGod")
                        return False

                # 执行任务识别
                result = self.task_recognition.recognize_tasks()

                # 保存识别结果供其他模块使用
                self.last_recognition_result = result

                if result and result.get('success', False):
                    # 获取匹配结果中的排序任务
                    match_result = result.get('match_result', {})
                    matched_tasks = match_result.get('matched_tasks', [])

                    self._log_to_gui(f"任务识别成功，发现 {len(matched_tasks)} 个任务 - By @ConceptualGod")

                    # 显示完整的任务排序结果和智能英雄推荐
                    recommended_hero = None
                    if matched_tasks:
                        self._log_to_gui(f"=== 任务执行顺序（按优先级排序） === - By @ConceptualGod")
                        self._log_to_gui(f"任务执行顺序: - By @ConceptualGod")

                        # 显示所有任务的执行顺序
                        for i, task in enumerate(matched_tasks, 1):
                            task_desc = task.get('task_desc', '')
                            task_type = task.get('task_type', '')
                            hero_type = task.get('hero_type', '')
                            task_hero = task.get('recommended_hero', '')

                            self._log_to_gui(f"  {i}. [{task_type}] {task_desc} ({hero_type}) - 推荐: {task_hero} - By @ConceptualGod")

                        # 智能分析最优英雄组合
                        recommended_hero = self._analyze_optimal_hero_for_gui(matched_tasks)
                        self._log_to_gui(f"=== 智能英雄推荐 === - By @ConceptualGod")
                        self._log_to_gui(f"最优英雄: {recommended_hero} - By @ConceptualGod")

                        # 显示推荐原因
                        self._explain_hero_recommendation_for_gui(matched_tasks, recommended_hero)
                    else:
                        recommended_hero = "华佗"
                        self._log_to_gui("未识别到目标任务，使用默认英雄华佗 - By @ConceptualGod")

                    # 将推荐英雄传递给游戏启动控制器
                    if recommended_hero and self.game_starter:
                        self.game_starter.set_recommended_hero(recommended_hero)
                        self._log_to_gui(f"已设置推荐英雄: {recommended_hero} - By @ConceptualGod")

                    # 返回推荐英雄给登录控制器
                    return recommended_hero
                else:
                    self._log_to_gui("任务识别失败 - By @ConceptualGod")
                    return False
            else:
                self._log_to_gui("任务识别控制器未初始化 - By @ConceptualGod")
                return False
        except Exception as e:
            self._log_to_gui(f"任务识别异常: {str(e)} - By @ConceptualGod")
            return False

    def _analyze_optimal_hero_for_gui(self, tasks: List[Dict[str, Any]]) -> str:
        """为GUI分析最优英雄组合"""
        try:
            if not tasks:
                return "华佗"

            # 收集所有可能的英雄
            all_heroes = set()
            hero_task_compatibility = {}

            for task in tasks:
                recommended_hero = task.get('recommended_hero', '')
                if recommended_hero and recommended_hero != "未知英雄":
                    all_heroes.add(recommended_hero)

                    if recommended_hero not in hero_task_compatibility:
                        hero_task_compatibility[recommended_hero] = []
                    hero_task_compatibility[recommended_hero].append(task)

            if not all_heroes:
                return "华佗"

            # 分析每个英雄的综合得分
            hero_scores = {}
            for hero in all_heroes:
                compatible_tasks = hero_task_compatibility.get(hero, [])

                # 任务数量得分
                task_count_score = len(compatible_tasks) * 10

                # 任务类型多样性得分
                task_types = set(task.get('task_type', '') for task in compatible_tasks)
                diversity_score = len(task_types) * 5

                # 英雄类型覆盖得分
                hero_types = set(task.get('hero_type', '') for task in compatible_tasks)
                coverage_score = 0
                if "任意英雄" in hero_types:
                    coverage_score += 15
                if len(hero_types) > 1:
                    coverage_score += 10

                # 任务优先级得分
                priority_score = 0
                task_priority = {"胜利类": 10, "完整局类": 8, "MVP类": 6, "助攻类": 4, "牺牲值类": 2}
                for task in compatible_tasks:
                    task_type = task.get('task_type', '')
                    priority_score += task_priority.get(task_type, 1)

                total_score = task_count_score + diversity_score + coverage_score + priority_score
                hero_scores[hero] = total_score

            # 选择得分最高的英雄
            optimal_hero = max(hero_scores.items(), key=lambda x: x[1])[0]
            return optimal_hero

        except Exception as e:
            return tasks[0].get('recommended_hero', '华佗') if tasks else "华佗"

    def _explain_hero_recommendation_for_gui(self, tasks: List[Dict[str, Any]], optimal_hero: str):
        """为GUI解释英雄推荐的原因"""
        try:
            # 统计该英雄能完成的任务
            compatible_tasks = []
            incompatible_tasks = []

            for task in tasks:
                recommended_hero = task.get('recommended_hero', '')
                if recommended_hero == optimal_hero:
                    compatible_tasks.append(task)
                else:
                    incompatible_tasks.append(task)

            # 解释推荐原因
            if len(compatible_tasks) == len(tasks):
                self._log_to_gui(f"推荐原因: {optimal_hero}能完成所有{len(tasks)}个任务，效率最高 - By @ConceptualGod")
            elif len(compatible_tasks) > len(incompatible_tasks):
                self._log_to_gui(f"推荐原因: {optimal_hero}能完成{len(compatible_tasks)}/{len(tasks)}个任务，覆盖率最高 - By @ConceptualGod")
            else:
                self._log_to_gui(f"推荐原因: {optimal_hero}适合完成优先级最高的任务 - By @ConceptualGod")

            # 详细说明任务分配
            if compatible_tasks:
                self._log_to_gui(f"{optimal_hero}可完成的任务: - By @ConceptualGod")
                for task in compatible_tasks:
                    task_desc = task.get('task_desc', '')
                    task_type = task.get('task_type', '')
                    self._log_to_gui(f"  ✓ [{task_type}] {task_desc} - By @ConceptualGod")

            if incompatible_tasks:
                self._log_to_gui(f"需要其他英雄完成的任务: - By @ConceptualGod")
                for task in incompatible_tasks:
                    task_desc = task.get('task_desc', '')
                    task_type = task.get('task_type', '')
                    recommended_hero = task.get('recommended_hero', '')
                    self._log_to_gui(f"  → [{task_type}] {task_desc} (建议: {recommended_hero}) - By @ConceptualGod")

                self._log_to_gui(f"建议: 先用{optimal_hero}完成主要任务，再考虑切换英雄 - By @ConceptualGod")

        except Exception as e:
            pass

    def _run_game_starter(self, current_username: str = "", recommended_hero: str = "华佗"):
        """
        运行开始游戏

        Args:
            current_username: 当前账号用户名
            recommended_hero: 推荐的英雄名称

        开发者: @ConceptualGod
        """
        try:
            if self.game_starter:
                self._log_to_gui(f"开始游戏启动流程，推荐英雄: {recommended_hero} - By @ConceptualGod")

                # 设置当前账号信息和推荐英雄
                if current_username:
                    self.game_starter.current_username = current_username
                if recommended_hero:
                    self.game_starter.set_recommended_hero(recommended_hero)

                # 设置日志回调，确保GUI和命令行日志一致
                self.game_starter.log_callback = self._log_to_gui



                # 执行游戏启动流程
                result = self.game_starter.start_game_flow()
                if result:
                    self._log_to_gui("完整游戏流程执行成功 - By @ConceptualGod")
                    return True
                else:
                    self._log_to_gui("完整游戏流程执行失败 - By @ConceptualGod")
                    return False
            else:
                self._log_to_gui("游戏启动控制器未初始化 - By @ConceptualGod")
                return False
        except Exception as e:
            self._log_to_gui(f"游戏启动异常: {str(e)} - By @ConceptualGod")
            return False

    def _log_to_gui(self, message):
        """
        在GUI中显示日志信息
        开发者: @ConceptualGod
        """
        try:
            # 将日志信息显示到登录控制器的状态文本框中
            if hasattr(self, 'login_controller') and hasattr(self.login_controller, 'status_text'):
                import time
                timestamp = time.strftime("%H:%M:%S")
                log_message = f"[{timestamp}] {message}\n"

                # 更新状态文本框
                self.login_controller.status_text.config(state=tk.NORMAL)
                self.login_controller.status_text.insert(tk.END, log_message)
                self.login_controller.status_text.see(tk.END)
                self.login_controller.status_text.config(state=tk.DISABLED)

                # 同时记录到日志文件
                self.logger.info(message)
            else:
                # 如果登录控制器还没有初始化，只记录到日志文件
                self.logger.info(message)
        except Exception as e:
            self.logger.error(f"GUI日志显示异常: {str(e)} - By @ConceptualGod")

    def run(self):
        """运行主窗口"""
        try:
            self._log_to_gui("启动GUI界面 - By @ConceptualGod")
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"GUI运行异常: {str(e)}")
            messagebox.showerror("错误 - By @ConceptualGod", f"程序运行异常: {str(e)}")

    def _stop_game_operations(self):
        """
        停止游戏内操作

        开发者: @ConceptualGod
        """
        try:
            if self.game_operation:
                self.game_operation.stop_operations()
                self._log_to_gui("游戏内操作已停止 - By @ConceptualGod")
        except Exception as e:
            self.logger.error(f"停止游戏内操作失败: {str(e)} - By @ConceptualGod")

    def _run_game_operations(self, current_username: str = None):
        """
        运行游戏内操作

        Args:
            current_username: 当前账号用户名

        Returns:
            bool: 游戏内操作是否成功

        开发者: @ConceptualGod
        """
        try:
            self._log_to_gui(f"开始运行游戏内操作，当前账号: {current_username} - By @ConceptualGod")

            if self.game_operation:
                # 设置当前账号信息
                if current_username:
                    self.game_operation.current_username = current_username

                # 设置日志回调，确保GUI和命令行日志一致
                self.game_operation.log_callback = self._log_to_gui



                # 执行游戏内操作
                result = self.game_operation.start_game_operations()

                if result:
                    self._log_to_gui(f"游戏内操作执行成功 - By @ConceptualGod")
                    return True
                else:
                    self.logger.warning(f"游戏内操作执行失败 - By @ConceptualGod")
                    return False
            else:
                self.logger.error("游戏内操作控制器未初始化 - By @ConceptualGod")
                return False

        except Exception as e:
            self.logger.error(f"运行游戏内操作异常: {str(e)} - By @ConceptualGod")
            return False

    def _stop_game_starter(self):
        """
        停止游戏启动控制器

        开发者: @ConceptualGod
        """
        try:
            if self.game_starter:
                self.game_starter.stop_game_flow()
                self._log_to_gui("游戏启动控制器已停止 - By @ConceptualGod")
        except Exception as e:
            self.logger.error(f"停止游戏启动控制器失败: {str(e)} - By @ConceptualGod")

    def _stop_task_recognition(self):
        """
        停止任务识别

        开发者: @ConceptualGod
        """
        try:
            if self.task_recognition:
                self.task_recognition.stop_recognition()
                self._log_to_gui("任务识别已停止 - By @ConceptualGod")
        except Exception as e:
            self.logger.error(f"停止任务识别失败: {str(e)} - By @ConceptualGod")

    def _stop_progress_monitor(self):
        """
        停止进度监控

        开发者: @ConceptualGod
        """
        try:
            if self.progress_monitor:
                self.progress_monitor.stop_monitoring()
                self._log_to_gui("进度监控已停止 - By @ConceptualGod")
        except Exception as e:
            self.logger.error(f"停止进度监控失败: {str(e)} - By @ConceptualGod")


def main():
    """主函数"""
    try:
        app = MainWindow()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {str(e)} - By @ConceptualGod")
        messagebox.showerror("错误 - By @ConceptualGod", f"程序启动失败: {str(e)}")


if __name__ == "__main__":
    main()
