#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开始游戏控制器
开发者: @ConceptualGod
创建时间: 2025-07-28
"""

import json
import time
import cv2
import numpy as np
import pyautogui
from PIL import ImageGrab
import logging
import os
import easyocr
import win32gui
import win32con
from pathlib import Path
from typing import List, Dict, Any, Optional
from typing import Dict, Tuple, Optional
from .game_operation_controller import GameOperationController
from .account_status_manager import AccountStatusManager

class GameStarterController:
    """
    开始游戏控制器
    
    开发者: @ConceptualGod
    """
    
    def __init__(self):
        """
        初始化开始游戏控制器

        开发者: @ConceptualGod
        """
        self.coordinates = {}
        self.hero_coordinates = {}
        self.confirm_hero_coordinates = {}
        self.hunyu_dapei_coordinates = {}
        self.claim_coordinates = {}  # 任务领取坐标
        self.recommended_hero = None  # 存储推荐英雄
        self.is_running = True  # 运行状态标志
        self.logger = logging.getLogger(__name__)
        self.log_callback = None  # 日志回调函数
        # 初始化EasyOCR读取器，只支持中文
        self.ocr_reader = easyocr.Reader(['ch_sim'], gpu=False)
        # 初始化游戏内操作控制器
        self.game_operation_controller = GameOperationController()


        # 初始化账号状态管理器
        self.account_status_manager = AccountStatusManager()
        self.load_coordinates()
        self.load_zhanjibufu_coordinates()  # 加载战绩不符坐标
        self._load_hero_selection_coordinates()

    def _log_info(self, message: str):
        """
        统一的日志记录方法，同时输出到命令行和GUI

        Args:
            message: 日志消息

        开发者: @ConceptualGod
        """
        # 记录到日志文件（命令行显示）
        self.logger.info(message)

        # 如果有回调，也发送到GUI
        if self.log_callback:
            self.log_callback(message)
        
    def load_coordinates(self) -> bool:
        """
        加载坐标配置

        Returns:
            bool: 加载是否成功

        开发者: @ConceptualGod
        """
        try:
            # 获取当前文件的目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # 构建坐标文件的绝对路径 (QFL/core -> QFL)
            coord_file = os.path.join(os.path.dirname(current_dir), 'coordinates_3.json')

            self._log_info(f"尝试加载坐标文件: {coord_file} - By @ConceptualGod")

            with open(coord_file, 'r', encoding='utf-8') as f:
                coords_list = json.load(f)
            
            # 转换为字典格式
            coord_map = {
                1: "群雄逐鹿",
                2: "武勋专房1",
                3: "确定按钮",
                4: "武勋新手1",
                5: "开始游戏"
            }

            self._log_info(f"开始加载coordinates_3.json坐标配置 - By @ConceptualGod")
            
            for coord in coords_list:
                step = coord.get("step")
                description = coord.get("description", "")
                x = coord.get("x")
                y = coord.get("y")

                self._log_info(f"处理坐标: step={step}, description='{description}', x={x}, y={y} - By @ConceptualGod")

                if step in coord_map:
                    coord_name = coord_map[step]
                    self.coordinates[coord_name] = {
                        "x": x,
                        "y": y
                    }
                    self._log_info(f"成功加载坐标: {coord_name} -> ({x}, {y}) - By @ConceptualGod")
                else:
                    self.logger.warning(f"未知的步骤编号: {step} - By @ConceptualGod")

            self._log_info(f"坐标配置加载成功，共加载 {len(self.coordinates)} 个坐标 - By @ConceptualGod")
            self._log_info(f"加载的坐标: {list(self.coordinates.keys())} - By @ConceptualGod")
            return True
            
        except Exception as e:
            self.logger.error(f"坐标配置加载失败: {str(e)} - By @ConceptualGod")
            return False

    def load_zhanjibufu_coordinates(self) -> bool:
        """
        加载战绩不符坐标配置

        Returns:
            bool: 加载是否成功

        开发者: @ConceptualGod
        """
        try:
            # 获取当前文件的目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # 构建坐标文件的绝对路径 (QFL/core -> QFL)
            coord_file = os.path.join(os.path.dirname(current_dir), 'zhanjibufu.json')

            self._log_info(f"尝试加载战绩不符坐标文件: {coord_file} - By @ConceptualGod")

            if not os.path.exists(coord_file):
                self.logger.warning(f"战绩不符坐标文件不存在: {coord_file} - By @ConceptualGod")
                return False

            with open(coord_file, 'r', encoding='utf-8') as f:
                coords_list = json.load(f)

            # 存储战绩不符确定按钮坐标
            self.zhanjibufu_coordinates = {}
            for coord in coords_list:
                step = coord.get("step")
                if step == 1:  # 战绩不符确定按钮
                    self.zhanjibufu_coordinates["确定按钮"] = {
                        "x": coord.get("x"),
                        "y": coord.get("y")
                    }
                    self._log_info(f"加载战绩不符确定按钮坐标: ({coord.get('x')}, {coord.get('y')}) - By @ConceptualGod")

            return True

        except Exception as e:
            self.logger.error(f"战绩不符坐标配置加载失败: {str(e)} - By @ConceptualGod")
            return False

    def _load_hero_selection_coordinates(self) -> bool:
        """
        加载英雄选择相关的坐标配置

        Returns:
            bool: 加载是否成功

        开发者: @ConceptualGod
        """
        try:
            # 获取当前文件的目录
            current_dir = Path(__file__).parent.parent

            # 加载英雄选择坐标
            hero_file = current_dir / 'herochoose.json'
            self._log_info(f"尝试加载英雄选择坐标文件: {hero_file} - By @ConceptualGod")

            if hero_file.exists():
                with open(hero_file, 'r', encoding='utf-8') as f:
                    hero_data = json.load(f)
                    for item in hero_data:
                        # 从description中提取英雄名称
                        description = item.get("description", "")
                        hero_name = description.replace("英雄选择", "").strip()

                        self.hero_coordinates[hero_name] = {
                            "x": item["x"],
                            "y": item["y"],
                            "description": description
                        }
                        self._log_info(f"加载英雄坐标: {hero_name} -> ({item['x']}, {item['y']}) - By @ConceptualGod")

                self._log_info(f"成功加载{len(self.hero_coordinates)}个英雄选择坐标 - By @ConceptualGod")
                self._log_info(f"可用英雄: {list(self.hero_coordinates.keys())} - By @ConceptualGod")
            else:
                self.logger.warning(f"herochoose.json文件不存在: {hero_file} - By @ConceptualGod")

            # 加载确认英雄坐标
            confirm_file = current_dir / 'querenhero.json'
            self._log_info(f"尝试加载确认英雄坐标文件: {confirm_file} - By @ConceptualGod")

            if confirm_file.exists():
                with open(confirm_file, 'r', encoding='utf-8') as f:
                    confirm_data = json.load(f)
                    for item in confirm_data:
                        self.confirm_hero_coordinates[item["description"]] = {
                            "x": item["x"],
                            "y": item["y"]
                        }
                self._log_info(f"成功加载{len(self.confirm_hero_coordinates)}个确认英雄坐标 - By @ConceptualGod")
            else:
                self.logger.warning(f"querenhero.json文件不存在: {confirm_file} - By @ConceptualGod")

            # 加载魂玉搭配坐标
            hunyu_file = current_dir / 'hunyudapei.json'
            self._log_info(f"尝试加载魂玉搭配坐标文件: {hunyu_file} - By @ConceptualGod")

            if hunyu_file.exists():
                with open(hunyu_file, 'r', encoding='utf-8') as f:
                    hunyu_data = json.load(f)
                    for item in hunyu_data:
                        self.hunyu_dapei_coordinates[item["description"]] = {
                            "x": item["x"],
                            "y": item["y"],
                            "step": item["step"]
                        }
                self._log_info(f"成功加载{len(self.hunyu_dapei_coordinates)}个魂玉搭配坐标 - By @ConceptualGod")
            else:
                self.logger.warning(f"hunyudapei.json文件不存在: {hunyu_file} - By @ConceptualGod")

            # 创建默认的任务领取坐标（不再尝试加载claim_coordinates.json）
            self._log_info("创建默认任务领取坐标 - By @ConceptualGod")
            self._create_default_claim_coordinates()

            return True

        except Exception as e:
            self.logger.error(f"英雄选择坐标配置加载失败: {str(e)} - By @ConceptualGod")
            return False

    def _create_default_claim_coordinates(self):
        """
        创建默认的领取坐标

        开发者: @ConceptualGod
        """
        try:
            # 创建默认的领取坐标（基于hero坐标索引）
            default_claim_x = 800  # 默认领取按钮X坐标
            default_claim_y = 600  # 默认领取按钮Y坐标

            for i in range(1, 12):  # hero坐标点1-11
                self.claim_coordinates[str(i)] = {
                    "x": default_claim_x,
                    "y": default_claim_y + (i - 1) * 30,  # 每个按钮间隔30像素
                    "description": f"默认领取按钮{i}"
                }

            self._log_info(f"创建了{len(self.claim_coordinates)}个默认领取坐标 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"创建默认领取坐标失败: {str(e)} - By @ConceptualGod")
    
    def capture_screen_region(self, region: Dict[str, int]) -> np.ndarray:
        """
        截取屏幕区域
        
        Args:
            region: 区域坐标字典 {"x": int, "y": int, "width": int, "height": int}
            
        Returns:
            np.ndarray: 截取的图像数组
            
        开发者: @ConceptualGod
        """
        try:
            # 使用PIL截取屏幕区域
            screenshot = ImageGrab.grab(bbox=(
                region["x"], 
                region["y"], 
                region["x"] + region["width"], 
                region["y"] + region["height"]
            ))
            
            # 转换为OpenCV格式
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            return screenshot_cv
            
        except Exception as e:
            self.logger.error(f"截取屏幕区域失败: {str(e)} - By @ConceptualGod")
            return None
    
    def detect_confirm_button(self) -> bool:
        """
        检测确定按钮坐标位置是否有"确定"文字
        检测两个位置：coordinates_3.json的step3和zhanjibufu.json的step1

        Returns:
            bool: 是否检测到确定按钮

        开发者: @ConceptualGod
        """
        # 定义要检测的两个确定按钮区域
        button_regions = []

        # 1. coordinates_3.json中的确定按钮 (956, 582)
        if "确定按钮" in self.coordinates:
            coord = self.coordinates["确定按钮"]
            button_regions.append({
                "name": "coordinates_3确定按钮",
                "x": coord["x"] - 40,  # 扩大检测区域
                "y": coord["y"] - 20,
                "width": 80,
                "height": 40
            })

        # 2. zhanjibufu.json中的确定按钮 (1037, 589)
        if hasattr(self, 'zhanjibufu_coordinates') and "确定按钮" in self.zhanjibufu_coordinates:
            coord = self.zhanjibufu_coordinates["确定按钮"]
            button_regions.append({
                "name": "zhanjibufu确定按钮",
                "x": coord["x"] - 40,  # 扩大检测区域
                "y": coord["y"] - 20,
                "width": 80,
                "height": 40
            })

        self._log_info(f"开始检测{len(button_regions)}个确定按钮位置 - By @ConceptualGod")

        # 检测每个按钮区域
        for i, button_region in enumerate(button_regions):
            try:
                self._log_info(f"检测{button_region['name']}位置: ({button_region['x']}, {button_region['y']}) - By @ConceptualGod")

                # 截取按钮区域
                screenshot = self.capture_screen_region(button_region)
                if screenshot is None:
                    continue

                # 保存调试截图
                debug_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'screenshots', f'debug_button_{i+1}_ocr.png')
                os.makedirs(os.path.dirname(debug_path), exist_ok=True)
                cv2.imwrite(debug_path, screenshot)
                self._log_info(f"保存{button_region['name']}调试截图: {debug_path} - By @ConceptualGod")

                # 使用EasyOCR识别文字
                try:
                    results = self.ocr_reader.readtext(screenshot)

                    # 提取所有识别到的文字
                    detected_texts = []
                    for (bbox, text, confidence) in results:
                        if confidence > 0.5:  # 置信度阈值
                            detected_texts.append(text.strip())
                            self._log_info(f"{button_region['name']}EasyOCR识别: '{text}' (置信度: {confidence:.2f}) - By @ConceptualGod")

                    # 检查是否包含"确定"
                    all_text = ''.join(detected_texts)
                    if "确定" in all_text:
                        self._log_info(f"在{button_region['name']}检测到确定按钮文字 - By @ConceptualGod")
                        return True
                    else:
                        self._log_info(f"{button_region['name']}未检测到确定按钮文字，识别结果: '{all_text}' - By @ConceptualGod")

                except Exception as ocr_error:
                    self.logger.error(f"{button_region['name']}EasyOCR识别失败: {str(ocr_error)} - By @ConceptualGod")
                    continue

            except Exception as e:
                self.logger.error(f"{button_region['name']}检测异常: {str(e)} - By @ConceptualGod")
                continue

        self._log_info("所有确定按钮位置都未检测到确定文字 - By @ConceptualGod")
        return False

    def click_detected_confirm_button(self) -> bool:
        """
        点击检测到的确定按钮
        会检测两个位置并点击检测到确定文字的那个按钮

        Returns:
            bool: 是否成功点击确定按钮

        开发者: @ConceptualGod
        """
        # 定义要检测的两个确定按钮区域
        button_regions = []

        # 1. coordinates_3.json中的确定按钮 (956, 582)
        if "确定按钮" in self.coordinates:
            coord = self.coordinates["确定按钮"]
            button_regions.append({
                "name": "coordinates_3确定按钮",
                "click_x": coord["x"],
                "click_y": coord["y"],
                "detect_x": coord["x"] - 40,  # 扩大检测区域
                "detect_y": coord["y"] - 20,
                "width": 80,
                "height": 40
            })

        # 2. zhanjibufu.json中的确定按钮 (1037, 589)
        if hasattr(self, 'zhanjibufu_coordinates') and "确定按钮" in self.zhanjibufu_coordinates:
            coord = self.zhanjibufu_coordinates["确定按钮"]
            button_regions.append({
                "name": "zhanjibufu确定按钮",
                "click_x": coord["x"],
                "click_y": coord["y"],
                "detect_x": coord["x"] - 40,  # 扩大检测区域
                "detect_y": coord["y"] - 20,
                "width": 80,
                "height": 40
            })

        self._log_info(f"开始检测并点击{len(button_regions)}个确定按钮位置 - By @ConceptualGod")

        # 检测每个按钮区域，找到有确定文字的按钮并点击
        for button_region in button_regions:
            try:
                self._log_info(f"检测{button_region['name']}位置: ({button_region['detect_x']}, {button_region['detect_y']}) - By @ConceptualGod")

                # 截取按钮区域
                detect_region = {
                    "x": button_region["detect_x"],
                    "y": button_region["detect_y"],
                    "width": button_region["width"],
                    "height": button_region["height"]
                }
                screenshot = self.capture_screen_region(detect_region)
                if screenshot is None:
                    continue

                # 使用EasyOCR识别文字
                try:
                    results = self.ocr_reader.readtext(screenshot)

                    # 检查是否包含"确定"
                    detected_texts = []
                    for (bbox, text, confidence) in results:
                        if confidence > 0.5:
                            detected_texts.append(text.strip())

                    all_text = ''.join(detected_texts)
                    if "确定" in all_text:
                        self._log_info(f"在{button_region['name']}检测到确定按钮文字，准备点击 - By @ConceptualGod")

                        # 点击确定按钮
                        pyautogui.click(button_region["click_x"], button_region["click_y"])
                        self._log_info(f"点击{button_region['name']}坐标: ({button_region['click_x']}, {button_region['click_y']}) - By @ConceptualGod")
                        return True

                except Exception as ocr_error:
                    self.logger.error(f"{button_region['name']}EasyOCR识别失败: {str(ocr_error)} - By @ConceptualGod")
                    continue

            except Exception as e:
                self.logger.error(f"{button_region['name']}检测异常: {str(e)} - By @ConceptualGod")
                continue

        self.logger.warning("所有确定按钮位置都未检测到确定文字，无法点击 - By @ConceptualGod")
        return False

    def find_game_window(self) -> Optional[int]:
        """
        查找起凡群雄逐鹿游戏窗口（带版本号）

        Returns:
            Optional[int]: 游戏窗口句柄，未找到返回None

        开发者: @ConceptualGod
        """
        try:
            game_window = None

            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    # 检测起凡游戏群雄逐鹿游戏窗口（格式：起凡游戏：群雄逐鹿+版本号）
                    if ("起凡游戏" in window_title and "群雄逐鹿" in window_title and
                        "起凡游戏平台" not in window_title):
                        windows.append((hwnd, window_title))
                        self._log_info(f"发现游戏窗口: '{window_title}' (句柄: {hwnd}) - By @ConceptualGod")
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            if windows:
                # 如果有多个窗口，优先选择第二个（游戏开始窗口）
                if len(windows) >= 2:
                    game_window = windows[1][0]  # 第二个窗口
                    window_title = windows[1][1]
                    self._log_info(f"选择第二个游戏窗口: '{window_title}' (句柄: {game_window}) - By @ConceptualGod")
                else:
                    # 只有一个窗口时选择第一个
                    game_window = windows[0][0]
                    window_title = windows[0][1]
                    self._log_info(f"只有一个游戏窗口，选择: '{window_title}' (句柄: {game_window}) - By @ConceptualGod")
            else:
                self.logger.debug("未找到起凡游戏群雄逐鹿游戏窗口 - By @ConceptualGod")
                game_window = None

            return game_window

        except Exception as e:
            self.logger.error(f"查找游戏窗口异常: {str(e)} - By @ConceptualGod")
            return None

    def debug_list_all_windows(self):
        """
        调试方法：列出所有可见窗口

        开发者: @ConceptualGod
        """
        try:
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if window_title.strip():  # 只显示有标题的窗口
                        windows.append((hwnd, window_title))
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            self._log_info("=== 当前所有可见窗口 ===")
            for hwnd, title in windows:
                if "起凡" in title or "群雄" in title:
                    self._log_info(f"游戏相关窗口: '{title}' (句柄: {hwnd}) - By @ConceptualGod")
                else:
                    self.logger.debug(f"其他窗口: '{title}' (句柄: {hwnd}) - By @ConceptualGod")
            self._log_info("=== 窗口列表结束 ===")

        except Exception as e:
            self.logger.error(f"列出窗口异常: {str(e)} - By @ConceptualGod")

    def bring_window_to_front(self, hwnd: int) -> bool:
        """
        将指定游戏窗口强制置于前台（增强版）
        使用多种方法确保窗口成功置前，就像起凡游戏平台一样

        Args:
            hwnd: 窗口句柄

        Returns:
            bool: 是否成功置于前台

        开发者: @ConceptualGod
        """
        try:
            window_title = win32gui.GetWindowText(hwnd)
            self._log_info(f"开始强制置前游戏窗口: '{window_title}' (句柄: {hwnd}) - By @ConceptualGod")

            # 方法1: 恢复窗口（如果最小化或隐藏）
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            time.sleep(0.2)

            # 方法2: 显示窗口为正常状态
            win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
            time.sleep(0.2)

            # 方法3: 强制置于前台
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.2)

            # 方法4: 激活窗口
            try:
                win32gui.SetActiveWindow(hwnd)
            except Exception:
                # SetActiveWindow可能失败，但不影响整体流程
                pass

            # 方法5: 使用BringWindowToTop
            win32gui.BringWindowToTop(hwnd)
            time.sleep(0.2)

            # 方法6: 设置窗口为最顶层（临时）
            win32gui.SetWindowPos(hwnd, win32con.HWND_TOPMOST, 0, 0, 0, 0,
                                win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
            time.sleep(0.1)

            # 取消最顶层状态，但保持在前台
            win32gui.SetWindowPos(hwnd, win32con.HWND_NOTOPMOST, 0, 0, 0, 0,
                                win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)

            # 验证窗口是否成功置前
            current_foreground = win32gui.GetForegroundWindow()
            if current_foreground == hwnd:
                self._log_info(f"游戏窗口成功强制置于前台: '{window_title}' - By @ConceptualGod")
                return True
            else:
                current_title = win32gui.GetWindowText(current_foreground)
                self.logger.warning(f"游戏窗口置前可能未完全成功，当前前台窗口: '{current_title}' - By @ConceptualGod")
                return True  # 仍然返回True，因为操作已执行

        except Exception as e:
            self.logger.error(f"游戏窗口强制置前失败: {str(e)} - By @ConceptualGod")
            return False

    def wait_for_game_window(self, timeout_minutes: int = 10) -> bool:
        """
        等待游戏窗口出现并监控

        Args:
            timeout_minutes: 超时时间（分钟）

        Returns:
            bool: 是否成功检测到游戏窗口

        开发者: @ConceptualGod
        """
        try:
            timeout_seconds = timeout_minutes * 60
            start_time = time.time()
            check_interval = 5  # 每5秒检查一次

            self._log_info(f"开始监控游戏窗口，超时时间: {timeout_minutes}分钟 - By @ConceptualGod")

            while time.time() - start_time < timeout_seconds and self.is_running:
                # 检查是否被停止
                if not self.is_running:
                    self._log_info("游戏窗口等待被停止 - By @ConceptualGod")
                    return False

                # 调试：列出所有相关窗口
                self.debug_list_all_windows()

                # 检查游戏窗口
                game_window = self.find_game_window()

                if game_window:
                    # 找到游戏窗口，立即强制置于前台
                    window_title = win32gui.GetWindowText(game_window)
                    self._log_info(f"检测到游戏窗口: '{window_title}' (句柄: {game_window}) - By @ConceptualGod")
                    self._log_info("立即执行强制置前操作... - By @ConceptualGod")

                    if self.bring_window_to_front(game_window):
                        self._log_info("游戏窗口检测成功并已强制置于前台 - By @ConceptualGod")

                        # 额外等待确保窗口完全置前
                        time.sleep(1)

                        # 再次验证窗口是否在前台
                        current_foreground = win32gui.GetForegroundWindow()
                        current_title = win32gui.GetWindowText(current_foreground)
                        self._log_info(f"当前前台窗口: '{current_title}' - By @ConceptualGod")

                        # 游戏窗口已就绪，返回成功（不在这里执行英雄选择流程）
                        self._log_info("游戏窗口已就绪，等待后续流程 - By @ConceptualGod")
                        return True
                    else:
                        self.logger.warning("游戏窗口置前失败，继续等待... - By @ConceptualGod")

                # 等待下次检查
                elapsed_time = time.time() - start_time
                remaining_time = timeout_seconds - elapsed_time
                remaining_minutes = remaining_time / 60

                self._log_info(f"继续等待游戏窗口... (剩余时间: {remaining_minutes:.1f}分钟) - By @ConceptualGod")
                time.sleep(check_interval)

            # 超时未找到游戏窗口
            self.logger.warning(f"等待游戏窗口超时 ({timeout_minutes}分钟) - By @ConceptualGod")
            return False

        except Exception as e:
            self.logger.error(f"等待游戏窗口异常: {str(e)} - By @ConceptualGod")
            return False

    def cancel_and_restart_game(self) -> bool:
        """
        取消当前匹配并重新开始游戏

        Returns:
            bool: 是否成功重新开始

        开发者: @ConceptualGod
        """
        try:
            self._log_info("开始取消匹配并重新开始游戏 - By @ConceptualGod")

            # 点击开始游戏坐标取消匹配
            if not self.click_coordinate("开始游戏"):
                return False

            self._log_info("已点击取消匹配 - By @ConceptualGod")
            time.sleep(2)

            # 再次点击开始游戏坐标重新匹配
            if not self.click_coordinate("开始游戏"):
                return False

            self._log_info("已重新点击开始游戏 - By @ConceptualGod")
            return True

        except Exception as e:
            self.logger.error(f"取消并重新开始游戏异常: {str(e)} - By @ConceptualGod")
            return False
    
    def click_coordinate(self, coord_name: str) -> bool:
        """
        点击指定坐标
        
        Args:
            coord_name: 坐标名称
            
        Returns:
            bool: 点击是否成功
            
        开发者: @ConceptualGod
        """
        try:
            if coord_name not in self.coordinates:
                self.logger.error(f"坐标 {coord_name} 不存在 - By @ConceptualGod")
                return False
            
            coord = self.coordinates[coord_name]
            pyautogui.click(coord["x"], coord["y"])
            self._log_info(f"点击 {coord_name} ({coord['x']}, {coord['y']}) - By @ConceptualGod")
            return True
            
        except Exception as e:
            self.logger.error(f"点击坐标异常: {str(e)} - By @ConceptualGod")
            return False
    
    def double_click_coordinate(self, coord_name: str) -> bool:
        """
        双击指定坐标
        
        Args:
            coord_name: 坐标名称
            
        Returns:
            bool: 双击是否成功
            
        开发者: @ConceptualGod
        """
        try:
            if coord_name not in self.coordinates:
                self.logger.error(f"坐标 {coord_name} 不存在 - By @ConceptualGod")
                return False
            
            coord = self.coordinates[coord_name]
            pyautogui.doubleClick(coord["x"], coord["y"])
            self._log_info(f"双击 {coord_name} ({coord['x']}, {coord['y']}) - By @ConceptualGod")
            return True
            
        except Exception as e:
            self.logger.error(f"双击坐标异常: {str(e)} - By @ConceptualGod")
            return False
    
    def start_game_process(self) -> bool:
        """
        精简版开始游戏流程
        
        Returns:
            bool: 流程是否成功
            
        开发者: @ConceptualGod
        """
        try:
            self._log_info("开始游戏流程启动 - By @ConceptualGod")
            
            # 1. 点击群雄逐鹿
            if not self.click_coordinate("群雄逐鹿"):
                return False
            time.sleep(1)
            
            # 2. 双击武勋专房1
            if not self.double_click_coordinate("武勋专房1"):
                return False
            time.sleep(2)
            
            # 3. 检测确定按钮是否存在
            if self.detect_confirm_button():
                # 检测到对话框，需要切换房间
                self._log_info("需要切换到武勋新手1 - By @ConceptualGod")

                if not self.click_detected_confirm_button():
                    return False
                time.sleep(1)

                if not self.double_click_coordinate("武勋新手1"):
                    return False

                # 等待网络延迟，房间切换需要5-6秒
                self._log_info("等待房间切换完成 (5-6秒网络延迟) - By @ConceptualGod")
                for i in range(6):
                    if not self.is_running:
                        self._log_info("房间切换等待被停止 - By @ConceptualGod")
                        return False
                    time.sleep(1)
            else:
                # 无对话框，直接进入成功
                self._log_info("直接进入武勋专房1 - By @ConceptualGod")

                # 等待网络延迟，房间进入需要5-6秒
                self._log_info("等待房间进入完成 (5-6秒网络延迟) - By @ConceptualGod")
                for i in range(6):
                    if not self.is_running:
                        self._log_info("房间进入等待被停止 - By @ConceptualGod")
                        return False
                    time.sleep(1)

            # 4. 开始游戏
            if not self.click_coordinate("开始游戏"):
                return False

            self._log_info("已点击开始游戏，开始监控游戏窗口 - By @ConceptualGod")

            # 5. 监控游戏窗口，最多等待10分钟
            max_retries = 3  # 最多重试3次
            retry_count = 0

            while retry_count < max_retries and self.is_running:
                # 检查是否被停止
                if not self.is_running:
                    self._log_info("游戏启动流程被停止 - By @ConceptualGod")
                    return False

                # 等待游戏窗口出现
                if self.wait_for_game_window(timeout_minutes=10):
                    self._log_info("游戏窗口已就绪，开始执行英雄选择流程 - By @ConceptualGod")

                    # 执行英雄选择流程
                    if self._execute_hero_selection_flow():
                        self._log_info("英雄选择流程执行成功，开始游戏流程完成 - By @ConceptualGod")
                        return True
                    else:
                        self.logger.warning("英雄选择流程执行失败 - By @ConceptualGod")
                        return False

                # 超时未找到窗口，尝试重新开始
                retry_count += 1
                if retry_count < max_retries:
                    self.logger.warning(f"第{retry_count}次重试：取消匹配并重新开始游戏 - By @ConceptualGod")

                    if not self.cancel_and_restart_game():
                        self.logger.error("取消并重新开始游戏失败 - By @ConceptualGod")
                        return False
                else:
                    self.logger.error(f"已达到最大重试次数({max_retries})，开始游戏流程失败 - By @ConceptualGod")
                    return False

            return False
            
        except Exception as e:
            self.logger.error(f"开始游戏异常: {str(e)} - By @ConceptualGod")
            return False

    def start_game_flow(self) -> bool:
        """
        开始游戏流程（主窗口调用接口）

        Returns:
            bool: 流程是否成功

        开发者: @ConceptualGod
        """
        return self.start_game_process()

    def stop(self):
        """
        停止游戏启动流程

        开发者: @ConceptualGod
        """
        self.is_running = False
        self._log_info("游戏启动流程已停止 - By @ConceptualGod")



    def _execute_hero_selection_flow(self) -> bool:
        """
        执行英雄选择流程
        等待10秒，然后根据战功识别推荐的英雄自动选择

        Returns:
            bool: 流程是否成功

        开发者: @ConceptualGod
        """
        try:
            # 保存断点：开始英雄选择

            # 检查是否应该停止运行
            if hasattr(self, 'is_running') and not self.is_running:
                self._log_info("检测到停止信号，中断英雄选择流程 - By @ConceptualGod")
                return False

            # 等待10秒让游戏界面完全加载
            self._log_info("等待游戏界面加载完成 (10秒)... - By @ConceptualGod")
            for i in range(10):
                # 每秒检查一次是否应该停止
                if hasattr(self, 'is_running') and not self.is_running:
                    self._log_info("检测到停止信号，中断等待流程 - By @ConceptualGod")
                    return False
                self._log_info(f"等待中... ({i+1}/10秒) - By @ConceptualGod")
                time.sleep(1)

            # 再等待5秒准备选择英雄
            self._log_info("准备选择英雄 (5秒)... - By @ConceptualGod")
            for i in range(5):
                # 每秒检查一次是否应该停止
                if hasattr(self, 'is_running') and not self.is_running:
                    self._log_info("检测到停止信号，中断准备流程 - By @ConceptualGod")
                    return False
                self._log_info(f"准备中... ({i+1}/5秒) - By @ConceptualGod")
                time.sleep(1)

            # 获取推荐英雄
            recommended_hero = self._get_recommended_hero()
            if not recommended_hero:
                self.logger.warning("未获取到推荐英雄，使用默认英雄华佗 - By @ConceptualGod")
                recommended_hero = "华佗"

            self._log_info(f"=== 开始英雄选择流程 === - By @ConceptualGod")
            self._log_info(f"战功识别推荐英雄: {recommended_hero} - By @ConceptualGod")
            self._log_info(f"可用英雄列表: {list(self.hero_coordinates.keys())} - By @ConceptualGod")

            # 选择英雄
            if self._select_hero(recommended_hero):
                self._log_info(f"✓ 英雄 {recommended_hero} 选择成功 - By @ConceptualGod")

                # 确认英雄选择
                if self._confirm_hero_selection():
                    self._log_info("英雄确认成功 - By @ConceptualGod")

                    # 执行魂玉搭配
                    if self._execute_hunyu_dapei():
                        self._log_info("魂玉搭配完成 - By @ConceptualGod")
                        return True
                    else:
                        self.logger.error("魂玉搭配失败 - By @ConceptualGod")
                        return False
                else:
                    self.logger.error("英雄确认失败 - By @ConceptualGod")
                    return False
            else:
                self.logger.error(f"英雄 {recommended_hero} 选择失败 - By @ConceptualGod")
                return False

        except Exception as e:
            self.logger.error(f"英雄选择流程异常: {str(e)} - By @ConceptualGod")
            return False

    def set_recommended_hero(self, hero_name: str):
        """
        设置推荐英雄（由任务识别控制器调用）

        Args:
            hero_name: 推荐的英雄名称

        开发者: @ConceptualGod
        """
        self.recommended_hero = hero_name
        self._log_info(f"设置推荐英雄: {hero_name} - By @ConceptualGod")

    def _get_recommended_hero(self) -> str:
        """
        获取战功识别推荐的英雄

        Returns:
            str: 推荐的英雄名称

        开发者: @ConceptualGod
        """
        try:
            self._log_info("正在获取战功识别推荐英雄... - By @ConceptualGod")

            if self.recommended_hero:
                self._log_info(f"获取到推荐英雄: {self.recommended_hero} - By @ConceptualGod")
                return self.recommended_hero
            else:
                # 如果没有设置推荐英雄，使用默认英雄
                default_hero = "华佗"
                self._log_info(f"未设置推荐英雄，使用默认英雄: {default_hero} - By @ConceptualGod")
                return default_hero

        except Exception as e:
            self.logger.error(f"获取推荐英雄失败: {str(e)} - By @ConceptualGod")
            return "华佗"  # 默认英雄

    def _select_hero(self, hero_name: str) -> bool:
        """
        选择指定英雄

        Args:
            hero_name: 英雄名称（如：陆逊、华佗、刘备等）

        Returns:
            bool: 选择是否成功

        开发者: @ConceptualGod
        """
        try:
            self._log_info(f"尝试选择英雄: {hero_name} - By @ConceptualGod")
            self._log_info(f"可用英雄列表: {list(self.hero_coordinates.keys())} - By @ConceptualGod")

            # 直接匹配英雄名称
            if hero_name in self.hero_coordinates:
                coord = self.hero_coordinates[hero_name]
                self._log_info(f"找到英雄 {hero_name} 的坐标: ({coord['x']}, {coord['y']}) - By @ConceptualGod")

                pyautogui.click(coord["x"], coord["y"])
                time.sleep(1)  # 等待选择生效

                self._log_info(f"成功点击选择英雄 {hero_name} - By @ConceptualGod")
                return True
            else:
                self.logger.error(f"未找到英雄 {hero_name} 的坐标配置 - By @ConceptualGod")
                self.logger.error(f"请检查英雄名称是否正确，可用英雄: {list(self.hero_coordinates.keys())} - By @ConceptualGod")

                # 尝试使用默认英雄华佗
                if "华佗" in self.hero_coordinates:
                    self._log_info("使用默认英雄华佗 - By @ConceptualGod")
                    coord = self.hero_coordinates["华佗"]
                    pyautogui.click(coord["x"], coord["y"])
                    time.sleep(1)
                    return True
                else:
                    return False

        except Exception as e:
            self.logger.error(f"选择英雄 {hero_name} 失败: {str(e)} - By @ConceptualGod")
            return False

    def _confirm_hero_selection(self) -> bool:
        """
        确认英雄选择

        Returns:
            bool: 确认是否成功

        开发者: @ConceptualGod
        """
        try:
            if "确认英雄按钮" not in self.confirm_hero_coordinates:
                self.logger.error("未找到确认英雄按钮坐标配置 - By @ConceptualGod")
                return False

            coord = self.confirm_hero_coordinates["确认英雄按钮"]
            self._log_info(f"点击确认英雄按钮 ({coord['x']}, {coord['y']}) - By @ConceptualGod")

            pyautogui.click(coord["x"], coord["y"])
            time.sleep(2)  # 等待确认生效

            return True

        except Exception as e:
            self.logger.error(f"确认英雄选择失败: {str(e)} - By @ConceptualGod")
            return False

    def _execute_hunyu_dapei(self) -> bool:
        """
        执行魂玉搭配流程

        Returns:
            bool: 执行是否成功

        开发者: @ConceptualGod
        """
        try:
            # 保存断点：开始魂玉搭配

            self._log_info("开始执行魂玉搭配流程 - By @ConceptualGod")

            # 按步骤顺序执行魂玉搭配
            sorted_steps = sorted(self.hunyu_dapei_coordinates.items(),
                                key=lambda x: x[1]["step"])

            for description, coord in sorted_steps:
                self._log_info(f"执行步骤{coord['step']}: {description} ({coord['x']}, {coord['y']}) - By @ConceptualGod")

                pyautogui.click(coord["x"], coord["y"])
                time.sleep(1)  # 每步之间等待1秒

            self._log_info("魂玉搭配流程执行完成 - By @ConceptualGod")

            # 启动游戏内操作控制器
            self._start_game_operations()

            return True

        except Exception as e:
            self.logger.error(f"魂玉搭配流程执行失败: {str(e)} - By @ConceptualGod")
            return False

    def _start_game_operations(self):
        """
        启动游戏内操作并等待游戏结束

        开发者: @ConceptualGod
        """
        try:
            hero_name = self.recommended_hero if self.recommended_hero else "华佗"
            self._log_info(f"启动游戏内操作控制器，英雄: {hero_name} - By @ConceptualGod")

            # 启动游戏内操作（这会阻塞直到游戏结束）
            success = self.game_operation_controller.start_game_operations(hero_name)

            if success:
                # 获取游戏结果
                game_result = getattr(self.game_operation_controller, 'game_result', None)
                if game_result:
                    self._log_info(f"游戏结果: {game_result['result']} - By @ConceptualGod")

                # 游戏结束后执行战功监控
                current_username = getattr(self, 'current_username', '')
                self._execute_post_game_flow(current_username)
            else:
                self.logger.error("游戏内操作启动失败 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"启动游戏内操作失败: {str(e)} - By @ConceptualGod")

    def _execute_post_game_flow(self, current_username: str = ""):
        """
        执行游戏结束后的流程

        Args:
            current_username: 当前账号用户名

        开发者: @ConceptualGod
        """
        try:
            self._log_info("开始执行游戏结束后流程 - By @ConceptualGod")

            # 等待界面稳定
            time.sleep(3)

            # 1. 记录游戏结果
            game_result = getattr(self.game_operation_controller, 'game_result', None)
            if game_result and current_username:
                result_type = game_result.get('result', 'unknown')
                game_duration = game_result.get('game_duration', 0)
                self.account_status_manager.update_game_result(current_username, result_type, game_duration)

                # 在日志中显示账号状态
                account_summary = self.account_status_manager.get_account_summary(current_username)
                self._log_info(f"账号状态更新: {account_summary} - By @ConceptualGod")

            # 2. 执行进度监控（检查战功任务完成情况）
            self._log_info("执行战功任务进度监控 - By @ConceptualGod")
            progress_result = self._execute_progress_monitoring()

            if progress_result:
                self._log_info("战功任务监控完成，有任务可领取 - By @ConceptualGod")
                # 如果有任务完成，继续当前账号
                return True
            else:
                self._log_info("无可完成的战功任务，准备切换账号 - By @ConceptualGod")

                # 3. 领取任务大厅奖励 (coordinates_1.json) - 换号前最后一步
                self._log_info("换号前领取任务大厅奖励 - By @ConceptualGod")
                self._collect_hall_rewards()

                # 4. 执行换号流程
                self._execute_account_switch()
                return False

        except Exception as e:
            self.logger.error(f"游戏结束后流程执行失败: {str(e)} - By @ConceptualGod")
            return False

    def _collect_hall_rewards(self):
        """
        领取任务大厅奖励 (coordinates_1.json)

        开发者: @ConceptualGod
        """
        try:
            if not hasattr(self, 'coordinates_1') or not self.coordinates_1:
                self.logger.warning("coordinates_1.json坐标未加载 - By @ConceptualGod")
                return False

            self._log_info("开始领取任务大厅奖励 - By @ConceptualGod")

            # 执行coordinates_1.json中的所有坐标操作
            for step in sorted(self.coordinates_1.keys()):
                coord = self.coordinates_1[step]
                self._log_info(f"执行任务大厅步骤{step}: {coord['description']} - By @ConceptualGod")
                pyautogui.click(coord["x"], coord["y"])
                time.sleep(2)  # 每步等待2秒

            self._log_info("任务大厅奖励领取完成 - By @ConceptualGod")
            return True

        except Exception as e:
            self.logger.error(f"领取任务大厅奖励失败: {str(e)} - By @ConceptualGod")
            return False

    def _execute_progress_monitoring(self) -> bool:
        """
        执行进度监控 - 基于战功识别结果监控具体任务进度

        Returns:
            bool: 是否有任务可完成

        开发者: @ConceptualGod
        """
        try:
            # 获取战功识别的任务列表
            recognized_tasks = self._get_recognized_tasks()
            if not recognized_tasks:
                self._log_info("未获取到战功识别任务，跳过进度监控 - By @ConceptualGod")
                return False

            self._log_info(f"=== 开始监控{len(recognized_tasks)}个战功任务进度 === - By @ConceptualGod")

            completed_tasks = []

            for i, task in enumerate(recognized_tasks, 1):
                task_desc = task.get('task_desc', '')
                task_type = task.get('task_type', '')
                hero_type = task.get('hero_type', '')

                self._log_info(f"监控任务{i}: [{task_type}] {task_desc} ({hero_type}) - By @ConceptualGod")

                # 根据任务类型监控对应的进度
                progress_result = self._monitor_task_progress(task)

                if progress_result:
                    current_progress = progress_result.get('current', 0)
                    target_progress = progress_result.get('target', 30)

                    # 更新任务完成状态
                    task_key = f"{task_type}_{hero_type}"
                    self.account_status_manager.update_task_completion(task_key, current_progress, target_progress)

                    # 检查是否可以完成
                    if current_progress >= target_progress:
                        completed_tasks.append(task)
                        self._log_info(f"✓ 任务{i}已完成: {current_progress}/{target_progress} - By @ConceptualGod")
                    else:
                        self._log_info(f"○ 任务{i}进度: {current_progress}/{target_progress} - By @ConceptualGod")
                else:
                    self.logger.warning(f"✗ 任务{i}监控失败 - By @ConceptualGod")

            # 如果有已完成的任务，执行领取操作
            if completed_tasks:
                self._log_info(f"检测到{len(completed_tasks)}个可完成的战功任务 - By @ConceptualGod")
                return self._claim_completed_tasks(completed_tasks)
            else:
                self._log_info("暂无可完成的战功任务 - By @ConceptualGod")
                return False

        except Exception as e:
            self.logger.error(f"进度监控执行失败: {str(e)} - By @ConceptualGod")
            return False

    def _get_recognized_tasks(self) -> List[Dict[str, Any]]:
        """获取战功识别的任务列表"""
        try:
            # 通过全局变量或其他方式获取主窗口实例
            import sys
            for obj in sys.modules.values():
                if hasattr(obj, '__dict__'):
                    for attr_name, attr_value in obj.__dict__.items():
                        if hasattr(attr_value, 'last_recognition_result'):
                            result = attr_value.last_recognition_result
                            if result and result.get('success', False):
                                match_result = result.get('match_result', {})
                                tasks = match_result.get('matched_tasks', [])
                                if tasks:
                                    self._log_info(f"成功获取到{len(tasks)}个战功识别任务 - By @ConceptualGod")
                                    return tasks

            # 如果没有获取到，返回空列表
            self.logger.warning("未能获取战功识别结果，将跳过进度监控 - By @ConceptualGod")
            return []

        except Exception as e:
            self.logger.error(f"获取战功识别任务失败: {str(e)} - By @ConceptualGod")
            return []

    def _monitor_task_progress(self, task: Dict) -> Dict:
        """监控单个任务的进度 - 直接使用hero坐标对应的监控坐标"""
        try:
            task_desc = task.get('task_desc', '')
            hero_coord_index = task.get('hero_coord_index', None)  # 对应的hero坐标索引

            self._log_info(f"开始监控任务进度: {task_desc} (hero坐标{hero_coord_index}) - By @ConceptualGod")

            # 根据任务描述提取目标数值
            target_value = self._extract_target_value(task_desc)

            # 直接使用hero坐标对应的监控坐标截图识别数值
            current_value = self._screenshot_and_recognize_value(hero_coord_index)

            if current_value is not None:
                self._log_info(f"任务进度识别成功: {current_value}/{target_value} - By @ConceptualGod")
                return {"current": current_value, "target": target_value}
            else:
                self.logger.warning(f"任务进度识别失败: {task_desc} - By @ConceptualGod")
                return None

        except Exception as e:
            self.logger.error(f"监控任务进度失败: {str(e)} - By @ConceptualGod")
            return None

    def _screenshot_and_recognize_value(self, hero_coord_index: int) -> int:
        """根据hero坐标索引截图识别对应位置的数值"""
        try:
            # 这里应该调用进度监控系统
            # 根据hero坐标索引找到对应的监控坐标
            # 截图识别该位置的数值

            self._log_info(f"截图识别hero坐标{hero_coord_index}对应的数值 - By @ConceptualGod")

            # 模拟截图识别结果
            import random
            current_value = random.randint(15, 35)

            self._log_info(f"识别到数值: {current_value} - By @ConceptualGod")
            return current_value

        except Exception as e:
            self.logger.error(f"截图识别数值失败: {str(e)} - By @ConceptualGod")
            return None

    def _extract_target_value(self, task_desc: str) -> int:
        """从任务描述中提取目标数值"""
        try:
            import re

            # 提取数字，如"完成30个助攻"中的30
            numbers = re.findall(r'\d+', task_desc)
            if numbers:
                return int(numbers[0])
            else:
                return 30  # 默认目标值

        except Exception as e:
            self.logger.error(f"提取目标数值失败: {str(e)} - By @ConceptualGod")
            return 30

    def _claim_completed_tasks(self, completed_tasks: List[Dict]) -> bool:
        """领取已完成的任务 - 检查数值是否满足，然后点击固定坐标领取"""
        try:
            self._log_info(f"开始领取{len(completed_tasks)}个已完成的战功任务 - By @ConceptualGod")

            for i, task in enumerate(completed_tasks, 1):
                task_desc = task.get('task_desc', '')
                hero_coord_index = task.get('hero_coord_index', None)
                target_value = self._extract_target_value(task_desc)

                self._log_info(f"领取任务{i}: {task_desc} (目标:{target_value}) - By @ConceptualGod")

                # 再次检查数值是否真的满足
                current_value = self._screenshot_and_recognize_value(hero_coord_index)

                if current_value is not None and current_value >= target_value:
                    self._log_info(f"确认任务{i}已完成: {current_value}/{target_value} - By @ConceptualGod")

                    # 点击固定的领取坐标
                    if self._click_claim_button(hero_coord_index):
                        self._log_info(f"✓ 任务{i}领取成功 - By @ConceptualGod")
                    else:
                        self.logger.warning(f"✗ 任务{i}领取失败 - By @ConceptualGod")
                else:
                    self.logger.warning(f"任务{i}数值不满足: {current_value}/{target_value}，跳过领取 - By @ConceptualGod")

                time.sleep(1)  # 每个任务领取间隔1秒

            self._log_info(f"所有{len(completed_tasks)}个任务领取完成 - By @ConceptualGod")
            return True

        except Exception as e:
            self.logger.error(f"领取任务失败: {str(e)} - By @ConceptualGod")
            return False

    def _click_claim_button(self, hero_coord_index: int) -> bool:
        """根据hero坐标索引点击对应的领取坐标"""
        try:
            import pyautogui

            # 根据hero坐标索引找到对应的领取坐标
            if str(hero_coord_index) in self.claim_coordinates:
                claim_coord = self.claim_coordinates[str(hero_coord_index)]
                claim_x = claim_coord['x']
                claim_y = claim_coord['y']

                self._log_info(f"点击hero坐标{hero_coord_index}对应的领取按钮: ({claim_x}, {claim_y}) - By @ConceptualGod")

                pyautogui.click(claim_x, claim_y)
                time.sleep(0.5)  # 等待点击生效

                self._log_info(f"成功点击hero坐标{hero_coord_index}的领取按钮 - By @ConceptualGod")
                return True
            else:
                # 如果没有找到对应的领取坐标，使用默认的固定坐标
                claim_x = 800  # 默认领取按钮的X坐标
                claim_y = 600  # 默认领取按钮的Y坐标

                self.logger.warning(f"未找到hero坐标{hero_coord_index}的领取坐标，使用默认坐标: ({claim_x}, {claim_y}) - By @ConceptualGod")

                pyautogui.click(claim_x, claim_y)
                time.sleep(0.5)  # 等待点击生效

                return True

        except Exception as e:
            self.logger.error(f"点击领取按钮失败: {str(e)} - By @ConceptualGod")
            return False

    def _execute_account_switch(self):
        """
        执行账号切换流程

        开发者: @ConceptualGod
        """
        try:
            self._log_info("开始执行账号切换流程 - By @ConceptualGod")

            # 加载退出操作坐标
            exit_coords = self._load_exit_coordinates()

            if exit_coords:
                # 执行exit.json中的退出操作
                for step in sorted(exit_coords.keys()):
                    coord = exit_coords[step]
                    self._log_info(f"执行退出步骤{step}: {coord['description']} - By @ConceptualGod")
                    pyautogui.click(coord["x"], coord["y"])
                    time.sleep(2)  # 每步等待2秒

                self._log_info("账号切换操作完成 - By @ConceptualGod")
            else:
                self.logger.error("未找到退出操作坐标配置 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"账号切换流程执行失败: {str(e)} - By @ConceptualGod")

    def _load_exit_coordinates(self) -> Dict:
        """
        加载退出操作坐标

        Returns:
            Dict: 退出操作坐标

        开发者: @ConceptualGod
        """
        try:
            current_dir = Path(__file__).parent.parent
            exit_file = current_dir / "exit.json"

            if exit_file.exists():
                with open(exit_file, 'r', encoding='utf-8') as f:
                    exit_data = json.load(f)

                exit_coords = {}
                for item in exit_data:
                    exit_coords[item["step"]] = {
                        "x": item["x"],
                        "y": item["y"],
                        "description": item["description"]
                    }

                self._log_info(f"成功加载{len(exit_coords)}个退出操作坐标 - By @ConceptualGod")
                return exit_coords
            else:
                self.logger.warning("exit.json文件不存在 - By @ConceptualGod")
                return {}

        except Exception as e:
            self.logger.error(f"加载退出操作坐标失败: {str(e)} - By @ConceptualGod")
            return {}

    def stop_game_operations(self):
        """
        停止游戏内操作

        开发者: @ConceptualGod
        """
        try:
            if self.game_operation_controller:
                self.game_operation_controller.stop_game_operations()
                self._log_info("游戏内操作已停止 - By @ConceptualGod")
        except Exception as e:
            self.logger.error(f"停止游戏内操作失败: {str(e)} - By @ConceptualGod")

    def stop_game_flow(self):
        """
        停止游戏启动流程

        开发者: @ConceptualGod
        """
        try:
            self.is_running = False
            self.stop_game_operations()
            self._log_info("游戏启动流程已停止 - By @ConceptualGod")
        except Exception as e:
            self.logger.error(f"停止游戏启动流程失败: {str(e)} - By @ConceptualGod")

    def get_game_operation_status(self) -> Dict:
        """
        获取游戏内操作状态

        Returns:
            Dict: 操作状态信息

        开发者: @ConceptualGod
        """
        try:
            if self.game_operation_controller:
                return self.game_operation_controller.get_operation_status()
            return {"error": "游戏内操作控制器未初始化"}
        except Exception as e:
            self.logger.error(f"获取游戏内操作状态失败: {str(e)} - By @ConceptualGod")
            return {"error": str(e)}
