#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模式管理器
负责游戏模式的切换和管理

开发者: @ConceptualGod
创建时间: 2025-08-05
"""

import time
import logging
from typing import Dict, Any
from enum import Enum

class GameMode(Enum):
    """游戏模式枚举"""
    DEVELOPMENT = "发育模式"
    FOLLOW = "跟随模式"
    BATTLE = "战斗模式"
    RETREAT = "撤退模式"

class ModeManager:
    """
    模式管理器
    
    功能包括：
    - 游戏模式切换管理
    - 模式状态维护
    - 切换条件检测
    
    开发者: @ConceptualGod
    """
    
    def __init__(self, game_params: Dict):
        """
        初始化模式管理器
        
        Args:
            game_params: 游戏参数配置
            
        开发者: @ConceptualGod
        """
        self.game_params = game_params
        self.logger = logging.getLogger(__name__)
        
        # 模式状态
        self.current_mode = GameMode.DEVELOPMENT
        self.previous_mode = GameMode.DEVELOPMENT
        self.mode_start_time = time.time()
        self.game_start_time = time.time()
        
        # 模式切换条件
        self.mode_switch_time = game_params.get("mode_switch_time", 20) * 60  # 转换为秒
        self.enemy_threshold = game_params.get("enemy_threshold", 3)
        
        self.logger.info("模式管理器初始化完成 - By @ConceptualGod")
    
    def update_mode(self, game_state: Dict[str, Any]) -> GameMode:
        """
        根据游戏状态更新模式
        
        Args:
            game_state: 游戏状态信息
            
        Returns:
            GameMode: 当前游戏模式
            
        开发者: @ConceptualGod
        """
        try:
            old_mode = self.current_mode
            
            # 检查是否需要撤退
            if self._should_retreat(game_state):
                self._switch_mode(GameMode.RETREAT)
            else:
                # 根据游戏时间和状态决定模式
                game_time = time.time() - self.game_start_time
                
                if game_time < self.mode_switch_time:
                    # 前20分钟：发育模式或战斗模式
                    self._update_early_game_mode(game_state)
                else:
                    # 20分钟后：跟随模式或战斗模式
                    self._update_late_game_mode(game_state)
            
            # 记录模式切换
            if old_mode != self.current_mode:
                self.logger.info(f"模式切换: {old_mode.value} -> {self.current_mode.value} - By @ConceptualGod")
            
            return self.current_mode
            
        except Exception as e:
            self.logger.error(f"更新模式失败: {str(e)} - By @ConceptualGod")
            return self.current_mode
    
    def _should_retreat(self, game_state: Dict[str, Any]) -> bool:
        """
        检查是否应该撤退
        
        Args:
            game_state: 游戏状态信息
            
        Returns:
            bool: 是否应该撤退
            
        开发者: @ConceptualGod
        """
        try:
            blood_percentage = game_state.get("blood_percentage", 100)
            mana_percentage = game_state.get("mana_percentage", 100)
            
            low_blood_threshold = self.game_params.get("low_blood_threshold", 40)
            mana_threshold = self.game_params.get("mana_threshold", 10)
            
            # 血量或蓝量过低需要撤退
            if blood_percentage < low_blood_threshold or mana_percentage < mana_threshold:
                return True
            
            # 敌人数量过多且我方劣势时撤退
            enemies_nearby = game_state.get("enemies_nearby", 0)
            allies_nearby = game_state.get("allies_nearby", 0)
            
            if enemies_nearby >= self.enemy_threshold and enemies_nearby > allies_nearby + 1:
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查撤退条件失败: {str(e)} - By @ConceptualGod")
            return False
    
    def _update_early_game_mode(self, game_state: Dict[str, Any]):
        """
        更新前期游戏模式（前20分钟）
        
        Args:
            game_state: 游戏状态信息
            
        开发者: @ConceptualGod
        """
        try:
            enemies_nearby = game_state.get("enemies_nearby", 0)
            allies_nearby = game_state.get("allies_nearby", 0)
            
            # 有敌人且我方不劣势时进入战斗模式
            if enemies_nearby > 0 and allies_nearby >= enemies_nearby:
                self._switch_mode(GameMode.BATTLE)
            else:
                # 否则保持发育模式
                self._switch_mode(GameMode.DEVELOPMENT)
                
        except Exception as e:
            self.logger.error(f"更新前期模式失败: {str(e)} - By @ConceptualGod")
    
    def _update_late_game_mode(self, game_state: Dict[str, Any]):
        """
        更新后期游戏模式（20分钟后）
        
        Args:
            game_state: 游戏状态信息
            
        开发者: @ConceptualGod
        """
        try:
            enemies_nearby = game_state.get("enemies_nearby", 0)
            
            # 有敌人时进入战斗模式
            if enemies_nearby > 0:
                self._switch_mode(GameMode.BATTLE)
            else:
                # 否则进入跟随模式
                self._switch_mode(GameMode.FOLLOW)
                
        except Exception as e:
            self.logger.error(f"更新后期模式失败: {str(e)} - By @ConceptualGod")
    
    def _switch_mode(self, new_mode: GameMode):
        """
        切换模式
        
        Args:
            new_mode: 新模式
            
        开发者: @ConceptualGod
        """
        if self.current_mode != new_mode:
            self.previous_mode = self.current_mode
            self.current_mode = new_mode
            self.mode_start_time = time.time()
    
    def get_mode_duration(self) -> float:
        """
        获取当前模式持续时间
        
        Returns:
            float: 持续时间（秒）
            
        开发者: @ConceptualGod
        """
        return time.time() - self.mode_start_time
    
    def get_game_time(self) -> float:
        """
        获取游戏时间
        
        Returns:
            float: 游戏时间（秒）
            
        开发者: @ConceptualGod
        """
        return time.time() - self.game_start_time
    
    def get_game_time_minutes(self) -> float:
        """
        获取游戏时间（分钟）
        
        Returns:
            float: 游戏时间（分钟）
            
        开发者: @ConceptualGod
        """
        return self.get_game_time() / 60
    
    def is_early_game(self) -> bool:
        """
        是否为前期游戏
        
        Returns:
            bool: 是否为前期
            
        开发者: @ConceptualGod
        """
        return self.get_game_time() < self.mode_switch_time
    
    def is_late_game(self) -> bool:
        """
        是否为后期游戏
        
        Returns:
            bool: 是否为后期
            
        开发者: @ConceptualGod
        """
        return self.get_game_time() >= self.mode_switch_time
    
    def should_enter_tower_range(self) -> bool:
        """
        是否可以进入防御塔范围
        
        Returns:
            bool: 是否可以进入
            
        开发者: @ConceptualGod
        """
        # 跟随模式可以进入防御塔
        if self.current_mode == GameMode.FOLLOW:
            return True
        
        # 战斗模式20分钟后可以进入敌方防御塔
        if self.current_mode == GameMode.BATTLE and self.is_late_game():
            return True
        
        return False
    
    def get_mode_strategy(self) -> Dict[str, Any]:
        """
        获取当前模式的策略配置
        
        Returns:
            Dict: 策略配置
            
        开发者: @ConceptualGod
        """
        strategies = {
            GameMode.DEVELOPMENT: {
                "priority": "safety",
                "follow_distance": self.game_params.get("follow_distance", 300),
                "retreat_threshold": self.enemy_threshold,
                "can_enter_tower": False,
                "skill_usage": "conservative"
            },
            GameMode.FOLLOW: {
                "priority": "follow",
                "follow_distance": self.game_params.get("follow_distance", 300),
                "retreat_threshold": self.enemy_threshold + 1,
                "can_enter_tower": True,
                "skill_usage": "aggressive"
            },
            GameMode.BATTLE: {
                "priority": "combat",
                "follow_distance": self.game_params.get("battle_range", 600),
                "retreat_threshold": self.enemy_threshold + 2,
                "can_enter_tower": self.should_enter_tower_range(),
                "skill_usage": "aggressive"
            },
            GameMode.RETREAT: {
                "priority": "survival",
                "follow_distance": self.game_params.get("retreat_distance", 1500),
                "retreat_threshold": 1,
                "can_enter_tower": False,
                "skill_usage": "defensive"
            }
        }
        
        return strategies.get(self.current_mode, strategies[GameMode.DEVELOPMENT])
    
    def reset_game_timer(self):
        """
        重置游戏计时器（新游戏开始时调用）
        
        开发者: @ConceptualGod
        """
        self.game_start_time = time.time()
        self.mode_start_time = time.time()
        self.current_mode = GameMode.DEVELOPMENT
        self.previous_mode = GameMode.DEVELOPMENT
        
        self.logger.info("游戏计时器已重置 - By @ConceptualGod")
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取模式管理器状态
        
        Returns:
            Dict: 状态信息
            
        开发者: @ConceptualGod
        """
        return {
            "current_mode": self.current_mode.value,
            "previous_mode": self.previous_mode.value,
            "mode_duration": self.get_mode_duration(),
            "game_time": self.get_game_time(),
            "game_time_minutes": self.get_game_time_minutes(),
            "is_early_game": self.is_early_game(),
            "is_late_game": self.is_late_game(),
            "can_enter_tower": self.should_enter_tower_range(),
            "strategy": self.get_mode_strategy()
        }
