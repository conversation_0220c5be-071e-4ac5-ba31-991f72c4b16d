# 起凡游戏自动化脚本系统完整流程步骤详解

**开发者:** @ConceptualGod  
**版本:** v2.0 Final Complete System  
**完成时间:** 2025-08-05  
**项目:** 起凡游戏自动化脚本

## 📋 系统概述

起凡游戏自动化脚本是一个完整的多账号轮登、战功任务识别、游戏内操作、进度监控的全流程自动化系统。

### 🏗️ 系统架构

```
起凡自动化脚本系统
├── 程序入口层
│   ├── gui_main.py - 主程序启动入口
│   └── install.py - 依赖检查和环境安装
├── GUI界面层 (gui/)
│   ├── main_window.py - 主窗口统一界面
│   ├── account_manager_gui.py - 账号管理界面
│   ├── login_controller.py - 登录控制界面
│   └── 其他专用GUI模块
├── 核心控制器层 (core/)
│   ├── 任务识别系统
│   │   ├── task_recognition_controller.py - 任务识别主控制器
│   │   ├── task_matcher.py - 任务匹配器
│   │   ├── task_data_manager.py - 任务数据管理器
│   │   └── ocr_processor.py - OCR处理器
│   ├── 进度监控系统
│   │   ├── progress_monitor_controller.py - 进度监控主控制器
│   │   ├── progress_data_manager.py - 进度数据管理器
│   │   └── auto_reward_collector.py - 自动奖励领取器
│   ├── 游戏启动系统
│   │   ├── game_starter_controller.py - 游戏启动主控制器
│   │   └── game_state_detector.py - 游戏状态检测器
│   ├── 游戏内操作系统
│   │   ├── game_operation_controller.py - 游戏内操作主控制器
│   │   ├── hero_operator.py - 英雄操作器
│   │   ├── mode_manager.py - 模式管理器
│   │   ├── decision_engine.py - 决策引擎
│   │   └── game_end_detector.py - 游戏结束检测器
│   └── 账号管理系统
│       ├── account_manager.py - 账号管理器
│       └── account_status_manager.py - 账号状态管理器
├── 配置文件系统
│   ├── 基础坐标配置 (9个文件)
│   ├── 游戏内操作配置 (6个文件)
│   ├── 任务识别配置 (5个文件)
│   └── 账号数据配置 (1个文件)
└── 工具模块 (utils/)
    ├── logger.py - 日志系统
    ├── config_loader.py - 配置加载器
    └── path_utils.py - 路径工具
```

## 🚀 完整流程步骤详解

### 阶段一：程序启动与环境初始化

#### 步骤1：程序启动 (gui_main.py)
```python
# 1.1 执行环境检查
run_install()  # 调用install.py

# 1.2 设置Python路径和工作目录
sys.path.insert(0, str(current_dir))
os.chdir(current_dir)

# 1.3 启动主GUI界面
from gui.main_window import main as gui_main
gui_main()
```

#### 步骤2：依赖检查与安装 (install.py)
```python
# 2.1 检查requirements.txt中的依赖包
required_packages = [
    'tkinter', 'opencv-python', 'pillow', 'pyautogui', 
    'easyocr', 'pywin32', 'numpy', 'pandas', 'openpyxl'
]

# 2.2 自动安装缺失的包
for package in missing_packages:
    subprocess.run([sys.executable, '-m', 'pip', 'install', package])

# 2.3 创建必要的目录结构
os.makedirs('logs', exist_ok=True)
os.makedirs('screenshots', exist_ok=True)
os.makedirs('data', exist_ok=True)

# 2.4 验证配置文件完整性
validate_config_files()
```

#### 步骤3：主窗口初始化 (main_window.py)
```python
# 3.1 创建主窗口
self.root = tk.Tk()
self.root.title("起凡自动化脚本 By @ConceptualGod")
self.root.geometry("1000x700")

# 3.2 初始化核心控制器
self.progress_monitor = ProgressMonitorController()
self.task_recognition = TaskRecognitionController()
self.game_starter = GameStarterController()

# 3.3 创建选项卡界面
self.notebook = ttk.Notebook(main_frame)
- 账号管理选项卡
- 登录控制选项卡

# 3.4 建立控制器间的回调关系
self._setup_integration_callbacks()
```

### 阶段二：核心控制器初始化

#### 步骤4：进度监控控制器初始化
```python
# 4.1 加载监控坐标配置
zhangongpick.json - 11个监控点坐标
zhangongtaskpick.json - 确定按钮坐标

# 4.2 初始化进度数据管理器
self.progress_data_manager = ProgressDataManager()

# 4.3 初始化自动奖励领取器
self.auto_reward_collector = AutoRewardCollector()

# 4.4 配置EasyOCR引擎
self.ocr_reader = easyocr.Reader(['ch_sim'], gpu=False)
```

#### 步骤5：任务识别控制器初始化
```python
# 5.1 加载扫描区域配置
task.json - 任务区域坐标 (408,612,564,272)

# 5.2 加载任务坐标配置
zhangonghero.json - 11个任务坐标点

# 5.3 加载任务数据定义
zhangong.json - 战功任务数据库

# 5.4 初始化OCR处理器和任务匹配器
self.ocr_processor = OCRProcessor()
self.task_matcher = TaskMatcher()
```

#### 步骤6：游戏启动控制器初始化
```python
# 6.1 加载游戏启动坐标
coordinates_3.json - 5个游戏启动步骤坐标

# 6.2 加载英雄选择坐标
herochoose.json - 6个英雄选择坐标
querenhero.json - 确认英雄坐标

# 6.3 加载魂玉搭配坐标
hunyudapei.json - 11个魂玉搭配步骤坐标

# 6.4 加载任务领取坐标
claim_coordinates.json - 任务领取按钮坐标
```

### 阶段三：用户操作与账号管理

#### 步骤7：账号管理操作
```python
# 7.1 导入账号数据
支持格式: CSV, Excel (.xlsx, .xls)
数据结构: 用户名, 密码, 备注

# 7.2 账号数据存储
保存到: data/accounts.json
格式: {"username": "用户名", "password": "密码", "note": "备注"}

# 7.3 账号状态管理
胜利次数, 失败次数, 逃跑次数, 最近游戏结果
```

#### 步骤8：登录控制设置
```python
# 8.1 选择登录模式
单号登录: 选择单个账号进行操作
多号轮登: 批量选择多个账号轮换

# 8.2 设置轮登参数
账号间隔时间: 默认5秒
失败重试次数: 默认3次
自动换号条件: 任务完成或失败
```

### 阶段四：战功任务识别流程

#### 步骤9：任务识别执行
```python
# 9.1 截图任务区域
region = {"x": 408, "y": 612, "width": 564, "height": 272}
screenshot = capture_screen_region(region)

# 9.2 OCR文本识别
results = self.ocr_reader.readtext(screenshot)
detected_texts = [text for (bbox, text, confidence) in results if confidence > 0.5]

# 9.3 任务文本匹配
for text in detected_texts:
    matched_task = self.task_matcher.match_task(text)
    if matched_task:
        tasks.append(matched_task)

# 9.4 英雄坐标匹配
for task in tasks:
    closest_hero = find_closest_hero_coordinate(task)
    task['hero_coord_index'] = closest_hero['step']
```

#### 步骤10：智能英雄推荐
```python
# 10.1 英雄兼容性分析
hero_task_compatibility = {}
for task in matched_tasks:
    hero_type = task.get("hero_type", "")
    recommended_hero = task.get("recommended_hero", "")
    hero_task_compatibility[recommended_hero].append(task)

# 10.2 综合评分计算
for hero in all_heroes:
    task_count_score = len(compatible_tasks) * 10
    diversity_score = len(task_types) * 5
    coverage_score = calculate_coverage_score(hero_types)
    priority_score = calculate_priority_score(task_types)
    total_score = task_count_score + diversity_score + coverage_score + priority_score

# 10.3 最优英雄选择
optimal_hero = max(hero_scores.items(), key=lambda x: x[1])[0]
```

### 阶段五：游戏启动流程

#### 步骤11：游戏启动操作
```python
# 11.1 点击群雄逐鹿
click_coordinate("群雄逐鹿")  # coordinates_3.json step 1

# 11.2 双击武勋专房1
double_click_coordinate("武勋专房1")  # coordinates_3.json step 2

# 11.3 检测确定按钮
if detect_confirm_button():
    click_coordinate("确定按钮")  # coordinates_3.json step 3
    double_click_coordinate("武勋新手1")  # coordinates_3.json step 4

# 11.4 开始游戏
click_coordinate("开始游戏")  # coordinates_3.json step 5

# 11.5 监控游戏窗口
wait_for_game_window(timeout_minutes=10)
```

#### 步骤12：英雄选择流程
```python
# 12.1 等待游戏界面加载
time.sleep(10)  # 等待10秒

# 12.2 获取推荐英雄
recommended_hero = self._get_recommended_hero()

# 12.3 选择英雄
if recommended_hero in self.hero_coordinates:
    coord = self.hero_coordinates[recommended_hero]
    pyautogui.click(coord["x"], coord["y"])

# 12.4 确认英雄选择
coord = self.confirm_hero_coordinates["确认英雄按钮"]
pyautogui.click(coord["x"], coord["y"])

# 12.5 魂玉搭配
execute_hunyu_dapei()  # hunyudapei.json 11个步骤
```

### 阶段六：游戏内操作流程

#### 步骤13：游戏内操作初始化
```python
# 13.1 加载游戏内配置
jinnang.json - 7个锦囊处理步骤
chuzhuang.json - 20个出装步骤
jiadianshengmingzhi.json - 5个加点步骤
game_params.json - 游戏参数配置
hero_skills.json - 英雄技能配置
hotkeys.json - 快捷键配置

# 13.2 初始化核心模块
self.hero_operator = HeroOperator()
self.mode_manager = ModeManager()
self.decision_engine = DecisionEngine()
self.game_end_detector = GameEndDetector()
```

#### 步骤14：游戏内主循环
```python
while self.is_running:
    # 14.1 检测游戏是否结束
    game_end_result = self.game_end_detector.detect_game_end()
    if game_end_result["game_ended"]:
        self._handle_game_end(game_end_result)
        break

    # 14.2 获取游戏状态
    game_state = self._get_current_game_state()

    # 14.3 更新游戏模式
    current_mode = self.mode_manager.update_mode(game_state)

    # 14.4 决策引擎制定决策
    decision = self.decision_engine.make_decision(game_state, current_mode)

    # 14.5 执行决策
    if decision:
        self._execute_decision(decision, game_state)
```

### 阶段七：游戏结束后流程

#### 步骤15：游戏结束检测
```python
# 15.1 检测游戏结束标志
victory_keywords = ["胜利", "Victory", "获胜"]
defeat_keywords = ["失败", "Defeat", "战败"]

# 15.2 记录游戏结果
game_result = {
    "result": "胜利" or "失败",
    "duration": game_duration,
    "hero": current_hero
}

# 15.3 更新账号状态
self.account_status_manager.update_game_result(username, game_result)
```

#### 步骤16：战功任务进度监控
```python
# 16.1 获取识别的任务列表
recognized_tasks = self._get_recognized_tasks()

# 16.2 监控每个任务进度
for task in recognized_tasks:
    hero_coord_index = task.get('hero_coord_index')
    target_value = self._extract_target_value(task_desc)
    
    # 16.3 截图识别当前进度
    current_value = self._screenshot_and_recognize_value(hero_coord_index)
    
    # 16.4 检查是否完成
    if current_value >= target_value:
        completed_tasks.append(task)
```

#### 步骤17：任务领取流程
```python
# 17.1 验证任务完成
for task in completed_tasks:
    current_value = self._screenshot_and_recognize_value(hero_coord_index)
    if current_value >= target_value:
        # 17.2 点击领取坐标
        claim_coord = self.claim_coordinates[str(hero_coord_index)]
        pyautogui.click(claim_coord['x'], claim_coord['y'])
        time.sleep(0.5)
```

#### 步骤18：账号切换流程
```python
# 18.1 领取任务大厅奖励
execute_coordinate_file("coordinates_1.json")  # 6个步骤

# 18.2 执行退出操作
execute_coordinate_file("exit.json")  # 2个步骤

# 18.3 切换到下一个账号
next_account = get_next_account()
if next_account:
    login_with_account(next_account)
```

## 📊 配置文件系统详解

### 基础坐标配置 (9个文件)
```
login.json (3个坐标) - 登录操作
coordinates_1.json (6个坐标) - 任务大厅操作
coordinates_2.json (4个坐标) - 战功操作  
coordinates_3.json (5个坐标) - 游戏启动操作
close.json (1个坐标) - 界面关闭
exit.json (2个坐标) - 退出操作
herochoose.json (6个坐标) - 英雄选择
querenhero.json (1个坐标) - 确认英雄
hunyudapei.json (11个坐标) - 魂玉搭配
```

### 游戏内操作配置 (6个文件)
```
jinnang.json (7个坐标) - 锦囊处理
chuzhuang.json (20个坐标) - 出装逻辑
jiadianshengmingzhi.json (5个坐标) - 加点系统
game_params.json - 游戏参数配置
hero_skills.json - 英雄技能配置
hotkeys.json - 快捷键配置
```

### 任务识别配置 (5个文件)
```
task.json - 扫描区域配置
zhangonghero.json (11个配置项) - 任务坐标配置
zhangong.json (1个配置项) - 任务数据定义
zhangongpick.json (11个配置项) - 进度监控坐标
zhangongtaskpick.json (1个配置项) - 确定按钮坐标
```

### 账号数据配置 (1个文件)
```
data/accounts.json - 账号数据存储
```

## 🎯 数据流转机制

```
账号导入 → 登录控制 → 任务识别 → 英雄推荐 → 游戏启动 →
英雄选择 → 游戏内操作 → 游戏结束检测 → 状态更新 → 
进度监控 → 任务领取 → 账号切换 → 循环执行
```

## ✅ 系统特性

### 完整自动化
- ✅ **全流程自动化** - 从登录到游戏结束的完整自动化
- ✅ **多账号轮登** - 支持批量账号自动轮换
- ✅ **智能任务识别** - 基于OCR的智能任务识别
- ✅ **智能英雄推荐** - 基于任务需求的智能英雄推荐

### 可靠稳定
- ✅ **异常处理** - 完善的异常处理和错误恢复
- ✅ **日志记录** - 详细的操作日志记录
- ✅ **状态监控** - 实时的游戏状态监控
- ✅ **配置驱动** - 基于配置文件的灵活配置

### 用户友好
- ✅ **GUI界面** - 直观的图形用户界面
- ✅ **实时反馈** - 实时的操作状态反馈
- ✅ **简单操作** - 简单的一键式操作
- ✅ **详细文档** - 完整的使用文档

## 🔧 技术实现细节

### 关键技术栈
```
GUI框架: tkinter + ttk
图像处理: OpenCV + PIL
OCR识别: EasyOCR
自动化操作: pyautogui
数据处理: pandas + openpyxl
日志系统: logging
配置管理: JSON
```

### 核心算法

#### 任务匹配算法
```python
def match_task(self, text: str) -> Dict:
    """任务文本匹配算法"""
    # 1. 文本预处理
    cleaned_text = self._clean_text(text)

    # 2. 关键词匹配
    for task_type, keywords in self.task_keywords.items():
        if any(keyword in cleaned_text for keyword in keywords):
            # 3. 数值提取
            numbers = re.findall(r'\d+', cleaned_text)
            target_value = int(numbers[0]) if numbers else 30

            # 4. 英雄类型识别
            hero_type = self._identify_hero_type(cleaned_text)

            return {
                "task_desc": cleaned_text,
                "task_type": task_type,
                "hero_type": hero_type,
                "target_value": target_value
            }
```

#### 英雄推荐算法
```python
def _analyze_optimal_hero_combination(self, tasks: List[Dict]) -> str:
    """智能英雄推荐算法"""
    hero_scores = {}

    for hero in all_heroes:
        # 任务数量得分 (10分/任务)
        task_count_score = len(compatible_tasks) * 10

        # 任务类型多样性得分 (5分/类型)
        diversity_score = len(task_types) * 5

        # 英雄类型覆盖得分
        coverage_score = 0
        if "任意英雄" in hero_types:
            coverage_score += 15
        if len(hero_types) > 1:
            coverage_score += 10

        # 任务优先级得分
        priority_score = sum(task_priority.get(task_type, 1) for task_type in task_types)

        total_score = task_count_score + diversity_score + coverage_score + priority_score
        hero_scores[hero] = total_score

    return max(hero_scores.items(), key=lambda x: x[1])[0]
```

#### 游戏状态检测算法
```python
def detect_game_end(self) -> Dict:
    """游戏结束检测算法"""
    # 1. 截图游戏界面
    screenshot = self._capture_game_screen()

    # 2. OCR识别文本
    texts = self._ocr_recognize(screenshot)

    # 3. 关键词匹配
    victory_keywords = ["胜利", "Victory", "获胜", "胜"]
    defeat_keywords = ["失败", "Defeat", "战败", "败"]

    for text in texts:
        if any(keyword in text for keyword in victory_keywords):
            return {"game_ended": True, "result": "胜利"}
        elif any(keyword in text for keyword in defeat_keywords):
            return {"game_ended": True, "result": "失败"}

    return {"game_ended": False, "result": None}
```

### 坐标系统设计

#### 坐标文件格式标准
```json
{
  "step": 1,
  "x": 675,
  "y": 223,
  "description": "刘备英雄选择",
  "timestamp": "03:40:11",
  "status": "未执行"
}
```

#### 坐标映射机制
```python
# hero坐标索引 → 监控坐标 → 领取坐标
coordinate_mapping = {
    "1": {
        "hero": {"x": 675, "y": 223},
        "monitor": {"x": 500, "y": 200, "width": 50, "height": 20},
        "claim": {"x": 800, "y": 600}
    }
}
```

## 🎮 游戏内操作详解

### 英雄操作系统
```python
class HeroOperator:
    """英雄操作器"""

    def release_skill(self, skill_key: str):
        """释放技能"""
        pyautogui.press(skill_key)

    def use_item(self, item_slot: int):
        """使用物品"""
        pyautogui.press(str(item_slot))

    def move_to_position(self, x: int, y: int):
        """移动到指定位置"""
        pyautogui.rightClick(x, y)
```

### 决策引擎系统
```python
class DecisionEngine:
    """决策引擎"""

    def make_decision(self, game_state: Dict, mode: str) -> Dict:
        """制定游戏决策"""
        if mode == "发育模式":
            return self._farming_decision(game_state)
        elif mode == "战斗模式":
            return self._combat_decision(game_state)
        elif mode == "跟随模式":
            return self._follow_decision(game_state)
```

### 模式管理系统
```python
class ModeManager:
    """模式管理器"""

    def update_mode(self, game_state: Dict) -> str:
        """更新游戏模式"""
        game_time = game_state.get("game_time", 0)

        if game_time < 300:  # 前5分钟
            return "发育模式"
        elif game_time < 900:  # 5-15分钟
            return "跟随模式"
        else:  # 15分钟后
            return "战斗模式"
```

## 📊 数据管理系统

### 账号状态管理
```python
class AccountStatusManager:
    """账号状态管理器"""

    def update_game_result(self, username: str, result: Dict):
        """更新游戏结果"""
        if username not in self.account_stats:
            self.account_stats[username] = {
                "wins": 0, "losses": 0, "escapes": 0,
                "last_result": "", "total_games": 0
            }

        stats = self.account_stats[username]
        if result["result"] == "胜利":
            stats["wins"] += 1
        elif result["result"] == "失败":
            stats["losses"] += 1

        stats["last_result"] = result["result"]
        stats["total_games"] += 1
```

### 任务数据管理
```python
class TaskDataManager:
    """任务数据管理器"""

    def load_zhangong_data(self):
        """加载战功数据"""
        with open('zhangong.json', 'r', encoding='utf-8') as f:
            self.zhangong_data = json.load(f)

        # 解析5种目标任务类型
        self.target_tasks = {
            "助攻类": self._filter_tasks_by_type("助攻"),
            "牺牲值类": self._filter_tasks_by_type("牺牲"),
            "MVP类": self._filter_tasks_by_type("MVP"),
            "胜利类": self._filter_tasks_by_type("胜利"),
            "完整局类": self._filter_tasks_by_type("完整")
        }
```

## 🔍 调试与监控

### 日志系统
```python
# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/automation_{date}.log'),
        logging.StreamHandler()
    ]
)

# 日志使用示例
self.logger.info("开始执行任务识别 - By @ConceptualGod")
self.logger.warning("未找到匹配的任务 - By @ConceptualGod")
self.logger.error("任务识别失败 - By @ConceptualGod")
```

### 截图调试
```python
def save_debug_screenshot(self, image: np.ndarray, name: str):
    """保存调试截图"""
    debug_path = f'screenshots/debug_{name}_{timestamp}.png'
    cv2.imwrite(debug_path, image)
    self.logger.info(f"保存调试截图: {debug_path}")
```

### 性能监控
```python
def monitor_performance(self):
    """性能监控"""
    start_time = time.time()
    # 执行操作
    end_time = time.time()
    duration = end_time - start_time
    self.logger.info(f"操作耗时: {duration:.2f}秒")
```

## 🛠️ 维护与扩展

### 配置文件维护
```python
def validate_config_files(self):
    """验证配置文件完整性"""
    required_files = [
        'login.json', 'coordinates_1.json', 'coordinates_2.json',
        'coordinates_3.json', 'herochoose.json', 'task.json'
    ]

    for file_path in required_files:
        if not os.path.exists(file_path):
            self.logger.error(f"配置文件缺失: {file_path}")
            return False

    return True
```

### 系统扩展接口
```python
class ExtensionInterface:
    """系统扩展接口"""

    def register_task_type(self, task_type: str, matcher: callable):
        """注册新的任务类型"""
        self.task_matchers[task_type] = matcher

    def register_hero(self, hero_name: str, config: Dict):
        """注册新的英雄配置"""
        self.hero_configs[hero_name] = config

    def register_coordinate_set(self, name: str, coordinates: List[Dict]):
        """注册新的坐标集合"""
        self.coordinate_sets[name] = coordinates
```

## 📋 使用指南

### 首次使用步骤
1. **运行gui_main.py启动程序**
2. **导入账号数据** (CSV/Excel格式)
3. **配置登录参数** (间隔时间、重试次数)
4. **执行任务识别** 验证识别效果
5. **启动多号轮登** 开始自动化流程

### 日常维护
1. **检查日志文件** 查看运行状态
2. **更新坐标配置** 适应游戏界面变化
3. **备份账号数据** 定期备份重要数据
4. **清理截图文件** 清理调试截图

### 故障排除
1. **OCR识别失败** → 检查截图区域和OCR配置
2. **坐标点击失败** → 验证坐标配置准确性
3. **游戏窗口检测失败** → 确认游戏窗口标题
4. **账号登录失败** → 检查账号数据和网络连接

---

**系统完成:** 2025-08-05
**开发者:** @ConceptualGod
**状态:** 完整实现，全流程自动化
**特点:** 智能、可靠、完整、易用
**技术栈:** Python + tkinter + OpenCV + EasyOCR + pyautogui
