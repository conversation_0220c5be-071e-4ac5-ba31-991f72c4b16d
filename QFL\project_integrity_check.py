#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目完整性检查脚本
开发者: @ConceptualGod
"""

import sys
import os
import json
from pathlib import Path

def check_project_integrity():
    """
    检查项目完整性
    开发者: @ConceptualGod
    """
    print("=" * 60)
    print("起凡自动化脚本项目完整性检查 - By @ConceptualGod")
    print("=" * 60)
    
    current_dir = Path(__file__).parent
    project_root = current_dir.parent
    
    # 检查结果统计
    total_checks = 0
    passed_checks = 0
    
    # 1. 检查核心Python文件
    print("\n1. 检查核心Python文件...")
    core_files = [
        "QFL/gui_main.py",
        "QFL/gui/main_window.py",
        "QFL/gui/login_controller.py",
        "QFL/gui/account_manager_gui.py",
        "QFL/core/progress_monitor_controller.py",
        "QFL/core/task_recognition_controller.py",
        "QFL/core/game_starter_controller.py",
        "QFL/core/game_operation_controller.py",
        "QFL/core/game_state_detector.py",
        "QFL/install.py"
    ]
    
    for file_path in core_files:
        total_checks += 1
        full_path = project_root / file_path
        if full_path.exists():
            print(f"[通过] {file_path} - By @ConceptualGod")
            passed_checks += 1
        else:
            print(f"[失败] {file_path} - 文件不存在 - By @ConceptualGod")
    
    # 2. 检查坐标配置文件
    print("\n2. 检查坐标配置文件...")
    coordinate_files = [
        "QFL/login.json",
        "QFL/coordinates_1.json",
        "QFL/coordinates_2.json",
        "QFL/coordinates_3.json",
        "QFL/close.json",
        "QFL/exit.json",
        "QFL/herochoose.json",
        "QFL/querenhero.json",
        "QFL/hunyudapei.json",
        "QFL/jinnang.json",
        "QFL/chuzhuang.json",
        "QFL/jiadianshengmingzhi.json"
    ]
    
    for file_path in coordinate_files:
        total_checks += 1
        full_path = project_root / file_path
        if full_path.exists():
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print(f"[通过] {file_path} - {len(data)}个坐标 - By @ConceptualGod")
                    passed_checks += 1
            except Exception as e:
                print(f"[失败] {file_path} - JSON格式错误: {e} - By @ConceptualGod")
        else:
            print(f"[失败] {file_path} - 文件不存在 - By @ConceptualGod")
    
    # 3. 检查任务识别配置文件
    print("\n3. 检查任务识别配置文件...")
    task_files = [
        "QFL/task.json",
        "QFL/zhangonghero.json",
        "QFL/zhangong.json",
        "QFL/zhangongpick.json",
        "QFL/zhangongtaskpick.json",
        "QFL/game_params.json",
        "QFL/hero_skills.json",
        "QFL/hotkeys.json"
    ]
    
    for file_path in task_files:
        total_checks += 1
        full_path = project_root / file_path
        if full_path.exists():
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        print(f"[通过] {file_path} - {len(data)}个配置项 - By @ConceptualGod")
                    else:
                        print(f"[通过] {file_path} - 配置文件正常 - By @ConceptualGod")
                    passed_checks += 1
            except Exception as e:
                print(f"[失败] {file_path} - JSON格式错误: {e} - By @ConceptualGod")
        else:
            print(f"[失败] {file_path} - 文件不存在 - By @ConceptualGod")
    
    # 4. 检查数据目录
    print("\n4. 检查数据目录...")
    data_files = [
        "QFL/data/accounts.json"
    ]
    
    for file_path in data_files:
        total_checks += 1
        full_path = project_root / file_path
        if full_path.exists():
            print(f"[通过] {file_path} - By @ConceptualGod")
            passed_checks += 1
        else:
            print(f"[失败] {file_path} - 文件不存在 - By @ConceptualGod")
    
    # 5. 检查工具模块
    print("\n5. 检查工具模块...")
    util_files = [
        "QFL/utils/logger.py",
        "QFL/utils/config_loader.py",
        "QFL/utils/path_utils.py"
    ]
    
    for file_path in util_files:
        total_checks += 1
        full_path = project_root / file_path
        if full_path.exists():
            print(f"[通过] {file_path} - By @ConceptualGod")
            passed_checks += 1
        else:
            print(f"[失败] {file_path} - 文件不存在 - By @ConceptualGod")
    
    # 6. 检查文档文件
    print("\n6. 检查文档文件...")
    doc_files = [
        "docs/完整项目步骤流程.md",
        "docs/集成功能实现说明.md",
        "docs/代码开发规范文档.md",
        "docs/起凡游戏自动化说明.md"
    ]
    
    for file_path in doc_files:
        total_checks += 1
        full_path = project_root / file_path
        if full_path.exists():
            print(f"[通过] {file_path} - By @ConceptualGod")
            passed_checks += 1
        else:
            print(f"[失败] {file_path} - 文件不存在 - By @ConceptualGod")
    
    # 7. 检查依赖文件
    print("\n7. 检查依赖文件...")
    dep_files = [
        "QFL/requirements.txt"
    ]
    
    for file_path in dep_files:
        total_checks += 1
        full_path = project_root / file_path
        if full_path.exists():
            print(f"[通过] {file_path} - By @ConceptualGod")
            passed_checks += 1
        else:
            print(f"[失败] {file_path} - 文件不存在 - By @ConceptualGod")
    
    # 8. 功能模块导入测试
    print("\n8. 功能模块导入测试...")
    sys.path.insert(0, str(project_root / "QFL"))
    
    modules_to_test = [
        ("gui.main_window", "MainWindow"),
        ("core.progress_monitor_controller", "ProgressMonitorController"),
        ("core.task_recognition_controller", "TaskRecognitionController"),
        ("core.game_starter_controller", "GameStarterController"),
        ("utils.logger", "setup_logger")
    ]
    
    for module_name, class_name in modules_to_test:
        total_checks += 1
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"[通过] {module_name}.{class_name} - 导入成功 - By @ConceptualGod")
            passed_checks += 1
        except Exception as e:
            print(f"[失败] {module_name}.{class_name} - 导入失败: {e} - By @ConceptualGod")
    
    # 9. 检查坐标数据完整性
    print("\n9. 检查坐标数据完整性...")
    
    # 检查英雄选择坐标
    total_checks += 1
    hero_file = project_root / "QFL/herochoose.json"
    if hero_file.exists():
        try:
            with open(hero_file, 'r', encoding='utf-8') as f:
                hero_data = json.load(f)
                hero_names = [item.get("description", "").replace("英雄选择", "").strip() 
                             for item in hero_data]
                expected_heroes = ["刘备", "华佗", "陆逊", "诸葛瑾", "孙权", "曹操"]
                if all(hero in hero_names for hero in expected_heroes):
                    print(f"[通过] 英雄选择坐标完整 - {len(hero_data)}个英雄 - By @ConceptualGod")
                    passed_checks += 1
                else:
                    print(f"[失败] 英雄选择坐标不完整 - 缺少必要英雄 - By @ConceptualGod")
        except Exception as e:
            print(f"[失败] 英雄选择坐标检查失败: {e} - By @ConceptualGod")
    else:
        print("[失败] herochoose.json文件不存在 - By @ConceptualGod")
    
    # 检查魂玉搭配步骤
    total_checks += 1
    hunyu_file = project_root / "QFL/hunyudapei.json"
    if hunyu_file.exists():
        try:
            with open(hunyu_file, 'r', encoding='utf-8') as f:
                hunyu_data = json.load(f)
                if len(hunyu_data) == 11:
                    print(f"[通过] 魂玉搭配步骤完整 - {len(hunyu_data)}个步骤 - By @ConceptualGod")
                    passed_checks += 1
                else:
                    print(f"[失败] 魂玉搭配步骤不完整 - 应为11步，实际{len(hunyu_data)}步 - By @ConceptualGod")
        except Exception as e:
            print(f"[失败] 魂玉搭配步骤检查失败: {e} - By @ConceptualGod")
    else:
        print("[失败] hunyudapei.json文件不存在 - By @ConceptualGod")
    
    # 10. 总结报告
    print("\n" + "=" * 60)
    print("项目完整性检查报告 - By @ConceptualGod")
    print("=" * 60)
    print(f"总检查项目: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"失败检查: {total_checks - passed_checks} - By @ConceptualGod")
    print(f"完整性: {passed_checks/total_checks*100:.1f}% - By @ConceptualGod")

    if passed_checks == total_checks:
        print("\n[通过] 项目完整性检查全部通过 - By @ConceptualGod")
        print("[通过] 所有核心文件和配置都已就绪 - By @ConceptualGod")
        print("[通过] 项目可以正常运行 - By @ConceptualGod")
    else:
        print(f"\n[失败] 项目完整性检查发现 {total_checks - passed_checks} 个问题 - By @ConceptualGod")
        print("[失败] 请检查缺失的文件和配置 - By @ConceptualGod")
        print("[失败] 建议修复问题后重新检查 - By @ConceptualGod")
    
    print("=" * 60)
    
    return passed_checks == total_checks

if __name__ == "__main__":
    success = check_project_integrity()
    input("按回车键退出...")
    sys.exit(0 if success else 1)
