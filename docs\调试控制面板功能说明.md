# 调试控制面板功能说明

**开发者:** @ConceptualGod  
**版本:** v2.0 Final  
**添加时间:** 2025-08-05  
**项目:** 起凡游戏自动化脚本

## 📋 调试面板概述

为登录控制器添加了完整的调试控制面板，包含16个调试按钮，可以单独测试每个步骤，也可以联动测试，避免卡死，方便您调试和修正。

## 🎛️ 调试面板布局

### 界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 多号轮登控制                                                │
├─────────────────────────────────────────────────────────────┤
│ [开始多号轮登] [停止轮登] [紧急停止] [清空日志]              │
├─────────────────────────────────────────────────────────────┤
│ 调试控制面板                                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 第一行：基础步骤调试                                    │ │
│ │ [测试窗口检测] [测试登录操作] [测试任务大厅] [测试界面关闭] │ │
│ │                                                         │ │
│ │ 第二行：核心功能调试                                    │ │
│ │ [测试战功操作] [测试任务识别] [测试游戏启动] [测试进度监控] │ │
│ │                                                         │ │
│ │ 第三行：游戏内步骤调试                                  │ │
│ │ [测试英雄选择] [测试魂玉搭配] [测试游戏内操作] [测试游戏结束] │ │
│ │                                                         │ │
│ │ 第四行：综合测试                                        │ │
│ │ [测试退出操作] [测试完整流程] [测试单个账号] [停止调试]   │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 调试功能详解

### 第一行：基础步骤调试

#### **1. 测试窗口检测**
- **功能**: 测试游戏窗口检测和置前功能
- **步骤**: 
  - 检测游戏窗口是否存在
  - 尝试将游戏窗口置于前台
  - 显示检测结果
- **用途**: 确保自动化能正确找到和操作游戏窗口

#### **2. 测试登录操作**
- **功能**: 测试login.json坐标操作
- **步骤**:
  - 使用第一个账号进行测试
  - 执行登录坐标点击（不输入账密）
  - 验证坐标操作是否正确
- **用途**: 调试登录界面的坐标准确性

#### **3. 测试任务大厅**
- **功能**: 测试coordinates_1.json操作
- **步骤**:
  - 执行任务大厅的6个坐标操作
  - 包括每日奖励、每周奖励、挑战模式等
- **用途**: 调试任务大厅操作的坐标准确性

#### **4. 测试界面关闭**
- **功能**: 测试close.json操作
- **步骤**:
  - 执行关闭欢迎窗口操作
- **用途**: 调试界面关闭操作的坐标准确性

### 第二行：核心功能调试

#### **5. 测试战功操作**
- **功能**: 测试coordinates_2.json操作
- **步骤**:
  - 执行战功界面的4个坐标操作
  - 包括点击战功、切换期数、选择第四期、开启战功
- **用途**: 调试战功设置操作的坐标准确性

#### **6. 测试任务识别**
- **功能**: 测试战功任务识别功能
- **步骤**:
  - 调用任务识别回调
  - 显示推荐的英雄
- **用途**: 调试EasyOCR识别和英雄推荐逻辑

#### **7. 测试游戏启动**
- **功能**: 测试coordinates_3.json和游戏启动回调
- **步骤**:
  - 执行游戏启动坐标操作
  - 测试游戏启动回调（使用测试英雄）
- **用途**: 调试游戏启动流程

#### **8. 测试进度监控**
- **功能**: 测试战功任务进度监控
- **步骤**:
  - 调用进度监控回调
  - 显示是否检测到已完成任务
- **用途**: 调试战功任务监控和OCR识别

### 第三行：游戏内步骤调试

#### **9. 测试英雄选择**
- **功能**: 测试英雄选择和确认流程
- **步骤**:
  - 等待游戏界面加载（调试模式：3秒）
  - 执行herochoose.json操作
  - 执行querenhero.json确认操作
- **用途**: 调试英雄选择的坐标和流程

#### **10. 测试魂玉搭配**
- **功能**: 测试魂玉搭配流程
- **步骤**:
  - 执行hunyudapei.json的11个步骤
  - 包括生命、防御、法术、冷却、套装选择
- **用途**: 调试魂玉搭配的坐标准确性

#### **11. 测试游戏内操作**
- **功能**: 测试游戏内的各种操作
- **步骤**:
  - 初始操作：购买速度之靴、1级加点
  - 锦囊处理：军机锦囊、白色锦囊
  - 装备购买：回城、购买装备
  - 升级加点：15级、25级加点
- **用途**: 调试游戏内操作的坐标和逻辑

#### **12. 测试游戏结束**
- **功能**: 测试游戏结束检测流程
- **步骤**:
  - 模拟游戏结束检测
  - 模拟OCR识别胜利/失败
  - 模拟记录游戏结果
- **用途**: 调试游戏结束检测逻辑

### 第四行：综合测试

#### **13. 测试退出操作**
- **功能**: 测试exit.json退出操作
- **步骤**:
  - 执行退出和切换账号操作
- **用途**: 调试账号切换的坐标准确性

#### **14. 测试完整流程**
- **功能**: 测试游戏启动后的完整流程（重点）
- **步骤**:
  - 快速执行前置操作（步骤1-8）
  - 详细测试任务识别（步骤9）
  - 详细测试游戏启动坐标（步骤10）
  - **重点测试游戏启动后的步骤**：
    - 游戏窗口监控（模拟）
    - 英雄选择流程
    - 魂玉搭配流程
    - 游戏内操作（模拟30分钟）
    - 游戏结束检测
    - 战功任务监控
    - 换号判断
- **用途**: 完整测试游戏启动后的所有步骤

#### **15. 测试单个账号**
- **功能**: 测试单个账号的完整流程（包含游戏内操作）
- **步骤**:
  - 使用第一个账号进行完整测试
  - 包含所有步骤的实际执行
- **用途**: 真实环境下的完整流程测试

#### **16. 停止调试**
- **功能**: 停止所有调试操作
- **步骤**:
  - 停止运行状态
  - 调用紧急停止
  - 清理调试标志
- **用途**: 紧急停止所有调试操作

## 🛡️ 防卡死机制

### 调试模式标志
```python
# 设置调试标志，避免卡死
self.debug_mode = True
```

### 防卡死措施
1. **时间缩短**: 调试模式下等待时间大幅缩短
2. **模拟执行**: 游戏内操作使用模拟而非实际执行
3. **跳过实际游戏**: 避免启动实际游戏导致长时间等待
4. **异常处理**: 每个调试方法都有完整的异常处理
5. **状态检查**: 检查回调是否存在再调用

### 调试模式下的特殊处理
```python
# 调试模式下跳过实际游戏启动，避免卡死
if hasattr(self, 'debug_mode') and self.debug_mode:
    self._log_status("调试模式：跳过实际游戏启动")
    time.sleep(2)  # 模拟游戏启动时间
    return True
```

## 📊 调试日志输出

### 日志格式
```
=== 开始调试XXX === - By @ConceptualGod
步骤1：XXX操作 - By @ConceptualGod
✓ XXX操作执行成功 - By @ConceptualGod
✗ XXX操作执行失败 - By @ConceptualGod
=== XXX调试完成 === - By @ConceptualGod
```

### 详细信息显示
- **操作步骤**: 每个步骤都有详细说明
- **执行结果**: 成功/失败状态清晰显示
- **错误信息**: 异常情况详细记录
- **推荐英雄**: 任务识别结果显示
- **模拟操作**: 标明哪些是模拟执行

## 🎯 使用建议

### 调试顺序建议
1. **基础测试**: 先测试窗口检测、登录操作等基础功能
2. **坐标验证**: 逐个测试各个坐标文件的准确性
3. **功能测试**: 测试任务识别、进度监控等核心功能
4. **游戏内测试**: 重点测试英雄选择、魂玉搭配等游戏内步骤
5. **完整流程**: 最后测试完整流程和单个账号

### 问题排查建议
1. **坐标问题**: 如果某个操作失败，检查对应的JSON坐标文件
2. **回调问题**: 如果功能测试失败，检查主窗口的回调设置
3. **时序问题**: 如果操作过快，可以增加等待时间
4. **窗口问题**: 确保游戏窗口在正确位置和状态

### 安全使用
1. **避免重复**: 不要同时运行多个调试操作
2. **及时停止**: 发现问题及时点击"停止调试"
3. **备份数据**: 调试前备份重要的配置文件
4. **分步测试**: 先测试单个功能，再测试完整流程

---

**功能完成:** 2025-08-05  
**开发者:** @ConceptualGod  
**状态:** 调试面板完整，功能齐全  
**特点:** 单独调试、联动测试、防卡死、详细日志
