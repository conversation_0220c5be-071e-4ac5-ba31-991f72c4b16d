# -*- coding: utf-8 -*-
"""
日志监控模块 - OCR识别的备用方案
开发者: @ConceptualGod
版本: v1.0
创建时间: 2025-08-05
项目: 起凡自动化脚本

功能:
- 监控4个核心日志文件作为OCR的备用方案
- 提供战功任务进度数据
- 检测游戏结束状态
- 监控装备购买和性能状态
"""

import os
import re
import logging
from datetime import datetime
from typing import Dict, Optional

class LogMonitor:
    """日志监控器 - OCR识别的备用方案"""
    
    def __init__(self, game_path="7fgame"):
        """初始化日志监控器"""
        self.logger = logging.getLogger(__name__)
        self.game_path = game_path
        self.log_path = os.path.join(game_path, "GameLog")
        self.today = datetime.now().strftime("%Y.%m.%d")
        
        # 核心数据
        self.task_data = {
            'assists': 0,           # [22] 助攻数
            'mvp_score': 0,         # [29] MVP分数
            'sacrifice_value': 0,   # [74] 牺牲值 (转换后的K值)
            'gold': 0,              # [11] 金币数量
            'hero_level': 0,        # [16] 英雄等级
            'game_ended': False,    # 游戏是否结束
            'is_victory': False,    # 是否胜利
            'equipment_count': 0,   # 装备购买次数
            'fps': 0,               # 帧率
            'ping': 0               # 延迟
        }
        
        self.logger.info("日志监控器初始化完成")
    
    def get_latest_session_path(self) -> Optional[str]:
        """获取最新游戏会话路径"""
        if not os.path.exists(self.log_path):
            return None
        
        # 获取今天的文件夹
        folders = [f for f in os.listdir(self.log_path) 
                  if os.path.isdir(os.path.join(self.log_path, f)) and self.today in f]
        
        if not folders:
            return None
        
        # 返回最新的会话路径
        latest_folder = sorted(folders)[-1]
        return os.path.join(self.log_path, latest_folder)
    
    def extract_number(self, line: str) -> int:
        """从日志行中提取数字"""
        match = re.search(r'=\s*(-?\d+)', line)
        return int(match.group(1)) if match else 0
    
    def parse_score_log(self, session_path: str) -> bool:
        """解析scoreLOG.log获取战功任务数据"""
        score_file = os.path.join(session_path, "scoreLOG.log")
        if not os.path.exists(score_file):
            return False
        
        try:
            with open(score_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # 从最后100行中提取数据
            for line in lines[-100:]:
                if "Escape_temp_tab[22]=" in line:  # 助攻数
                    self.task_data['assists'] = self.extract_number(line)
                elif "Escape_temp_tab[29]=" in line:  # MVP分数
                    self.task_data['mvp_score'] = self.extract_number(line)
                elif "Escape_temp_tab[74]=" in line:  # 牺牲值
                    damage_value = self.extract_number(line)
                    # 转换为K单位 (需要根据实际测试调整转换公式)
                    self.task_data['sacrifice_value'] = damage_value // 10000
                elif "Escape_temp_tab[11]=" in line:  # 金币
                    self.task_data['gold'] = self.extract_number(line)
                elif "Escape_temp_tab[16]=" in line:  # 英雄等级
                    self.task_data['hero_level'] = self.extract_number(line)
            
            return True
            
        except Exception as e:
            self.logger.error(f"解析scoreLOG.log失败: {e}")
            return False
    
    def parse_end_log(self, session_path: str) -> bool:
        """解析end.log获取游戏结束状态"""
        end_file = os.path.join(session_path, "end.log")
        if not os.path.exists(end_file):
            return False
        
        try:
            with open(end_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # 检查游戏结果
            for line in lines[-10:]:
                if "查询结果" in line:
                    result = self.extract_number(line)
                    self.task_data['game_ended'] = True
                    self.task_data['is_victory'] = (result == 5)
                    break
            
            return True
            
        except Exception as e:
            self.logger.error(f"解析end.log失败: {e}")
            return False
    
    def parse_buy_item_log(self, session_path: str) -> bool:
        """解析buyItemCmd.log获取装备购买数据"""
        buy_file = os.path.join(session_path, "buyItemCmd.log")
        if not os.path.exists(buy_file):
            return False
        
        try:
            with open(buy_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            self.task_data['equipment_count'] = len(lines)
            return True
            
        except Exception as e:
            self.logger.error(f"解析buyItemCmd.log失败: {e}")
            return False
    
    def parse_performance_log(self, session_path: str) -> bool:
        """解析Perf-fps.log获取性能数据"""
        perf_file = os.path.join(session_path, "Perf-fps.log")
        if not os.path.exists(perf_file):
            return False
        
        try:
            with open(perf_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # 从最后几行提取性能数据
            for line in lines[-10:]:
                if "[FPS," in line:
                    fps_match = re.search(r'\[FPS,([0-9.]+)', line)
                    if fps_match:
                        self.task_data['fps'] = int(float(fps_match.group(1)))
                elif "[Ping," in line:
                    ping_match = re.search(r'\[Ping,([0-9.]+)', line)
                    if ping_match:
                        self.task_data['ping'] = int(float(ping_match.group(1)))
            
            return True
            
        except Exception as e:
            self.logger.error(f"解析Perf-fps.log失败: {e}")
            return False
    
    def update_data(self) -> bool:
        """更新所有数据"""
        session_path = self.get_latest_session_path()
        if not session_path:
            self.logger.warning("未找到游戏会话路径")
            return False
        
        # 解析4个核心日志文件
        success = True
        success &= self.parse_score_log(session_path)
        success &= self.parse_end_log(session_path)
        success &= self.parse_buy_item_log(session_path)
        success &= self.parse_performance_log(session_path)
        
        if success:
            self.logger.info("日志数据更新成功")
        else:
            self.logger.warning("部分日志数据更新失败")
        
        return success
    
    def get_task_progress(self) -> Dict:
        """获取战功任务进度 - 替代OCR识别"""
        return {
            'assists': self.task_data['assists'],
            'mvp_score': self.task_data['mvp_score'],
            'sacrifice_value': self.task_data['sacrifice_value'],
            'gold': self.task_data['gold'],
            'hero_level': self.task_data['hero_level']
        }
    
    def get_game_status(self) -> Dict:
        """获取游戏状态 - 替代OCR识别"""
        return {
            'game_ended': self.task_data['game_ended'],
            'is_victory': self.task_data['is_victory'],
            'equipment_count': self.task_data['equipment_count'],
            'fps': self.task_data['fps'],
            'ping': self.task_data['ping']
        }
    
    def check_task_completion(self, task_type: str, target_value: int) -> bool:
        """检查任务完成状态"""
        if task_type == 'assists':
            return self.task_data['assists'] >= target_value
        elif task_type == 'mvp':
            return self.task_data['mvp_score'] >= target_value
        elif task_type == 'sacrifice':
            return self.task_data['sacrifice_value'] >= target_value
        elif task_type == 'victory':
            return self.task_data['is_victory']
        elif task_type == 'complete_game':
            return self.task_data['game_ended']
        else:
            return False
    
    def is_available(self) -> bool:
        """检查日志监控是否可用"""
        return self.get_latest_session_path() is not None
