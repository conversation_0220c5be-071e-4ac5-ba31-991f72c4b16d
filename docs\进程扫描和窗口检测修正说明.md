# 进程扫描和窗口检测修正说明

**开发者:** @ConceptualGod  
**版本:** v2.0 Final Enhanced  
**修正时间:** 2025-08-05  
**项目:** 起凡游戏自动化脚本

## 🔍 问题分析

根据您的反馈，窗口检测无法识别游戏进程，主要问题是：

1. **窗口检测过于局限** - 只查找包含"起凡游戏平台"的窗口
2. **没有进程扫描** - 没有主动扫描游戏相关进程
3. **关键词不全面** - 可能游戏窗口标题与预期不符

## 🛠️ 修正措施

### 1. 增强窗口检测逻辑

#### **修正前（局限性）**：
```python
def detect_game_window(self) -> bool:
    # 只查找包含"起凡游戏平台"的窗口
    if "起凡游戏平台" in window_title:
        windows.append((hwnd, window_title))
```

#### **修正后（全面检测）**：
```python
def detect_game_window(self) -> bool:
    # 方法1：进程扫描 + 窗口查找
    game_processes = self._scan_game_processes()
    
    # 方法2：扩展关键词窗口搜索
    game_keywords = [
        "起凡游戏平台", "起凡", "QiFan", "qifan", 
        "群雄逐鹿", "三国争霸", "发布时间",
        "登录", "Login", "游戏大厅"
    ]
```

### 2. 新增进程扫描功能

#### **扫描游戏进程**：
```python
def _scan_game_processes(self):
    # 可能的游戏进程名
    game_process_names = [
        "qifan.exe", "QiFan.exe", "起凡.exe",
        "game.exe", "client.exe", "launcher.exe",
        "qfgame.exe", "qf.exe", "起凡游戏.exe"
    ]
    
    # 扫描所有进程
    for proc in psutil.process_iter(['pid', 'name']):
        if any(game_name.lower() in process_name.lower() 
               for game_name in game_process_names):
            game_processes.append((process_name, pid))
```

#### **根据进程查找窗口**：
```python
def _find_windows_by_process(self, pid):
    # 根据进程ID查找对应的窗口
    _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
    if window_pid == pid:
        windows.append((hwnd, window_title))
```

### 3. 智能窗口选择

#### **优先级排序**：
```python
def _select_best_game_window(self, windows):
    # 优先级排序：越靠前优先级越高
    priority_keywords = [
        "起凡游戏平台",    # 最高优先级
        "群雄逐鹿", 
        "三国争霸",
        "起凡",
        "QiFan",
        "游戏大厅",
        "登录"            # 最低优先级
    ]
```

### 4. 改进登录状态判断

#### **多特征判断**：
```python
def _determine_login_status(self):
    # 未登录状态特征
    logout_keywords = ["发布时间", "起凡游戏平台", "登录", "Login", "游戏大厅"]
    
    # 已登录状态特征
    login_keywords = ["群雄逐鹿", "三国争霸", "房间", "大厅"]
    
    # 优先检查已登录特征，再检查未登录特征
```

### 5. 新增调试功能

#### **扫描进程按钮**：
在调试面板第一行添加了"扫描进程"按钮，可以：
- 扫描所有运行中的进程
- 过滤游戏相关进程
- 扫描所有可见窗口
- 过滤游戏相关窗口
- 测试当前窗口检测逻辑

## 🔧 使用方法

### 1. 立即诊断
**点击"扫描进程"按钮**，系统会显示：

#### **进程扫描结果**：
```
--- 扫描所有运行中的进程 ---
找到 156 个进程，其中 3 个可能是游戏相关进程
可能的游戏进程:
  - qifan.exe (PID: 1234)
  - game.exe (PID: 5678)
  - client.exe (PID: 9012)
```

#### **窗口扫描结果**：
```
--- 扫描所有可见窗口 ---
找到 45 个可见窗口，其中 2 个可能是游戏相关窗口
可能的游戏窗口:
  - '起凡游戏平台 - 发布时间: 2024-12-01' (句柄: 123456)
  - '群雄逐鹿 - 房间大厅' (句柄: 789012)
```

#### **当前检测测试**：
```
--- 测试当前窗口检测逻辑 ---
✓ 当前窗口检测成功
  窗口标题: '起凡游戏平台 - 发布时间: 2024-12-01'
  窗口句柄: 123456
  登录状态: False
```

### 2. 根据结果调整

#### **如果找到游戏进程但窗口检测失败**：
- 说明进程存在但窗口标题不匹配
- 需要根据实际窗口标题调整关键词

#### **如果找到游戏窗口但登录状态错误**：
- 说明登录状态判断逻辑需要调整
- 需要根据实际窗口标题调整判断条件

#### **如果都没找到**：
- 确认游戏是否已启动
- 检查游戏进程名是否在扫描列表中
- 可能需要手动添加实际的进程名

## 📊 调试输出示例

### 成功检测示例
```
[DEBUG] 开始检测游戏窗口... - By @ConceptualGod
[DEBUG] 找到游戏进程: [('qifan.exe', 1234)] - By @ConceptualGod
[DEBUG] 通过进程找到游戏窗口: '起凡游戏平台' (PID: 1234) - By @ConceptualGod
[DEBUG] 检测到未登录特征: '起凡游戏平台' - By @ConceptualGod
```

### 备用方法示例
```
[DEBUG] 进程方法未找到，使用窗口标题搜索... - By @ConceptualGod
[DEBUG] 找到匹配窗口: '群雄逐鹿大厅' (关键词: 群雄逐鹿) - By @ConceptualGod
[DEBUG] 选择高优先级窗口: '群雄逐鹿大厅' (关键词: 群雄逐鹿) - By @ConceptualGod
[DEBUG] 检测到已登录特征: '群雄逐鹿' - By @ConceptualGod
```

## 🎯 常见问题解决

### 问题1：找不到游戏进程
**现象**：扫描进程显示"未找到明显的游戏相关进程"

**解决方案**：
1. 确认游戏已启动
2. 查看"所有窗口标题"中是否有游戏相关窗口
3. 根据实际进程名添加到`game_process_names`列表

### 问题2：找到进程但窗口检测失败
**现象**：有游戏进程但"当前窗口检测失败"

**解决方案**：
1. 检查游戏窗口是否最小化
2. 确认窗口标题是否包含预期关键词
3. 根据实际窗口标题调整`game_keywords`列表

### 问题3：窗口检测成功但登录状态错误
**现象**：检测到窗口但登录状态判断错误

**解决方案**：
1. 查看实际窗口标题内容
2. 调整`logout_keywords`和`login_keywords`列表
3. 根据实际情况修改判断逻辑

### 问题4：检测不稳定
**现象**：有时能检测到，有时检测不到

**解决方案**：
1. 确保游戏窗口在固定位置
2. 避免其他程序遮挡游戏窗口
3. 检查系统资源使用情况

## 🎉 修正优势

### 全面性
- ✅ **双重检测** - 进程扫描 + 窗口搜索
- ✅ **关键词扩展** - 支持多种可能的窗口标题
- ✅ **智能选择** - 优先级排序选择最佳窗口

### 可靠性
- ✅ **容错机制** - 一种方法失败自动尝试另一种
- ✅ **详细日志** - 完整的调试信息输出
- ✅ **状态判断** - 多特征登录状态判断

### 可调试性
- ✅ **进程扫描** - 一键查看所有相关进程和窗口
- ✅ **实时测试** - 立即测试当前检测逻辑
- ✅ **问题定位** - 快速找到检测失败的原因

---

**修正完成:** 2025-08-05  
**开发者:** @ConceptualGod  
**状态:** 增强窗口检测，新增进程扫描  
**建议:** 立即点击"扫描进程"按钮进行诊断
